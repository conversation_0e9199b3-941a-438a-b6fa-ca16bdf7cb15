---
title: MinioMinio
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# MinioMinio

Base URLs:

# Authentication

# 员工管理

<a id="opIdpageListUsingPOST"></a>

## POST 分页查询员工信息

POST /api/employee/pageList

> Body 请求参数

```json
{
  "page": {
    "countMethod": "Count",
    "currentPage": 0,
    "offset": 0,
    "orderBys": [
      "string"
    ],
    "pageSize": 0,
    "rowCount": 0
  }
}
```

### 请求参数

|名称|位置|类型|必选|中文名|说明|
|---|---|---|---|---|---|
|body|body|[EmployeeParam](#schemaemployeeparam)| 否 | EmployeeParam|none|

> 返回示例

> 200 Response

```
{"code":"string","data":{"page":{"countMethod":"Count","currentPage":0,"offset":0,"orderBys":["string"],"pageSize":0,"rowCount":0},"records":[{"age":0,"createTime":"2019-08-24T14:15:22Z","gender":0,"id":0,"name":"string","updateTime":"2019-08-24T14:15:22Z"}]},"message":"string"}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[CommonResult«PageView«EmployeeVO»»](#schemacommonresult«pageview«employeevo»»)|
|201|[Created](https://tools.ietf.org/html/rfc7231#section-6.3.2)|Created|Inline|
|401|[Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1)|Unauthorized|Inline|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|Inline|
|404|[Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)|Not Found|Inline|

### 返回数据结构

状态码 **200**

*CommonResult«PageView«EmployeeVO»»*

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|string|false|none||none|
|» data|[PageView«EmployeeVO»](#schemapageview«employeevo»)|false|none|PageView«EmployeeVO»|none|
|»» page|[Page](#schemapage)|false|none|Page|none|
|»»» countMethod|string|false|none||none|
|»»» currentPage|integer(int32)|false|none||none|
|»»» offset|integer(int32)|false|none||none|
|»»» orderBys|[string]|false|none||none|
|»»» pageSize|integer(int32)|false|none||none|
|»»» rowCount|integer(int64)|false|none||none|
|»» records|[[EmployeeVO](#schemaemployeevo)]|false|none||none|
|»»» EmployeeVO|[EmployeeVO](#schemaemployeevo)|false|none|EmployeeVO|none|
|»»»» age|integer(int32)|false|none||员工年龄|
|»»»» createTime|string(date-time)|false|none||员工创建时间|
|»»»» gender|integer(int32)|false|none||员工性别:0-女 1-男|
|»»»» id|integer(int32)|false|none||员工id|
|»»»» name|string|false|none||员工姓名|
|»»»» updateTime|string(date-time)|false|none||员工更新时间|
|» message|string|false|none||none|

#### 枚举值

|属性|值|
|---|---|
|countMethod|Count|
|countMethod|NextPage|

# 数据模型

<h2 id="tocS_CommonResult«PageView«EmployeeVO»»">CommonResult«PageView«EmployeeVO»»</h2>

<a id="schemacommonresult«pageview«employeevo»»"></a>
<a id="schema_CommonResult«PageView«EmployeeVO»»"></a>
<a id="tocScommonresult«pageview«employeevo»»"></a>
<a id="tocscommonresult«pageview«employeevo»»"></a>

```json
{
  "code": "string",
  "data": {
    "page": {
      "countMethod": "Count",
      "currentPage": 0,
      "offset": 0,
      "orderBys": [
        "string"
      ],
      "pageSize": 0,
      "rowCount": 0
    },
    "records": [
      {
        "age": 0,
        "createTime": "2019-08-24T14:15:22Z",
        "gender": 0,
        "id": 0,
        "name": "string",
        "updateTime": "2019-08-24T14:15:22Z"
      }
    ]
  },
  "message": "string"
}

```

CommonResult«PageView«EmployeeVO»»

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|string|false|none||none|
|data|[PageView«EmployeeVO»](#schemapageview«employeevo»)|false|none||none|
|message|string|false|none||none|

<h2 id="tocS_PageView«EmployeeVO»">PageView«EmployeeVO»</h2>

<a id="schemapageview«employeevo»"></a>
<a id="schema_PageView«EmployeeVO»"></a>
<a id="tocSpageview«employeevo»"></a>
<a id="tocspageview«employeevo»"></a>

```json
{
  "page": {
    "countMethod": "Count",
    "currentPage": 0,
    "offset": 0,
    "orderBys": [
      "string"
    ],
    "pageSize": 0,
    "rowCount": 0
  },
  "records": [
    {
      "age": 0,
      "createTime": "2019-08-24T14:15:22Z",
      "gender": 0,
      "id": 0,
      "name": "string",
      "updateTime": "2019-08-24T14:15:22Z"
    }
  ]
}

```

PageView«EmployeeVO»

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|page|[Page](#schemapage)|false|none||none|
|records|[[EmployeeVO](#schemaemployeevo)]|false|none||none|

<h2 id="tocS_EmployeeVO">EmployeeVO</h2>

<a id="schemaemployeevo"></a>
<a id="schema_EmployeeVO"></a>
<a id="tocSemployeevo"></a>
<a id="tocsemployeevo"></a>

```json
{
  "age": 0,
  "createTime": "2019-08-24T14:15:22Z",
  "gender": 0,
  "id": 0,
  "name": "string",
  "updateTime": "2019-08-24T14:15:22Z"
}

```

EmployeeVO

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|age|integer(int32)|false|none||员工年龄|
|createTime|string(date-time)|false|none||员工创建时间|
|gender|integer(int32)|false|none||员工性别:0-女 1-男|
|id|integer(int32)|false|none||员工id|
|name|string|false|none||员工姓名|
|updateTime|string(date-time)|false|none||员工更新时间|

<h2 id="tocS_Page">Page</h2>

<a id="schemapage"></a>
<a id="schema_Page"></a>
<a id="tocSpage"></a>
<a id="tocspage"></a>

```json
{
  "countMethod": "Count",
  "currentPage": 0,
  "offset": 0,
  "orderBys": [
    "string"
  ],
  "pageSize": 0,
  "rowCount": 0
}

```

Page

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|countMethod|string|false|none||none|
|currentPage|integer(int32)|false|none||none|
|offset|integer(int32)|false|none||none|
|orderBys|[string]|false|none||none|
|pageSize|integer(int32)|false|none||none|
|rowCount|integer(int64)|false|none||none|

#### 枚举值

|属性|值|
|---|---|
|countMethod|Count|
|countMethod|NextPage|

<h2 id="tocS_EmployeeParam">EmployeeParam</h2>

<a id="schemaemployeeparam"></a>
<a id="schema_EmployeeParam"></a>
<a id="tocSemployeeparam"></a>
<a id="tocsemployeeparam"></a>

```json
{
  "page": {
    "countMethod": "Count",
    "currentPage": 0,
    "offset": 0,
    "orderBys": [
      "string"
    ],
    "pageSize": 0,
    "rowCount": 0
  }
}

```

EmployeeParam

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|page|[Page](#schemapage)|false|none||none|

