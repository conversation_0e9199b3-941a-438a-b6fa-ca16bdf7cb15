-- =============================================
-- 实时聊天系统数据库设计
-- =============================================

-- 1. 用户表 (已存在，这里展示相关字段)
-- CREATE TABLE users (
--     id BIGINT PRIMARY KEY AUTO_INCREMENT,
--     username VARCHAR(50) NOT NULL UNIQUE,
--     nickname VARCHAR(100),
--     avatar_url VARCHAR(255),
--     email VARCHAR(100),
--     phone VARCHAR(20),
--     status TINYINT DEFAULT 1 COMMENT '1:正常 0:禁用',
--     create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
--     update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
-- );

-- 2. 客服人员表
CREATE TABLE customer_service_agents (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '关联用户表ID',
    agent_name VARCHAR(100) NOT NULL COMMENT '客服姓名',
    agent_code VARCHAR(50) NOT NULL UNIQUE COMMENT '客服工号',
    department VARCHAR(100) COMMENT '所属部门',
    skill_tags JSON COMMENT '技能标签，如["技术支持","账户问题"]',
    max_concurrent_chats INT DEFAULT 5 COMMENT '最大并发聊天数',
    status ENUM('online', 'busy', 'offline', 'break') DEFAULT 'offline' COMMENT '客服状态',
    last_active_time DATETIME COMMENT '最后活跃时间',
    total_chats INT DEFAULT 0 COMMENT '总聊天次数',
    avg_response_time INT DEFAULT 0 COMMENT '平均响应时间(秒)',
    satisfaction_score DECIMAL(3,2) DEFAULT 0.00 COMMENT '满意度评分',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id),
    INDEX idx_agent_code (agent_code),
    INDEX idx_status (status),
    INDEX idx_department (department)
);

-- 3. 聊天会话表
CREATE TABLE chat_sessions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    session_id VARCHAR(64) NOT NULL UNIQUE COMMENT '会话唯一标识',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    agent_id BIGINT COMMENT '分配的客服ID',
    session_type ENUM('manual', 'auto', 'bot') DEFAULT 'auto' COMMENT '会话类型：人工、自动分配、机器人',
    status ENUM('waiting', 'active', 'closed', 'transferred') DEFAULT 'waiting' COMMENT '会话状态',
    priority ENUM('low', 'normal', 'high', 'urgent') DEFAULT 'normal' COMMENT '优先级',
    source ENUM('web', 'mobile', 'api') DEFAULT 'web' COMMENT '来源渠道',
    subject VARCHAR(200) COMMENT '会话主题',
    tags JSON COMMENT '标签，如["技术问题","账户问题"]',
    user_ip VARCHAR(45) COMMENT '用户IP地址',
    user_agent TEXT COMMENT '用户浏览器信息',
    start_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '会话开始时间',
    end_time DATETIME COMMENT '会话结束时间',
    last_message_time DATETIME COMMENT '最后消息时间',
    message_count INT DEFAULT 0 COMMENT '消息总数',
    satisfaction_rating TINYINT COMMENT '满意度评分 1-5',
    satisfaction_comment TEXT COMMENT '满意度评价',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (agent_id) REFERENCES customer_service_agents(id),
    INDEX idx_session_id (session_id),
    INDEX idx_user_id (user_id),
    INDEX idx_agent_id (agent_id),
    INDEX idx_status (status),
    INDEX idx_start_time (start_time),
    INDEX idx_last_message_time (last_message_time)
);

-- 4. 聊天消息表
CREATE TABLE chat_messages (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    message_id VARCHAR(64) NOT NULL UNIQUE COMMENT '消息唯一标识',
    session_id BIGINT NOT NULL COMMENT '会话ID',
    sender_type ENUM('user', 'agent', 'system', 'bot') NOT NULL COMMENT '发送者类型',
    sender_id BIGINT COMMENT '发送者ID',
    message_type ENUM('text', 'image', 'file', 'voice', 'video', 'system') DEFAULT 'text' COMMENT '消息类型',
    content TEXT NOT NULL COMMENT '消息内容',
    file_url VARCHAR(500) COMMENT '文件URL（如果是文件消息）',
    file_name VARCHAR(255) COMMENT '文件名',
    file_size BIGINT COMMENT '文件大小（字节）',
    file_type VARCHAR(50) COMMENT '文件MIME类型',
    reply_to_message_id BIGINT COMMENT '回复的消息ID',
    is_read BOOLEAN DEFAULT FALSE COMMENT '是否已读',
    read_time DATETIME COMMENT '阅读时间',
    is_deleted BOOLEAN DEFAULT FALSE COMMENT '是否已删除',
    send_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '发送时间',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (session_id) REFERENCES chat_sessions(id),
    FOREIGN KEY (reply_to_message_id) REFERENCES chat_messages(id),
    INDEX idx_message_id (message_id),
    INDEX idx_session_id (session_id),
    INDEX idx_sender (sender_type, sender_id),
    INDEX idx_send_time (send_time),
    INDEX idx_is_read (is_read)
);

-- 5. 客服工作状态记录表
CREATE TABLE agent_work_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    agent_id BIGINT NOT NULL,
    status ENUM('online', 'busy', 'offline', 'break') NOT NULL,
    start_time DATETIME NOT NULL,
    end_time DATETIME,
    duration_minutes INT COMMENT '持续时间（分钟）',
    concurrent_chats INT DEFAULT 0 COMMENT '并发聊天数',
    handled_messages INT DEFAULT 0 COMMENT '处理消息数',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (agent_id) REFERENCES customer_service_agents(id),
    INDEX idx_agent_id (agent_id),
    INDEX idx_start_time (start_time),
    INDEX idx_status (status)
);

-- 6. 自动回复规则表
CREATE TABLE auto_reply_rules (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    rule_name VARCHAR(100) NOT NULL COMMENT '规则名称',
    keywords JSON NOT NULL COMMENT '关键词列表',
    reply_content TEXT NOT NULL COMMENT '回复内容',
    rule_type ENUM('exact', 'contains', 'regex') DEFAULT 'contains' COMMENT '匹配类型',
    priority INT DEFAULT 0 COMMENT '优先级，数字越大优先级越高',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    usage_count INT DEFAULT 0 COMMENT '使用次数',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_priority (priority),
    INDEX idx_is_active (is_active)
);

-- 7. 聊天会话分配记录表
CREATE TABLE session_assignments (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    session_id BIGINT NOT NULL,
    from_agent_id BIGINT COMMENT '转移前客服ID',
    to_agent_id BIGINT NOT NULL COMMENT '分配给的客服ID',
    assignment_type ENUM('auto', 'manual', 'transfer') NOT NULL COMMENT '分配类型',
    assignment_reason VARCHAR(200) COMMENT '分配原因',
    assignment_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (session_id) REFERENCES chat_sessions(id),
    FOREIGN KEY (from_agent_id) REFERENCES customer_service_agents(id),
    FOREIGN KEY (to_agent_id) REFERENCES customer_service_agents(id),
    INDEX idx_session_id (session_id),
    INDEX idx_to_agent_id (to_agent_id),
    INDEX idx_assignment_time (assignment_time)
);

-- 8. 系统配置表
CREATE TABLE chat_system_config (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    config_key VARCHAR(100) NOT NULL UNIQUE,
    config_value TEXT NOT NULL,
    config_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
    description VARCHAR(500),
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_config_key (config_key)
);

-- 插入一些默认配置
INSERT INTO chat_system_config (config_key, config_value, config_type, description) VALUES
('max_wait_time', '300', 'number', '用户最大等待时间（秒）'),
('auto_close_time', '1800', 'number', '无活动自动关闭会话时间（秒）'),
('max_file_size', '10485760', 'number', '最大文件上传大小（字节）'),
('allowed_file_types', '["jpg","jpeg","png","gif","pdf","doc","docx","txt"]', 'json', '允许的文件类型'),
('welcome_message', '您好！欢迎使用在线客服，有什么可以帮助您的吗？', 'string', '欢迎消息'),
('offline_message', '客服暂时离线，请留言，我们会尽快回复您。', 'string', '离线消息'),
('queue_message', '当前客服繁忙，您在队列中的位置是第{position}位，预计等待时间{time}分钟。', 'string', '排队消息模板');
