# 聊天系统 RESTful API 设计

## 1. 基础响应格式

```json
{
    "code": "200",
    "message": "success",
    "data": {},
    "timestamp": 1640995200000
}
```

## 2. 用户端API接口

### 2.1 会话管理

#### 创建聊天会话
```
POST /api/chat/session/create
Content-Type: application/json
Authorization: Bearer {token}

Request Body:
{
    "subject": "技术支持咨询",
    "priority": "normal",
    "tags": ["技术问题", "登录问题"]
}

Response:
{
    "code": "200",
    "message": "会话创建成功",
    "data": {
        "sessionId": "uuid-session-id",
        "status": "waiting",
        "queuePosition": 3,
        "estimatedWaitTime": 180,
        "welcomeMessage": "您好！欢迎使用在线客服..."
    }
}
```

#### 获取会话信息
```
GET /api/chat/session/{sessionId}
Authorization: Bearer {token}

Response:
{
    "code": "200",
    "data": {
        "sessionId": "uuid-session-id",
        "status": "active",
        "agentInfo": {
            "agentId": 123,
            "agentName": "客服小王",
            "avatar": "avatar_url"
        },
        "startTime": "2024-01-01T10:00:00Z",
        "lastMessageTime": "2024-01-01T10:30:00Z"
    }
}
```

#### 结束会话
```
POST /api/chat/session/{sessionId}/close
Authorization: Bearer {token}

Request Body:
{
    "reason": "问题已解决",
    "satisfaction": {
        "rating": 5,
        "comment": "服务很好"
    }
}

Response:
{
    "code": "200",
    "message": "会话已结束"
}
```

### 2.2 消息管理

#### 获取历史消息
```
GET /api/chat/session/{sessionId}/messages?page=1&size=20&before={messageId}
Authorization: Bearer {token}

Response:
{
    "code": "200",
    "data": {
        "messages": [
            {
                "messageId": "uuid-message-id",
                "senderType": "agent",
                "senderId": 123,
                "senderName": "客服小王",
                "messageType": "text",
                "content": "您好，有什么可以帮助您的吗？",
                "sendTime": "2024-01-01T10:00:00Z",
                "isRead": true
            }
        ],
        "hasMore": true,
        "total": 50
    }
}
```

#### 发送消息 (HTTP备用接口)
```
POST /api/chat/session/{sessionId}/message
Authorization: Bearer {token}
Content-Type: application/json

Request Body:
{
    "messageType": "text",
    "content": "我的账户登录不了"
}

Response:
{
    "code": "200",
    "data": {
        "messageId": "uuid-message-id",
        "sendTime": "2024-01-01T10:00:00Z"
    }
}
```

#### 标记消息已读
```
POST /api/chat/session/{sessionId}/messages/read
Authorization: Bearer {token}

Request Body:
{
    "messageIds": ["uuid-1", "uuid-2"]
}

Response:
{
    "code": "200",
    "message": "消息已标记为已读"
}
```

### 2.3 文件上传

#### 上传聊天文件
```
POST /api/chat/upload
Authorization: Bearer {token}
Content-Type: multipart/form-data

Form Data:
- file: 文件内容
- sessionId: 会话ID
- messageType: image|file

Response:
{
    "code": "200",
    "data": {
        "fileUrl": "https://example.com/files/xxx.jpg",
        "fileName": "screenshot.jpg",
        "fileSize": 1024000,
        "fileType": "image/jpeg"
    }
}
```

### 2.4 用户会话历史

#### 获取用户会话列表
```
GET /api/chat/sessions?page=1&size=10&status=all
Authorization: Bearer {token}

Response:
{
    "code": "200",
    "data": {
        "sessions": [
            {
                "sessionId": "uuid-session-id",
                "subject": "技术支持咨询",
                "status": "closed",
                "agentName": "客服小王",
                "startTime": "2024-01-01T10:00:00Z",
                "endTime": "2024-01-01T11:00:00Z",
                "messageCount": 15,
                "lastMessage": "问题已解决，感谢您的帮助"
            }
        ],
        "total": 25,
        "hasMore": true
    }
}
```

## 3. 客服端API接口

### 3.1 客服状态管理

#### 更新客服状态
```
POST /api/agent/status
Authorization: Bearer {agent_token}

Request Body:
{
    "status": "online|busy|offline|break",
    "maxConcurrentChats": 5
}

Response:
{
    "code": "200",
    "message": "状态更新成功"
}
```

#### 获取客服工作台信息
```
GET /api/agent/dashboard
Authorization: Bearer {agent_token}

Response:
{
    "code": "200",
    "data": {
        "agentInfo": {
            "agentId": 123,
            "agentName": "客服小王",
            "status": "online",
            "currentChats": 3,
            "maxConcurrentChats": 5
        },
        "todayStats": {
            "handledSessions": 15,
            "avgResponseTime": 45,
            "satisfactionScore": 4.8
        },
        "waitingQueue": {
            "count": 5,
            "avgWaitTime": 120
        }
    }
}
```

### 3.2 会话分配和管理

#### 获取分配的会话列表
```
GET /api/agent/sessions?status=active&page=1&size=10
Authorization: Bearer {agent_token}

Response:
{
    "code": "200",
    "data": {
        "sessions": [
            {
                "sessionId": "uuid-session-id",
                "userInfo": {
                    "userId": 456,
                    "userName": "张三",
                    "avatar": "avatar_url"
                },
                "subject": "技术支持咨询",
                "priority": "normal",
                "status": "active",
                "startTime": "2024-01-01T10:00:00Z",
                "lastMessageTime": "2024-01-01T10:30:00Z",
                "unreadCount": 2
            }
        ]
    }
}
```

#### 接受会话分配
```
POST /api/agent/session/{sessionId}/accept
Authorization: Bearer {agent_token}

Response:
{
    "code": "200",
    "message": "会话已接受"
}
```

#### 转移会话
```
POST /api/agent/session/{sessionId}/transfer
Authorization: Bearer {agent_token}

Request Body:
{
    "toAgentId": 789,
    "reason": "专业技能匹配"
}

Response:
{
    "code": "200",
    "message": "会话转移成功"
}
```

### 3.3 快捷回复和模板

#### 获取快捷回复模板
```
GET /api/agent/quick-replies?category=greeting
Authorization: Bearer {agent_token}

Response:
{
    "code": "200",
    "data": {
        "templates": [
            {
                "id": 1,
                "title": "问候语",
                "content": "您好！我是客服{agentName}，很高兴为您服务！",
                "category": "greeting",
                "usageCount": 150
            }
        ]
    }
}
```

## 4. 管理端API接口

### 4.1 客服管理

#### 获取客服列表
```
GET /api/admin/agents?page=1&size=20&status=all&department=技术支持
Authorization: Bearer {admin_token}

Response:
{
    "code": "200",
    "data": {
        "agents": [
            {
                "agentId": 123,
                "agentName": "客服小王",
                "agentCode": "CS001",
                "department": "技术支持",
                "status": "online",
                "currentChats": 3,
                "todayStats": {
                    "handledSessions": 15,
                    "avgResponseTime": 45,
                    "satisfactionScore": 4.8
                }
            }
        ]
    }
}
```

#### 创建客服账户
```
POST /api/admin/agents
Authorization: Bearer {admin_token}

Request Body:
{
    "userId": 789,
    "agentName": "客服小李",
    "agentCode": "CS002",
    "department": "技术支持",
    "skillTags": ["技术支持", "账户问题"],
    "maxConcurrentChats": 5
}

Response:
{
    "code": "200",
    "message": "客服账户创建成功",
    "data": {
        "agentId": 124
    }
}
```

### 4.2 数据统计

#### 获取聊天统计数据
```
GET /api/admin/statistics/chat?startDate=2024-01-01&endDate=2024-01-31&granularity=day
Authorization: Bearer {admin_token}

Response:
{
    "code": "200",
    "data": {
        "overview": {
            "totalSessions": 1500,
            "avgWaitTime": 120,
            "avgSessionDuration": 900,
            "satisfactionScore": 4.6,
            "resolutionRate": 0.85
        },
        "dailyStats": [
            {
                "date": "2024-01-01",
                "sessions": 50,
                "avgWaitTime": 90,
                "satisfactionScore": 4.8
            }
        ]
    }
}
```

### 4.3 系统配置

#### 获取系统配置
```
GET /api/admin/config
Authorization: Bearer {admin_token}

Response:
{
    "code": "200",
    "data": {
        "maxWaitTime": 300,
        "autoCloseTime": 1800,
        "maxFileSize": 10485760,
        "allowedFileTypes": ["jpg", "png", "pdf"],
        "welcomeMessage": "您好！欢迎使用在线客服..."
    }
}
```

#### 更新系统配置
```
PUT /api/admin/config
Authorization: Bearer {admin_token}

Request Body:
{
    "maxWaitTime": 600,
    "welcomeMessage": "欢迎使用我们的在线客服系统！"
}

Response:
{
    "code": "200",
    "message": "配置更新成功"
}
```

## 5. 错误码定义

```
200: 成功
400: 请求参数错误
401: 未授权
403: 权限不足
404: 资源不存在
429: 请求过于频繁
500: 服务器内部错误

业务错误码:
10001: 会话不存在
10002: 会话已结束
10003: 客服不在线
10004: 文件类型不支持
10005: 文件大小超限
10006: 消息发送失败
10007: 会话分配失败
```

这套API设计涵盖了聊天系统的所有核心功能，支持用户端、客服端和管理端的完整业务流程。
