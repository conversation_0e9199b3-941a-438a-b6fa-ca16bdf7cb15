package com.yqs.springbootminio.websocket;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import com.yqs.springbootminio.model.User;
import com.yqs.springbootminio.service.UserService;
import com.yqs.springbootminio.util.JwtTokenUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.simp.stomp.StompCommand;
import org.springframework.messaging.simp.stomp.StompHeaderAccessor;
import org.springframework.messaging.support.ChannelInterceptor;
import org.springframework.messaging.support.MessageHeaderAccessor;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.security.Principal;

/**
 * WebSocket认证拦截器
 *
 * <AUTHOR>
 * @since 2025-06-24
 */
@Slf4j
@Component
public class WebSocketAuthInterceptor implements ChannelInterceptor {

    @Autowired
    private JwtTokenUtil jwtTokenUtil;

    @Autowired
    private UserService userService;

    @Override
    public Message<?> preSend(Message<?> message, MessageChannel channel) {
        StompHeaderAccessor accessor = MessageHeaderAccessor.getAccessor(message, StompHeaderAccessor.class);
        
        if (StompCommand.CONNECT.equals(accessor.getCommand())) {
            // 获取token
            String token = accessor.getFirstNativeHeader("Authorization");
//            if (token != null && token.startsWith("Bearer ")) {
//                token = token.substring(7);
//            }
            
            if (CharSequenceUtil.isNotBlank(token)) {
                try {
                    // 验证token
                    Long userId = jwtTokenUtil.getUserIdFromToken(token);
                    if (userId != null && jwtTokenUtil.validateTokenCheckExpire(token, userId)) {
                        // 获取用户信息
                        User user = userService.queryById(userId);
                        if (user != null) {
                            // 设置用户信息到WebSocket会话中
                            accessor.setUser(new WebSocketUserPrincipal(user));
                            log.info("WebSocket连接认证成功，用户ID: {}, 用户名: {}", user.getId(), user.getUserName());
                        } else {
                            log.warn("WebSocket连接认证失败，用户不存在，用户ID: {}", userId);
                            return null;
                        }
                    } else {
                        log.warn("WebSocket连接认证失败，token无效: {}", token);
                        return null;
                    }
                } catch (Exception e) {
                    log.error("WebSocket连接认证异常: {}", e.getMessage());
                    return null;
                }
            } else {
                log.warn("WebSocket连接认证失败，未提供token");
                return null;
            }
        }
        
        return message;
    }

    /**
     * WebSocket用户主体
     */
    public static class WebSocketUserPrincipal implements Principal {
        private final User user;

        public WebSocketUserPrincipal(User user) {
            this.user = user;
        }

        @Override
        public String getName() {
            return user.getId().toString();
        }

        public User getUser() {
            return user;
        }
    }
}
