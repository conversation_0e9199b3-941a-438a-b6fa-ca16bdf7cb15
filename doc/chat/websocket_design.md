# WebSocket实时通信设计方案

## 1. 整体架构

```
用户端 <--WebSocket--> Spring Boot服务器 <--WebSocket--> 客服端
                            |
                        消息队列(Redis)
                            |
                        数据库(MySQL)
```

## 2. Spring Boot WebSocket配置

### 2.1 依赖配置 (pom.xml)
```xml
<dependencies>
    <!-- WebSocket支持 -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-websocket</artifactId>
    </dependency>
    
    <!-- Redis支持 -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-redis</artifactId>
    </dependency>
    
    <!-- JSON处理 -->
    <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-databind</artifactId>
    </dependency>
</dependencies>
```

### 2.2 WebSocket配置类
```java
@Configuration
@EnableWebSocket
public class WebSocketConfig implements WebSocketConfigurer {
    
    @Autowired
    private ChatWebSocketHandler chatWebSocketHandler;
    
    @Autowired
    private WebSocketInterceptor webSocketInterceptor;
    
    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        registry.addHandler(chatWebSocketHandler, "/ws/chat")
                .addInterceptors(webSocketInterceptor)
                .setAllowedOrigins("*"); // 生产环境需要配置具体域名
    }
}
```

## 3. 消息协议设计

### 3.1 消息格式
```json
{
    "type": "message_type",
    "sessionId": "session_uuid",
    "messageId": "message_uuid", 
    "senderId": "sender_id",
    "senderType": "user|agent|system|bot",
    "content": "消息内容",
    "messageType": "text|image|file|voice|video|system",
    "timestamp": 1640995200000,
    "extra": {
        "fileName": "文件名",
        "fileSize": 1024,
        "fileUrl": "文件URL"
    }
}
```

### 3.2 消息类型定义
```java
public enum MessageType {
    // 连接相关
    CONNECT("connect"),           // 建立连接
    DISCONNECT("disconnect"),     // 断开连接
    HEARTBEAT("heartbeat"),       // 心跳检测
    
    // 会话相关
    SESSION_START("session_start"),     // 开始会话
    SESSION_END("session_end"),         // 结束会话
    SESSION_TRANSFER("session_transfer"), // 会话转移
    
    // 消息相关
    CHAT_MESSAGE("chat_message"),       // 聊天消息
    TYPING_START("typing_start"),       // 开始输入
    TYPING_END("typing_end"),           // 结束输入
    MESSAGE_READ("message_read"),       // 消息已读
    
    // 状态相关
    AGENT_STATUS("agent_status"),       // 客服状态变更
    QUEUE_UPDATE("queue_update"),       // 队列状态更新
    
    // 系统相关
    SYSTEM_NOTICE("system_notice"),     // 系统通知
    ERROR("error");                     // 错误消息
}
```

## 4. WebSocket处理器实现

### 4.1 主要处理器
```java
@Component
public class ChatWebSocketHandler extends TextWebSocketHandler {
    
    @Autowired
    private ChatSessionService chatSessionService;
    
    @Autowired
    private MessageService messageService;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    // 存储WebSocket会话
    private final Map<String, WebSocketSession> sessions = new ConcurrentHashMap<>();
    
    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        String userId = getUserId(session);
        String sessionKey = "ws_session:" + userId;
        
        sessions.put(sessionKey, session);
        redisTemplate.opsForValue().set(sessionKey, session.getId(), Duration.ofHours(24));
        
        // 发送连接成功消息
        sendMessage(session, createSystemMessage("连接成功"));
        
        // 检查是否有未完成的会话
        checkPendingSession(userId, session);
    }
    
    @Override
    protected void handleTextMessage(WebSocketSession session, TextMessage message) throws Exception {
        try {
            ChatMessage chatMessage = parseMessage(message.getPayload());
            
            switch (chatMessage.getType()) {
                case CHAT_MESSAGE:
                    handleChatMessage(session, chatMessage);
                    break;
                case TYPING_START:
                    handleTypingStatus(session, chatMessage, true);
                    break;
                case TYPING_END:
                    handleTypingStatus(session, chatMessage, false);
                    break;
                case HEARTBEAT:
                    handleHeartbeat(session);
                    break;
                default:
                    handleUnknownMessage(session, chatMessage);
            }
        } catch (Exception e) {
            sendErrorMessage(session, "消息处理失败: " + e.getMessage());
        }
    }
    
    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus status) throws Exception {
        String userId = getUserId(session);
        String sessionKey = "ws_session:" + userId;
        
        sessions.remove(sessionKey);
        redisTemplate.delete(sessionKey);
        
        // 更新用户离线状态
        updateUserOfflineStatus(userId);
    }
    
    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
        logger.error("WebSocket传输错误", exception);
        session.close();
    }
}
```

## 5. 消息路由和分发

### 5.1 消息路由服务
```java
@Service
public class MessageRoutingService {
    
    @Autowired
    private ChatWebSocketHandler webSocketHandler;
    
    @Autowired
    private AgentAssignmentService agentAssignmentService;
    
    public void routeMessage(ChatMessage message) {
        switch (message.getSenderType()) {
            case USER:
                routeUserMessage(message);
                break;
            case AGENT:
                routeAgentMessage(message);
                break;
            case SYSTEM:
                routeSystemMessage(message);
                break;
        }
    }
    
    private void routeUserMessage(ChatMessage message) {
        // 1. 保存消息到数据库
        messageService.saveMessage(message);
        
        // 2. 查找分配的客服
        Long agentId = agentAssignmentService.getAssignedAgent(message.getSessionId());
        
        if (agentId != null) {
            // 发送给指定客服
            webSocketHandler.sendToAgent(agentId, message);
        } else {
            // 自动分配客服或加入队列
            agentAssignmentService.assignAgent(message.getSessionId());
        }
        
        // 3. 检查是否需要自动回复
        checkAutoReply(message);
    }
    
    private void routeAgentMessage(ChatMessage message) {
        // 1. 保存消息到数据库
        messageService.saveMessage(message);
        
        // 2. 发送给用户
        Long userId = chatSessionService.getUserId(message.getSessionId());
        webSocketHandler.sendToUser(userId, message);
        
        // 3. 更新会话活跃时间
        chatSessionService.updateLastMessageTime(message.getSessionId());
    }
}
```

## 6. 客服分配策略

### 6.1 智能分配算法
```java
@Service
public class AgentAssignmentService {
    
    public Long assignAgent(Long sessionId) {
        // 1. 获取在线客服列表
        List<CustomerServiceAgent> onlineAgents = getOnlineAgents();
        
        if (onlineAgents.isEmpty()) {
            // 没有在线客服，加入等待队列
            addToWaitingQueue(sessionId);
            return null;
        }
        
        // 2. 根据负载均衡选择客服
        CustomerServiceAgent selectedAgent = selectBestAgent(onlineAgents);
        
        // 3. 分配会话
        assignSessionToAgent(sessionId, selectedAgent.getId());
        
        return selectedAgent.getId();
    }
    
    private CustomerServiceAgent selectBestAgent(List<CustomerServiceAgent> agents) {
        return agents.stream()
                .filter(agent -> agent.getCurrentChats() < agent.getMaxConcurrentChats())
                .min(Comparator.comparing(CustomerServiceAgent::getCurrentChats))
                .orElse(null);
    }
}
```

## 7. 消息持久化和缓存

### 7.1 Redis缓存策略
```java
@Service
public class MessageCacheService {
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    // 缓存最近的聊天消息（用于快速加载）
    public void cacheRecentMessages(Long sessionId, List<ChatMessage> messages) {
        String key = "chat_messages:" + sessionId;
        redisTemplate.opsForList().rightPushAll(key, messages);
        redisTemplate.expire(key, Duration.ofDays(7));
    }
    
    // 缓存用户会话状态
    public void cacheSessionStatus(Long sessionId, SessionStatus status) {
        String key = "session_status:" + sessionId;
        redisTemplate.opsForValue().set(key, status, Duration.ofHours(24));
    }
    
    // 缓存客服在线状态
    public void cacheAgentStatus(Long agentId, AgentStatus status) {
        String key = "agent_status:" + agentId;
        redisTemplate.opsForValue().set(key, status, Duration.ofHours(24));
    }
}
```

## 8. 心跳检测和断线重连

### 8.1 心跳机制
```java
@Component
public class HeartbeatService {
    
    @Scheduled(fixedRate = 30000) // 30秒检查一次
    public void checkHeartbeat() {
        // 检查所有WebSocket连接的心跳状态
        // 超时的连接将被关闭
    }
    
    public void handleHeartbeat(WebSocketSession session) {
        String userId = getUserId(session);
        String key = "heartbeat:" + userId;
        redisTemplate.opsForValue().set(key, System.currentTimeMillis(), Duration.ofMinutes(2));
    }
}
```

## 9. 安全和权限控制

### 9.1 WebSocket拦截器
```java
@Component
public class WebSocketInterceptor implements HandshakeInterceptor {
    
    @Override
    public boolean beforeHandshake(ServerHttpRequest request, ServerHttpResponse response,
                                 WebSocketHandler wsHandler, Map<String, Object> attributes) throws Exception {
        
        // 1. 验证JWT Token
        String token = extractToken(request);
        if (!jwtTokenUtil.validateToken(token)) {
            return false;
        }
        
        // 2. 获取用户信息
        String userId = jwtTokenUtil.getUserIdFromToken(token);
        attributes.put("userId", userId);
        
        // 3. 检查用户权限
        if (!hasPermission(userId)) {
            return false;
        }
        
        return true;
    }
}
```

这个设计方案提供了完整的实时聊天基础架构，支持：
- 实时消息传输
- 智能客服分配
- 消息持久化
- 断线重连
- 负载均衡
- 安全认证

接下来我将继续设计RESTful API接口部分。
