# Spring Boot 实时聊天系统实现示例

## 1. 项目结构

```
src/main/java/com/example/chat/
├── config/
│   ├── WebSocketConfig.java
│   └── RedisConfig.java
├── controller/
│   ├── ChatController.java
│   ├── AgentController.java
│   └── AdminController.java
├── handler/
│   ├── ChatWebSocketHandler.java
│   └── WebSocketInterceptor.java
├── service/
│   ├── ChatSessionService.java
│   ├── MessageService.java
│   ├── AgentService.java
│   └── MessageRoutingService.java
├── entity/
│   ├── ChatSession.java
│   ├── ChatMessage.java
│   ├── CustomerServiceAgent.java
│   └── User.java
├── dto/
│   ├── ChatMessageDTO.java
│   ├── SessionDTO.java
│   └── AgentStatusDTO.java
└── enums/
    ├── MessageType.java
    ├── SessionStatus.java
    └── AgentStatus.java
```

## 2. 核心实现代码

### 2.1 WebSocket配置

```java
@Configuration
@EnableWebSocket
public class WebSocketConfig implements WebSocketConfigurer {
    
    @Autowired
    private ChatWebSocketHandler chatWebSocketHandler;
    
    @Autowired
    private WebSocketInterceptor webSocketInterceptor;
    
    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        registry.addHandler(chatWebSocketHandler, "/ws/chat")
                .addInterceptors(webSocketInterceptor)
                .setAllowedOriginPatterns("*")
                .withSockJS();
    }
}
```

### 2.2 WebSocket处理器

```java
@Component
@Slf4j
public class ChatWebSocketHandler extends TextWebSocketHandler {
    
    @Autowired
    private ChatSessionService chatSessionService;
    
    @Autowired
    private MessageService messageService;
    
    @Autowired
    private MessageRoutingService messageRoutingService;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    // 存储WebSocket会话 - 用户ID -> WebSocketSession
    private final Map<String, WebSocketSession> userSessions = new ConcurrentHashMap<>();
    // 存储WebSocket会话 - 客服ID -> WebSocketSession  
    private final Map<String, WebSocketSession> agentSessions = new ConcurrentHashMap<>();
    
    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        String userId = getUserId(session);
        String userType = getUserType(session);
        
        if ("user".equals(userType)) {
            userSessions.put(userId, session);
            log.info("用户 {} 建立WebSocket连接", userId);
        } else if ("agent".equals(userType)) {
            agentSessions.put(userId, session);
            log.info("客服 {} 建立WebSocket连接", userId);
        }
        
        // 缓存会话信息
        String sessionKey = "ws_session:" + userType + ":" + userId;
        redisTemplate.opsForValue().set(sessionKey, session.getId(), Duration.ofHours(24));
        
        // 发送连接成功消息
        sendMessage(session, createSystemMessage("连接成功"));
    }
    
    @Override
    protected void handleTextMessage(WebSocketSession session, TextMessage message) throws Exception {
        try {
            ChatMessageDTO messageDTO = JSON.parseObject(message.getPayload(), ChatMessageDTO.class);
            
            switch (MessageType.valueOf(messageDTO.getType().toUpperCase())) {
                case SESSION_START:
                    handleSessionStart(session, messageDTO);
                    break;
                case CHAT_MESSAGE:
                    handleChatMessage(session, messageDTO);
                    break;
                case TYPING_START:
                    handleTypingStatus(session, messageDTO, true);
                    break;
                case TYPING_END:
                    handleTypingStatus(session, messageDTO, false);
                    break;
                case HEARTBEAT:
                    handleHeartbeat(session);
                    break;
                case SESSION_END:
                    handleSessionEnd(session, messageDTO);
                    break;
                default:
                    log.warn("未知消息类型: {}", messageDTO.getType());
            }
        } catch (Exception e) {
            log.error("处理WebSocket消息失败", e);
            sendErrorMessage(session, "消息处理失败: " + e.getMessage());
        }
    }
    
    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus status) throws Exception {
        String userId = getUserId(session);
        String userType = getUserType(session);
        
        if ("user".equals(userType)) {
            userSessions.remove(userId);
            log.info("用户 {} 断开WebSocket连接", userId);
        } else if ("agent".equals(userType)) {
            agentSessions.remove(userId);
            log.info("客服 {} 断开WebSocket连接", userId);
        }
        
        // 清理缓存
        String sessionKey = "ws_session:" + userType + ":" + userId;
        redisTemplate.delete(sessionKey);
        
        // 更新离线状态
        updateOfflineStatus(userId, userType);
    }
    
    // 处理会话开始
    private void handleSessionStart(WebSocketSession session, ChatMessageDTO messageDTO) {
        String userId = getUserId(session);
        
        try {
            // 创建聊天会话
            ChatSession chatSession = chatSessionService.createSession(
                Long.valueOf(userId), 
                messageDTO.getSubject(), 
                messageDTO.getPriority()
            );
            
            // 发送会话创建成功消息
            ChatMessageDTO response = new ChatMessageDTO();
            response.setType("session_created");
            response.setSessionId(chatSession.getSessionId());
            response.setWelcomeMessage("您好！欢迎使用在线客服，正在为您分配客服人员...");
            
            sendMessage(session, JSON.toJSONString(response));
            
            // 尝试分配客服
            messageRoutingService.assignAgent(chatSession.getId());
            
        } catch (Exception e) {
            log.error("创建会话失败", e);
            sendErrorMessage(session, "创建会话失败");
        }
    }
    
    // 处理聊天消息
    private void handleChatMessage(WebSocketSession session, ChatMessageDTO messageDTO) {
        String userId = getUserId(session);
        String userType = getUserType(session);
        
        try {
            // 保存消息到数据库
            ChatMessage chatMessage = messageService.saveMessage(messageDTO, userId, userType);
            
            // 路由消息
            messageRoutingService.routeMessage(chatMessage);
            
        } catch (Exception e) {
            log.error("处理聊天消息失败", e);
            sendErrorMessage(session, "消息发送失败");
        }
    }
    
    // 处理输入状态
    private void handleTypingStatus(WebSocketSession session, ChatMessageDTO messageDTO, boolean isTyping) {
        String userId = getUserId(session);
        String userType = getUserType(session);
        
        // 转发输入状态给对方
        if ("user".equals(userType)) {
            // 用户输入，通知客服
            Long agentId = chatSessionService.getAssignedAgent(messageDTO.getSessionId());
            if (agentId != null) {
                sendToAgent(agentId, createTypingMessage(isTyping));
            }
        } else if ("agent".equals(userType)) {
            // 客服输入，通知用户
            Long sessionUserId = chatSessionService.getSessionUserId(messageDTO.getSessionId());
            if (sessionUserId != null) {
                sendToUser(sessionUserId, createTypingMessage(isTyping));
            }
        }
    }
    
    // 发送消息给指定用户
    public void sendToUser(Long userId, String message) {
        WebSocketSession session = userSessions.get(userId.toString());
        if (session != null && session.isOpen()) {
            try {
                session.sendMessage(new TextMessage(message));
            } catch (IOException e) {
                log.error("发送消息给用户失败: {}", userId, e);
            }
        }
    }
    
    // 发送消息给指定客服
    public void sendToAgent(Long agentId, String message) {
        WebSocketSession session = agentSessions.get(agentId.toString());
        if (session != null && session.isOpen()) {
            try {
                session.sendMessage(new TextMessage(message));
            } catch (IOException e) {
                log.error("发送消息给客服失败: {}", agentId, e);
            }
        }
    }
    
    // 工具方法
    private String getUserId(WebSocketSession session) {
        return (String) session.getAttributes().get("userId");
    }
    
    private String getUserType(WebSocketSession session) {
        return (String) session.getAttributes().get("userType");
    }
    
    private void sendMessage(WebSocketSession session, String message) {
        try {
            if (session.isOpen()) {
                session.sendMessage(new TextMessage(message));
            }
        } catch (IOException e) {
            log.error("发送WebSocket消息失败", e);
        }
    }
    
    private void sendErrorMessage(WebSocketSession session, String errorMsg) {
        ChatMessageDTO errorMessage = new ChatMessageDTO();
        errorMessage.setType("error");
        errorMessage.setContent(errorMsg);
        sendMessage(session, JSON.toJSONString(errorMessage));
    }
    
    private String createSystemMessage(String content) {
        ChatMessageDTO systemMessage = new ChatMessageDTO();
        systemMessage.setType("system_notice");
        systemMessage.setContent(content);
        return JSON.toJSONString(systemMessage);
    }
    
    private String createTypingMessage(boolean isTyping) {
        ChatMessageDTO typingMessage = new ChatMessageDTO();
        typingMessage.setType(isTyping ? "typing_start" : "typing_end");
        return JSON.toJSONString(typingMessage);
    }
}
```

### 2.3 WebSocket拦截器

```java
@Component
public class WebSocketInterceptor implements HandshakeInterceptor {
    
    @Autowired
    private JwtTokenUtil jwtTokenUtil;
    
    @Override
    public boolean beforeHandshake(ServerHttpRequest request, ServerHttpResponse response,
                                 WebSocketHandler wsHandler, Map<String, Object> attributes) throws Exception {
        
        // 从URL参数中获取token和用户类型
        String query = request.getURI().getQuery();
        Map<String, String> params = parseQueryString(query);
        
        String token = params.get("token");
        String userType = params.get("type"); // user 或 agent
        
        if (token == null || userType == null) {
            return false;
        }
        
        try {
            // 验证JWT Token
            if (!jwtTokenUtil.validateToken(token)) {
                return false;
            }
            
            // 获取用户信息
            String userId = jwtTokenUtil.getUserIdFromToken(token);
            
            // 存储到WebSocket会话属性中
            attributes.put("userId", userId);
            attributes.put("userType", userType);
            attributes.put("token", token);
            
            return true;
            
        } catch (Exception e) {
            log.error("WebSocket握手验证失败", e);
            return false;
        }
    }
    
    @Override
    public void afterHandshake(ServerHttpRequest request, ServerHttpResponse response,
                             WebSocketHandler wsHandler, Exception exception) {
        // 握手完成后的处理
    }
    
    private Map<String, String> parseQueryString(String query) {
        Map<String, String> params = new HashMap<>();
        if (query != null) {
            String[] pairs = query.split("&");
            for (String pair : pairs) {
                String[] keyValue = pair.split("=");
                if (keyValue.length == 2) {
                    params.put(keyValue[0], keyValue[1]);
                }
            }
        }
        return params;
    }
}
```

这个实现提供了完整的WebSocket实时通信基础，包括：

1. **连接管理** - 用户和客服的连接建立、断开处理
2. **消息路由** - 智能消息分发和路由
3. **会话管理** - 聊天会话的创建、分配、结束
4. **状态同步** - 输入状态、在线状态的实时同步
5. **安全认证** - JWT token验证和权限控制
6. **错误处理** - 完善的异常处理和错误反馈

接下来需要实现具体的业务服务类。
