---
title: MinioMinio
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"
---
# MinioMinio

Base URLs:

# Authentication

# 注意

用户登录以后会返回一个token字符串，以后前端发送每个请求(除了登录请求)都会在请求头携带此token，格式为：Authorization：eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOjEsImNyZWF0ZWQiOjE3NTA2OTAxMzA5NDcsImF1dGhvcml0eSI6bnVsbCwiZXhwIjoxNzUxMjk0OTMwfQ.xSI3zT1z72roR4i7evxBWZ5uE0nfI1_UZDmCQVChdDdGllq7tzx8jxeXQoVkwPGyC_yun-nN2caa2chn35wfOw。

# 用户管理

`<a id="opIdgetInfoUsingGET"></a>`

## GET 获取用户信息

GET /api/user/getInfo

> 返回示例

> 200 Response

```
{"code":"string",
"data":{"avatar":"string","createTime":"2019-08-24T14:15:22Z","email":"string","id":0,"isDeleted":0,"nickName":"string","password":"string","phonenumber":"string","sex":"string","status":"string","updateTime":"2019-08-24T14:15:22Z","userName":"string"},
"message":"string"}
```

### 返回结果

| 状态码 | 状态码含义                                                   | 说明         | 数据模型                                         |
| ------ | ------------------------------------------------------------ | ------------ | ------------------------------------------------ |
| 200    | [OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)         | OK           | [CommonResult«User»](#schemacommonresult«user») |
| 401    | [Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1) | Unauthorized | Inline                                           |
| 403    | [Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)  | Forbidden    | Inline                                           |
| 404    | [Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)  | Not Found    | Inline                                           |

### 返回数据结构

状态码 **200**

*CommonResult«User»*

| 名称             | 类型              | 必选  | 约束 | 中文名 | 说明 |
| ---------------- | ----------------- | ----- | ---- | ------ | ---- |
| » code          | string            | false | none |        | none |
| » data          | [User](#schemauser)  | false | none | User   | none |
| »» avatar      | string            | false | none |        | none |
| »» createTime  | string(date-time) | false | none |        | none |
| »» email       | string            | false | none |        | none |
| »» id          | integer(int64)    | false | none |        | none |
| »» isDeleted   | integer(int32)    | false | none |        | none |
| »» nickName    | string            | false | none |        | none |
| »» password    | string            | false | none |        | none |
| »» phonenumber | string            | false | none |        | none |
| »» sex         | string            | false | none |        | none |
| »» status      | string            | false | none |        | none |
| »» updateTime  | string(date-time) | false | none |        | none |
| »» userName    | string            | false | none |        | none |
| » message       | string            | false | none |        | none |

### 成功返回格式：

```json
{
    "code": "200",
    "message": "操作成功",
    "data": {
        "id": 1,
        "userName": "admin",
        "nickName": "",
        "password": "$2a$10$if/7MAHZdCKt90DHHZGPaOp2qICGWJBu8G0SInvhTnvikMwnk6eKC",
        "status": "1",
        "email": "<EMAIL>",
        "phonenumber": "13800001111",
        "sex": "1",
        "avatar": "http://example.com/avatar1.jpg",
        "createTime": "2025-06-11T16:00:00.000+00:00",
        "updateTime": "2025-06-22T16:00:00.000+00:00",
        "isDeleted": 0
    }
}
```

`<a id="opIdloginUsingPOST"></a>`

## POST 用户登录

POST /api/user/login

> Body 请求参数

```json
{
  "password": "string",
  "userName": "string"
}
```

### 请求参数

| 名称 | 位置 | 类型                       | 必选 | 中文名    | 说明 |
| ---- | ---- | -------------------------- | ---- | --------- | ---- |
| body | body | [UserParam](#schemauserparam) | 否   | UserParam | none |

> 返回示例

> 200 Response

```
{"code":"string","data":{"property1":"string","property2":"string"},"message":"string"}
```

### 返回结果

| 状态码 | 状态码含义                                                   | 说明         | 数据模型                                                                         |
| ------ | ------------------------------------------------------------ | ------------ | -------------------------------------------------------------------------------- |
| 200    | [OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)         | OK           | [CommonResult«Map«string,string»»](#schemacommonresult«map«string,string»») |
| 201    | [Created](https://tools.ietf.org/html/rfc7231#section-6.3.2)    | Created      | Inline                                                                           |
| 401    | [Unauthorized](https://tools.ietf.org/html/rfc7235#section-3.1) | Unauthorized | Inline                                                                           |
| 403    | [Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)  | Forbidden    | Inline                                                                           |
| 404    | [Not Found](https://tools.ietf.org/html/rfc7231#section-6.5.4)  | Not Found    | Inline                                                                           |

### 返回数据结构

状态码 **200**

*CommonResult«Map«string,string»»*

| 名称                               | 类型   | 必选  | 约束 | 中文名 | 说明 |
| ---------------------------------- | ------ | ----- | ---- | ------ | ---- |
| » code                            | string | false | none |        | none |
| » data                            | object | false | none |        | none |
| »»**additionalProperties** | string | false | none |        | none |
| » message                         | string | false | none |        | none |

### 成功返回格式：

```json
{
    "code": "200",
    "message": "操作成功",
    "data": {
        "token": "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOjEsImNyZWF0ZWQiOjE3NTA2OTAxMzA5NDcsImF1dGhvcml0eSI6bnVsbCwiZXhwIjoxNzUxMjk0OTMwfQ.xSI3zT1z72roR4i7evxBWZ5uE0nfI1_UZDmCQVChdDdGllq7tzx8jxeXQoVkwPGyC_yun-nN2caa2chn35wfOw"
    }
}
```

# 数据模型

<h2 id="tocS_CommonResult«Map«string,string»»">CommonResult«Map«string,string»»</h2>

`<a id="schemacommonresult«map«string,string»»"></a>`
`<a id="schema_CommonResult«Map«string,string»»"></a>`
`<a id="tocScommonresult«map«string,string»»"></a>`
`<a id="tocscommonresult«map«string,string»»"></a>`

```json
{
  "code": "string",
  "data": {
    "property1": "string",
    "property2": "string"
  },
  "message": "string"
}

```

CommonResult«Map«string,string»»

### 属性

| 名称                             | 类型   | 必选  | 约束 | 中文名 | 说明 |
| -------------------------------- | ------ | ----- | ---- | ------ | ---- |
| code                             | string | false | none |        | none |
| data                             | object | false | none |        | none |
| »**additionalProperties** | string | false | none |        | none |
| message                          | string | false | none |        | none |

<h2 id="tocS_CommonResult«User»">CommonResult«User»</h2>

`<a id="schemacommonresult«user»"></a>`
`<a id="schema_CommonResult«User»"></a>`
`<a id="tocScommonresult«user»"></a>`
`<a id="tocscommonresult«user»"></a>`

```json
{
  "code": "string",
  "data": {
    "avatar": "string",
    "createTime": "2019-08-24T14:15:22Z",
    "email": "string",
    "id": 0,
    "isDeleted": 0,
    "nickName": "string",
    "password": "string",
    "phonenumber": "string",
    "sex": "string",
    "status": "string",
    "updateTime": "2019-08-24T14:15:22Z",
    "userName": "string"
  },
  "message": "string"
}

```

CommonResult«User»

### 属性

| 名称    | 类型             | 必选  | 约束 | 中文名 | 说明 |
| ------- | ---------------- | ----- | ---- | ------ | ---- |
| code    | string           | false | none |        | none |
| data    | [User](#schemauser) | false | none |        | none |
| message | string           | false | none |        | none |

<h2 id="tocS_User">User</h2>

`<a id="schemauser"></a>`
`<a id="schema_User"></a>`
`<a id="tocSuser"></a>`
`<a id="tocsuser"></a>`

```json
{
  "avatar": "string",
  "createTime": "2019-08-24T14:15:22Z",
  "email": "string",
  "id": 0,
  "isDeleted": 0,
  "nickName": "string",
  "password": "string",
  "phonenumber": "string",
  "sex": "string",
  "status": "string",
  "updateTime": "2019-08-24T14:15:22Z",
  "userName": "string"
}

```

User

### 属性

| 名称        | 类型              | 必选  | 约束 | 中文名 | 说明 |
| ----------- | ----------------- | ----- | ---- | ------ | ---- |
| avatar      | string            | false | none |        | none |
| createTime  | string(date-time) | false | none |        | none |
| email       | string            | false | none |        | none |
| id          | integer(int64)    | false | none |        | none |
| isDeleted   | integer(int32)    | false | none |        | none |
| nickName    | string            | false | none |        | none |
| password    | string            | false | none |        | none |
| phonenumber | string            | false | none |        | none |
| sex         | string            | false | none |        | none |
| status      | string            | false | none |        | none |
| updateTime  | string(date-time) | false | none |        | none |
| userName    | string            | false | none |        | none |

<h2 id="tocS_UserParam">UserParam</h2>

`<a id="schemauserparam"></a>`
`<a id="schema_UserParam"></a>`
`<a id="tocSuserparam"></a>`
`<a id="tocsuserparam"></a>`

```json
{
  "password": "string",
  "userName": "string"
}

```

UserParam

### 属性

| 名称     | 类型   | 必选  | 约束 | 中文名 | 说明 |
| -------- | ------ | ----- | ---- | ------ | ---- |
| password | string | false | none |        | none |
| userName | string | false | none |        | none |
