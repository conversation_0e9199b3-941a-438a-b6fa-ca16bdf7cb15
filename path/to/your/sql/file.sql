DELIMITER $$

CREATE PROCEDURE GenerateRandomEmployees(IN num INT)
BEGIN
    DECLARE i INT DEFAULT 0;
    DECLARE random_name VARCHAR(255);
    DECLARE random_age INT;
    DECLARE random_gender TINYINT;

    -- 姓名列表（示例）
    SET @first_names = '["张", "王", "李", "赵", "陈", "杨", "黄", "周", "吴", "徐"]';
    SET @last_names = '["三", "四", "五", "六", "七", "八", "九", "十", "一", "二"]';

    WHILE i < num DO
        -- 随机生成姓名
        SET random_name = CONCAT(
            JSON_UNQUOTE(JSON_EXTRACT(@first_names, CONCAT('$[', FLOOR(RAND() * 10), ']'))),
            JSON_UNQUOTE(JSON_EXTRACT(@last_names, CONCAT('$[', FLOOR(RAND() * 10), ']')))
        );

        -- 随机生成年龄（18到60）
        SET random_age = FLOOR(RAND() * 43) + 18;

        -- 随机生成性别（0或1）
        SET random_gender = FLOOR(RAND() * 2);

        -- 插入数据
        INSERT INTO employee (name, age, gender)
        VALUES (random_name, random_age, random_gender);

        SET i = i + 1;
    END WHILE;
END$$

DELIMITER ;