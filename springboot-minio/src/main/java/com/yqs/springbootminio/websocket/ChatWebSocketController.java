
    /**
     * 处理加入聊天室
     */
    @MessageMapping("/chat.joinRoom/{roomCode}")
    public void joinRoom(@DestinationVariable String roomCode, Principal principal) {
        try {
            WebSocketAuthInterceptor.WebSocketUserPrincipal userPrincipal = 
                (WebSocketAuthInterceptor.WebSocketUserPrincipal) principal;
            User currentUser = userPrincipal.getUser();

            // 获取聊天室
            ChatRoom chatRoom = chatService.getChatRoomByCode(roomCode);
            if (chatRoom == null) {
                log.warn("聊天室不存在，房间编码: {}", roomCode);
                return;
            }

            // 标记消息为已读
            chatService.markMessagesAsRead(chatRoom.getId(), currentUser.getId());

            // 发送加入通知
            Map<String, Object> joinNotification = new HashMap<>();
            joinNotification.put("type", "USER_JOINED");
            joinNotification.put("userId", currentUser.getId());
            joinNotification.put("userName", currentUser.getNickName() != null ? currentUser.getNickName() : currentUser.getUserName());
            joinNotification.put("message", currentUser.getUserName() + " 加入了聊天");

            messagingTemplate.convertAndSend("/topic/chat/" + roomCode, joinNotification);

            log.info("用户加入聊天室，用户: {}, 房间编码: {}", currentUser.getUserName(), roomCode);

        } catch (Exception e) {
            log.error("加入聊天室失败，房间编码: {}, 错误: {}", roomCode, e.getMessage(), e);
        }
    }

    /**
     * 订阅聊天室（补充实现）
     */
    @SubscribeMapping("/topic/chat/{roomCode}")
    public void subscribeToRoom(@DestinationVariable String roomCode, Principal principal) {
        log.info("用户订阅聊天室，房间编码: {}", roomCode);
        
        // 获取当前用户
        WebSocketAuthInterceptor.WebSocketUserPrincipal userPrincipal = 
            (WebSocketAuthInterceptor.WebSocketUserPrincipal) principal;
        User currentUser = userPrincipal.getUser();

        // 获取聊天室
        ChatRoom chatRoom = chatService.getChatRoomByCode(roomCode);
        if (chatRoom == null) {
            log.warn("聊天室不存在，房间编码: {}", roomCode);
            return;
        }

        // 验证用户是否有权限订阅该聊天室
        if (!customerServiceService.hasRoomPermission(currentUser.getId(),
                chatRoom.getUserId(), chatRoom.getCustomerServiceId())) {
            log.warn("用户无权限订阅此聊天室，用户ID: {}, 房间编码: {}", currentUser.getId(), roomCode);
            return;
        }

        // 记录订阅操作
        log.info("用户 {} 成功订阅聊天室 {}", currentUser.getUserName(), roomCode);
    }
