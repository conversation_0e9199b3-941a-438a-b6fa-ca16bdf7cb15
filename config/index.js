'use strict'
// Template version: 1.3.1
// see http://vuejs-templates.github.io/webpack for documentation.

const path = require('path')

module.exports = {
  dev: {

    // Paths
    assetsSubDirectory: 'static',
    assetsPublicPath: '/',
    proxyTable: {
      '/api': {
        target: 'http://localhost:8080',
        //target: 'https://minio.jdb1109.xyz/',
        changeOrigin: true,
        logLevel: 'debug',
        secure: false,
        ws: true,  // 启用WebSocket代理
        xfwd: true,
        toProxy: true,
        prependPath: false,
        ignorePath: false,
        onProxyReq: function(proxyReq, req, res) {
          console.log('🔄 [PROXY REQ]', req.method, req.url, '->', proxyReq.path);
          console.log('🔄 [PROXY REQ] Headers:', req.headers);
        },
        onProxyRes: function(proxyRes, req, res) {
          console.log('✅ [PROXY RES]', req.method, req.url, 'Status:', proxyRes.statusCode);
          console.log('✅ [PROXY RES] Headers:', proxyRes.headers);
        },
        onError: function(err, req, res) {
          console.error('❌ [PROXY ERROR]', req.method, req.url, 'Error:', err.message);
        }
      },
      '/ws': {
        target: 'http://localhost:8080',
        changeOrigin: true,
        ws: true,  // 启用WebSocket代理
        logLevel: 'debug',
        onProxyReqWs: function(proxyReq, req, socket, options, head) {
          console.log('🔗 [WS PROXY REQ]', req.url);
        },
        onError: function(err, req, res) {
          console.error('❌ [WS PROXY ERROR]', req.url, 'Error:', err.message);
        }
      }
    },

    // Various Dev Server settings
    host: 'localhost', // can be overwritten by process.env.HOST
    port: 3000, // can be overwritten by process.env.PORT, if port is in use, a free one will be determined
    autoOpenBrowser: false,
    errorOverlay: true,
    notifyOnErrors: true,
    poll: false, // https://webpack.js.org/configuration/dev-server/#devserver-watchoptions-

    // Use Eslint Loader?
    // If true, your code will be linted during bundling and
    // linting errors and warnings will be shown in the console.
    useEslint: false,
    // If true, eslint errors and warnings will also be shown in the error overlay
    // in the browser.
    showEslintErrorsInOverlay: false,

    /**
     * Source Maps
     */

    // https://webpack.js.org/configuration/devtool/#development
    devtool: 'cheap-module-eval-source-map',

    // If you have problems debugging vue-files in devtools,
    // set this to false - it *may* help
    // https://vue-loader.vuejs.org/en/options.html#cachebusting
    cacheBusting: true,

    cssSourceMap: true
  },

  build: {
    // Template for index.html
    index: path.resolve(__dirname, '../dist/index.html'),

    // Paths
    assetsRoot: path.resolve(__dirname, '../dist'),
    assetsSubDirectory: 'static',
    assetsPublicPath: '/',

    /**
     * Source Maps
     */

    productionSourceMap: true,
    // https://webpack.js.org/configuration/devtool/#production
    devtool: '#source-map',

    // Gzip off by default as many popular static hosts such as
    // Surge or Netlify already gzip all static assets for you.
    // Before setting to `true`, make sure to:
    // npm install --save-dev compression-webpack-plugin
    productionGzip: false,
    productionGzipExtensions: ['js', 'css'],

    // Run the build command with an extra argument to
    // View the bundle analyzer report after build finishes:
    // `npm run build --report`
    // Set to `true` or `false` to always turn it on or off
    bundleAnalyzerReport: process.env.npm_config_report
  }
}
