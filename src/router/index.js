import Vue from 'vue'
import Router from 'vue-router'
import userPermissionService from '@/utils/userPermission'
// import HelloWorld from '@/components/HelloWorld'

Vue.use(Router)

const router = new Router({
  routes: [
    {
      path: '/',
      redirect: '/index'
    },
    {
      path: '/index',
      name: 'Index',
      component: () => import('@/components/Index.vue'),
      meta: { requiresAuth: true } // 需要登录
    },
    {
      path: '/login',
      name: 'Login',
      component: () => import('@/components/Login.vue')
    },
    {
      path: '/register',
      name: 'Register',
      component: () => import('@/components/Register.vue')
    },
    {
      path: '/dashboard',
      name: 'Dashboard',
      component: () => import('@/components/Dashboard.vue'),
      meta: { requiresAuth: true } // 需要登录
    },
    {
      path: '/customer-service-workbench',
      name: 'CustomerServiceWorkbench',
      component: () => import('@/components/CustomerServiceWorkbench.vue'),
      meta: {
        requiresAuth: true,
        requiresCustomerService: true,
        title: '客服工作台'
      }
    },
    {
      path: '/customer-service-test',
      name: 'CustomerServiceTest',
      component: () => import('@/components/CustomerServiceTest.vue'),
      meta: {
        requiresAuth: true,
        title: '客服API测试'
      }
    },
    {
      path: '/chat-test',
      name: 'ChatTest',
      component: () => import('@/components/ChatTest.vue'),
      meta: {
        requiresAuth: true,
        title: '聊天功能测试'
      }
    },

  ]
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const token = localStorage.getItem('token')

  // 如果路由需要认证
  if (to.matched.some(record => record.meta.requiresAuth)) {
    if (!token) {
      // 没有token，跳转到登录页
      next({
        path: '/login',
        query: { redirect: to.fullPath } // 保存原来要访问的路径
      })
      return
    }



    // 检查是否需要客服权限
    if (to.matched.some(record => record.meta.requiresCustomerService)) {
      try {
        const userType = await userPermissionService.getUserType()
        if (!userType.isCustomerService) {
          // 不是客服，跳转到首页
          next('/index')
          return
        }
      } catch (error) {
        console.error('检查客服权限失败:', error)
        next('/index')
        return
      }
    }

    next()
  } else {
    // 如果是登录页且已经有token，跳转到首页
    if (to.path === '/login' && token) {
      next('/index')
    } else {
      next()
    }
  }
})

export default router
