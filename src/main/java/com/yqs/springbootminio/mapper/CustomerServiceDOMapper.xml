<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yqs.springbootminio.mapper.CustomerServiceMapper">

    <resultMap id="BaseResultMap" type="com.yqs.springbootminio.dao.CustomerServiceDO">
            <id property="id" column="id" />
            <result property="userId" column="user_id" />
            <result property="serviceName" column="service_name" />
            <result property="avatar" column="avatar" />
            <result property="status" column="status" />
            <result property="maxConcurrentChats" column="max_concurrent_chats" />
            <result property="currentChatCount" column="current_chat_count" />
            <result property="createTime" column="create_time" />
            <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="Base_Column_List">
        id,user_id,service_name,avatar,status,max_concurrent_chats,
        current_chat_count,create_time,update_time
    </sql>

    <!-- 查询单条数据 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM customer_service
        WHERE id = #{id}
    </select>

    <!-- 根据用户ID查询客服信息 -->
    <select id="selectByUserId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM customer_service
        WHERE user_id = #{userId}
    </select>

    <!-- 查询所有在线客服 -->
    <select id="selectOnlineServices" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM customer_service
        WHERE status = 1
        ORDER BY current_chat_count ASC
    </select>

    <!-- 查询可用的客服（在线且未达到最大并发数） -->
    <select id="selectAvailableServices" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM customer_service
        WHERE status = 1 AND current_chat_count &lt; max_concurrent_chats
        ORDER BY current_chat_count ASC
    </select>

    <!-- 新增数据 -->
    <insert id="insert" parameterType="com.yqs.springbootminio.dao.CustomerServiceDO" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO customer_service (user_id, service_name, avatar, status, max_concurrent_chats, current_chat_count)
        VALUES (#{userId}, #{serviceName}, #{avatar}, #{status}, #{maxConcurrentChats}, #{currentChatCount})
    </insert>

    <!-- 修改数据 -->
    <update id="update" parameterType="com.yqs.springbootminio.dao.CustomerServiceDO">
        UPDATE customer_service
        <set>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="serviceName != null and serviceName != ''">service_name = #{serviceName},</if>
            <if test="avatar != null">avatar = #{avatar},</if>
            <if test="status != null">status = #{status},</if>
            <if test="maxConcurrentChats != null">max_concurrent_chats = #{maxConcurrentChats},</if>
            <if test="currentChatCount != null">current_chat_count = #{currentChatCount},</if>
        </set>
        WHERE id = #{id}
    </update>

    <!-- 通过主键删除数据 -->
    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM customer_service WHERE id = #{id}
    </delete>

    <!-- 更新客服状态 -->
    <update id="updateStatus">
        UPDATE customer_service SET status = #{status} WHERE id = #{id}
    </update>

    <!-- 增加当前聊天数 -->
    <update id="incrementChatCount" parameterType="java.lang.Long">
        UPDATE customer_service SET current_chat_count = current_chat_count + 1 WHERE id = #{id}
    </update>

    <!-- 减少当前聊天数 -->
    <update id="decrementChatCount" parameterType="java.lang.Long">
        UPDATE customer_service SET current_chat_count = GREATEST(current_chat_count - 1, 0) WHERE id = #{id}
    </update>

</mapper>
