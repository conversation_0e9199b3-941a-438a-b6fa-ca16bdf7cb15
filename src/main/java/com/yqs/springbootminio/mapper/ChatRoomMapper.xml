<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yqs.springbootminio.mapper.ChatRoomMapper">

    <!-- 结果集映射 -->
    <resultMap id="BaseResultMap" type="com.yqs.springbootminio.dao.ChatRoomDO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="room_code" property="roomCode" jdbcType="VARCHAR"/>
        <result column="user_id" property="userId" jdbcType="BIGINT"/>
        <result column="customer_service_id" property="customerServiceId" jdbcType="BIGINT"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="last_message_time" property="lastMessageTime" jdbcType="TIMESTAMP"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, room_code, user_id, customer_service_id, status, last_message_time, create_time, update_time
    </sql>

    <!-- 查询单条数据 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM chat_room
        WHERE id = #{id}
    </select>

    <!-- 根据房间编码查询 -->
    <select id="selectByRoomCode" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM chat_room
        WHERE room_code = #{roomCode}
    </select>

    <!-- 根据用户ID查询聊天室列表 -->
    <select id="selectByUserId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM chat_room
        WHERE user_id = #{userId}
        ORDER BY last_message_time DESC, create_time DESC
    </select>

    <!-- 根据客服ID查询聊天室列表 -->
    <select id="selectByCustomerServiceId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM chat_room
        WHERE customer_service_id = #{customerServiceId}
        ORDER BY last_message_time DESC, create_time DESC
    </select>

    <!-- 查询用户的活跃聊天室 -->
    <select id="selectActiveRoomByUserId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM chat_room
        WHERE user_id = #{userId} AND status IN (1, 2)
        ORDER BY create_time DESC
        LIMIT 1
    </select>

    <!-- 新增数据 -->
    <insert id="insert" parameterType="com.yqs.springbootminio.dao.ChatRoomDO" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO chat_room (room_code, user_id, customer_service_id, status, last_message_time)
        VALUES (#{roomCode}, #{userId}, #{customerServiceId}, #{status}, #{lastMessageTime})
    </insert>

    <!-- 修改数据 -->
    <update id="update" parameterType="com.yqs.springbootminio.dao.ChatRoomDO">
        UPDATE chat_room
        <set>
            <if test="roomCode != null and roomCode != ''">room_code = #{roomCode},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="customerServiceId != null">customer_service_id = #{customerServiceId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="lastMessageTime != null">last_message_time = #{lastMessageTime},</if>
        </set>
        WHERE id = #{id}
    </update>

    <!-- 通过主键删除数据 -->
    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM chat_room WHERE id = #{id}
    </delete>

    <!-- 更新聊天室状态 -->
    <update id="updateStatus">
        UPDATE chat_room SET status = #{status} WHERE id = #{id}
    </update>

    <!-- 分配客服 -->
    <update id="assignCustomerService">
        UPDATE chat_room SET customer_service_id = #{customerServiceId}, status = 1 WHERE id = #{id}
    </update>

    <!-- 查询等待客服的聊天室列表 -->
    <select id="selectPendingRooms" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM chat_room
        WHERE status = 2
        ORDER BY create_time ASC
    </select>

</mapper>
