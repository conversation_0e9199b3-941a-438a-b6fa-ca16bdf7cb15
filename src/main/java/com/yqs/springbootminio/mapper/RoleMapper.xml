<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yqs.springbootminio.mapper.RoleMapper">
    <!-- 结果集映射 -->
    <resultMap id="BaseResultMap" type="com.yqs.springbootminio.dao.RoleDO">
        <id column="role_id" property="roleId" jdbcType="BIGINT"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="level" property="level" jdbcType="INTEGER"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="data_scope" property="dataScope" jdbcType="VARCHAR"/>
        <result column="create_by" property="createBy" jdbcType="VARCHAR"/>
        <result column="update_by" property="updateBy" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="DATE"/>
        <result column="update_time" property="updateTime" jdbcType="DATE"/>
    </resultMap>

    <!-- 查询单条数据 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT *
        FROM role
        WHERE role_id = #{roleId}
    </select>

    <!-- 查询所有数据 -->
    <select id="selectAll" resultMap="BaseResultMap">
        SELECT *
        FROM role
    </select>

    <!-- 根据条件查询数据 -->
    <select id="queryAllByLimit" parameterType="com.yqs.springbootminio.dao.RoleDO" resultMap="BaseResultMap">
        SELECT * FROM role
        <where>
            <if test="roleId != null">
                AND role_id = #{roleId}
            </if>
            <if test="name != null and name != ''">
                AND name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="level != null">
                AND level = #{level}
            </if>
            <if test="description != null and description != ''">
                AND description LIKE CONCAT('%', #{description}, '%')
            </if>
        </where>
    </select>
    <select id="selectByUserId" resultType="com.yqs.springbootminio.dao.RoleDO">
        SELECT * FROM role
        WHERE role_id IN (
            SELECT role_id FROM user_role
            WHERE user_id = #{userId}
        )
    </select>

    <!-- 新增数据 -->
    <insert id="insert" parameterType="com.yqs.springbootminio.dao.RoleDO">
        INSERT INTO role (name, level, description, data_scope, create_by, update_by, create_time, update_time)
        VALUES (#{name}, #{level}, #{description}, #{dataScope}, #{createBy}, #{updateBy}, #{createTime}, #{updateTime})
    </insert>

    <!-- 修改数据 -->
    <update id="update" parameterType="com.yqs.springbootminio.dao.RoleDO">
        UPDATE role
        SET
        <if test="name != null">
            name = #{name},
        </if>
        <if test="level != null">
            level = #{level},
        </if>
        <if test="description != null">
            description = #{description},
        </if>
        <if test="dataScope != null">
            data_scope = #{dataScope},
        </if>
        <if test="createBy != null">
            create_by = #{createBy},
        </if>
        <if test="updateBy != null">
            update_by = #{updateBy},
        </if>
        <if test="createTime != null">
            create_time = #{createTime},
        </if>
        <if test="updateTime != null">
            update_time = #{updateTime}
        </if>
        WHERE role_id = #{roleId}
    </update>

    <!-- 通过主键删除数据 -->
    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE
        FROM role
        WHERE role_id = #{roleId}
    </delete>
</mapper>
