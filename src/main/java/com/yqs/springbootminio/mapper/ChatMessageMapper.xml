<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yqs.springbootminio.mapper.ChatMessageMapper">

    <!-- 结果集映射 -->
    <resultMap id="BaseResultMap" type="com.yqs.springbootminio.dao.ChatMessageDO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="room_id" property="roomId" jdbcType="BIGINT"/>
        <result column="sender_id" property="senderId" jdbcType="BIGINT"/>
        <result column="sender_type" property="senderType" jdbcType="INTEGER"/>
        <result column="message_type" property="messageType" jdbcType="INTEGER"/>
        <result column="content" property="content" jdbcType="LONGVARCHAR"/>
        <result column="is_read" property="isRead" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, room_id, sender_id, sender_type, message_type, content, is_read, create_time
    </sql>

    <!-- 查询单条数据 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM chat_message
        WHERE id = #{id}
    </select>

    <!-- 根据聊天室ID查询消息列表（分页） -->
    <select id="selectByRoomId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM chat_message
        WHERE room_id = #{roomId}
        ORDER BY create_time ASC
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <!-- 根据聊天室ID查询消息总数 -->
    <select id="countByRoomId" parameterType="java.lang.Long" resultType="int">
        SELECT COUNT(1) FROM chat_message WHERE room_id = #{roomId}
    </select>

    <!-- 查询聊天室的最新消息 -->
    <select id="selectLatestByRoomId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM chat_message
        WHERE room_id = #{roomId}
        ORDER BY create_time DESC
        LIMIT 1
    </select>

    <!-- 新增数据 -->
    <insert id="insert" parameterType="com.yqs.springbootminio.dao.ChatMessageDO" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO chat_message (room_id, sender_id, sender_type, message_type, content, is_read)
        VALUES (#{roomId}, #{senderId}, #{senderType}, #{messageType}, #{content}, #{isRead})
    </insert>

    <!-- 修改数据 -->
    <update id="update" parameterType="com.yqs.springbootminio.dao.ChatMessageDO">
        UPDATE chat_message
        <set>
            <if test="roomId != null">room_id = #{roomId},</if>
            <if test="senderId != null">sender_id = #{senderId},</if>
            <if test="senderType != null">sender_type = #{senderType},</if>
            <if test="messageType != null">message_type = #{messageType},</if>
            <if test="content != null and content != ''">content = #{content},</if>
            <if test="isRead != null">is_read = #{isRead},</if>
        </set>
        WHERE id = #{id}
    </update>

    <!-- 通过主键删除数据 -->
    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM chat_message WHERE id = #{id}
    </delete>

    <!-- 标记消息为已读 -->
    <update id="markAsRead">
        UPDATE chat_message 
        SET is_read = 1 
        WHERE room_id = #{roomId} AND sender_id != #{readerId} AND is_read = 0
    </update>

    <!-- 查询未读消息数量 -->
    <select id="countUnreadMessages" resultType="int">
        SELECT COUNT(1)
        FROM chat_message
        WHERE room_id = #{roomId} AND sender_id != #{readerId} AND is_read = 0
    </select>

    <!-- 查询聊天室的最新消息（用于客服工作台） -->
    <select id="selectLatestMessageByRoomId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM chat_message
        WHERE room_id = #{roomId}
        ORDER BY create_time DESC
        LIMIT 1
    </select>

</mapper>
