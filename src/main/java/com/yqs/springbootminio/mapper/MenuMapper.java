package com.yqs.springbootminio.mapper;

import com.yqs.springbootminio.dao.MenuDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 系统菜单表数据库访问层接口
 *
 * <AUTHOR>
 * @since 2025-05-14 10:27:04
 */
@Mapper
public interface MenuMapper {
    /**
     * 查询单条数据
     *
     * @param menuId 主键
     * @return 实例对象
     */
    MenuDO selectById(Long menuId);

    /**
     * 查询所有数据
     *
     * @return 对象列表
     */
    List<MenuDO> selectAll();

    /**
     * 根据条件查询数据
     *
     * @param menuDO 实例对象
     * @return 对象列表
     */
    List<MenuDO> queryAllByLimit(MenuDO menuDO);

    /**
     * 新增数据
     *
     * @param menuDO 实例对象
     * @return 影响行数
     */
    int insert(MenuDO menuDO);

    /**
     * 修改数据
     *
     * @param menuDO 实例对象
     * @return 影响行数
     */
    int update(MenuDO menuDO);

    /**
     * 通过主键删除数据
     *
     * @param menuId 主键
     * @return 影响行数
     */
    int deleteById(Long menuId);

    /**
     * 根据用户ID查询用户拥有的菜单权限
     *
     * @param userId 用户ID
     * @return 菜单列表
     */
    List<MenuDO> selectMenusByUserId(Long userId);
}

