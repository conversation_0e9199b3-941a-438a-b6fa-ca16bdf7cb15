<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yqs.springbootminio.mapper.MenuMapper">
    <!-- 结果集映射 -->
    <resultMap id="BaseResultMap" type="com.yqs.springbootminio.dao.MenuDO">
        <id column="menu_id" property="menuId" jdbcType="BIGINT"/>
        <result column="pid" property="pid" jdbcType="BIGINT"/>
        <result column="sub_count" property="subCount" jdbcType="INTEGER"/>
        <result column="type" property="type" jdbcType="INTEGER"/>
        <result column="title" property="title" jdbcType="VARCHAR"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="component" property="component" jdbcType="VARCHAR"/>
        <result column="menu_sort" property="menuSort" jdbcType="INTEGER"/>
        <result column="icon" property="icon" jdbcType="VARCHAR"/>
        <result column="path" property="path" jdbcType="VARCHAR"/>
        <result column="i_frame" property="iFrame" jdbcType="BOOLEAN"/>
        <result column="cache" property="cache" jdbcType="BOOLEAN"/>
        <result column="hidden" property="hidden" jdbcType="BOOLEAN"/>
        <result column="permission" property="permission" jdbcType="VARCHAR"/>
        <result column="create_by" property="createBy" jdbcType="VARCHAR"/>
        <result column="update_by" property="updateBy" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="DATE"/>
        <result column="update_time" property="updateTime" jdbcType="DATE"/>
    </resultMap>

    <!-- 查询单条数据 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT *
        FROM menu
        WHERE menu_id = #{menuId}
    </select>

    <!-- 查询所有数据 -->
    <select id="selectAll" resultMap="BaseResultMap">
        SELECT *
        FROM menu
    </select>

    <!-- 根据条件查询数据 -->
    <select id="queryAllByLimit" parameterType="com.yqs.springbootminio.dao.MenuDO" resultMap="BaseResultMap">
        SELECT * FROM menu
        <where>
            <if test="menuId != null">
                AND menu_id = #{menuId}
            </if>
            <if test="pid != null">
                AND pid = #{pid}
            </if>
            <if test="title != null and title != ''">
                AND title LIKE CONCAT('%', #{title}, '%')
            </if>
            <if test="name != null and name != ''">
                AND name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="type != null">
                AND type = #{type}
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
        </where>
    </select>

    <!-- 新增数据 -->
    <insert id="insert" parameterType="com.yqs.springbootminio.dao.MenuDO">
        INSERT INTO menu (pid, sub_count, type, title, name, component, menu_sort, icon, path, i_frame, cache, hidden, permission, create_by, update_by, create_time, update_time)
        VALUES (#{pid}, #{subCount}, #{type}, #{title}, #{name}, #{component}, #{menuSort}, #{icon}, #{path}, #{iFrame}, #{cache}, #{hidden}, #{permission}, #{createBy}, #{updateBy}, #{createTime}, #{updateTime})
    </insert>

    <!-- 修改数据 -->
    <update id="update" parameterType="com.yqs.springbootminio.dao.MenuDO">
        UPDATE menu
        SET
        <if test="pid != null">
            pid = #{pid},
        </if>
        <if test="subCount != null">
            sub_count = #{subCount},
        </if>
        <if test="type != null">
            type = #{type},
        </if>
        <if test="title != null">
            title = #{title},
        </if>
        <if test="name != null">
            name = #{name},
        </if>
        <if test="component != null">
            component = #{component},
        </if>
        <if test="menuSort != null">
            menu_sort = #{menuSort},
        </if>
        <if test="icon != null">
            icon = #{icon},
        </if>
        <if test="path != null">
            path = #{path},
        </if>
        <if test="iFrame != null">
            i_frame = #{iFrame},
        </if>
        <if test="cache != null">
            cache = #{cache},
        </if>
        <if test="hidden != null">
            hidden = #{hidden},
        </if>
        <if test="permission != null">
            permission = #{permission},
        </if>
        <if test="createBy != null">
            create_by = #{createBy},
        </if>
        <if test="updateBy != null">
            update_by = #{updateBy},
        </if>
        <if test="createTime != null">
            create_time = #{createTime},
        </if>
        <if test="updateTime != null">
            update_time = #{updateTime}
        </if>
        WHERE menu_id = #{menuId}
    </update>

    <!-- 通过主键删除数据 -->
    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE
        FROM menu
        WHERE menu_id = #{menuId}
    </delete>

    <!-- 根据用户ID查询用户拥有的菜单权限 -->
    <select id="selectMenusByUserId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
            m.*
        FROM
            USER u
            JOIN users_roles ur ON u.id = ur.user_id
            JOIN roles_menus rm ON ur.role_id = rm.role_id
            JOIN menu m ON rm.menu_id = m.menu_id
        WHERE u.id = #{userId}
        ORDER BY m.menu_sort ASC
    </select>
</mapper>
