package com.yqs.springbootminio.mapper;

import com.yqs.springbootminio.dao.UsersRolesDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 用户角色关联表数据库访问层接口
 *
 * <AUTHOR>
 * @since 2025-05-14 10:27:04
 */
@Mapper
public interface UsersRolesMapper {
    /**
     * 查询单条数据
     *
     * @param userId 主键
     * @return 实例对象
     */
    UsersRolesDO selectById(Long userId);

    /**
     * 查询所有数据
     *
     * @return 对象列表
     */
    List<UsersRolesDO> selectAll();

    /**
     * 根据条件查询数据
     *
     * @param usersRolesDO 实例对象
     * @return 对象列表
     */
    List<UsersRolesDO> queryAllByLimit(UsersRolesDO usersRolesDO);

    /**
     * 新增数据
     *
     * @param usersRolesDO 实例对象
     * @return 影响行数
     */
    int insert(UsersRolesDO usersRolesDO);

    /**
     * 修改数据
     *
     * @param usersRolesDO 实例对象
     * @return 影响行数
     */
    int update(UsersRolesDO usersRolesDO);

    /**
     * 通过主键删除数据
     *
     * @param userId 主键
     * @return 影响行数
     */
    int deleteById(Long userId);
}