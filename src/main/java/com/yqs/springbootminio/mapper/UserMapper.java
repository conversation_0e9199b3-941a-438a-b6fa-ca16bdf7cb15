package com.yqs.springbootminio.mapper;

import com.yqs.springbootminio.dao.UserDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 用户表(User)表数据库访问层接口
 *
 * <AUTHOR>
 * @since 2025-05-14 10:27:04
 */
@Mapper
public interface UserMapper {
    /**
     * 查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    UserDO selectById(Long id);

    /**
     * 查询所有数据
     *
     * @return 对象列表
     */
    List<UserDO> selectAll();

    /**
     * 根据条件查询数据
     *
     * @param userDO 实例对象
     * @return 对象列表
     */
    List<UserDO> queryAllByLimit(UserDO userDO);

    /**
     * 新增数据
     *
     * @param userDO 实例对象
     * @return 影响行数
     */
    int insert(UserDO userDO);

    /**
     * 修改数据
     *
     * @param userDO 实例对象
     * @return 影响行数
     */
    int update(UserDO userDO);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Long id);

    /**
     * 通过用户名查询数据
     *
     * @param userName 用户名
     * @return 影响行数
     */
    UserDO selectByUserName(String userName);
}