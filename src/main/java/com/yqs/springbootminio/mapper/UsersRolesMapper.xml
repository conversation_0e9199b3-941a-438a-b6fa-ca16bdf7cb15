<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yqs.springbootminio.mapper.UsersRolesMapper">
    <!-- 结果集映射 -->
    <resultMap id="BaseResultMap" type="com.yqs.springbootminio.dao.UsersRolesDO">
        <id column="user_id" property="userId" jdbcType="BIGINT"/>
        <result column="role_id" property="roleId" jdbcType="BIGINT"/>
    </resultMap>

    <!-- 查询单条数据 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT *
        FROM users_roles
        WHERE user_id = #{userId}
    </select>

    <!-- 查询所有数据 -->
    <select id="selectAll" resultMap="BaseResultMap">
        SELECT *
        FROM users_roles
    </select>

    <!-- 根据条件查询数据 -->
    <select id="queryAllByLimit" parameterType="com.yqs.springbootminio.dao.UsersRolesDO" resultMap="BaseResultMap">
        SELECT * FROM users_roles
        <where>
            <if test="userId != null">
                AND user_id = #{userId}
            </if>
            <if test="roleId != null">
                AND role_id = #{roleId}
            </if>
        </where>
    </select>

    <!-- 新增数据 -->
    <insert id="insert" parameterType="com.yqs.springbootminio.dao.UsersRolesDO">
        INSERT INTO users_roles (user_id, role_id)
        VALUES (#{userId}, #{roleId})
    </insert>

    <!-- 修改数据 -->
    <update id="update" parameterType="com.yqs.springbootminio.dao.UsersRolesDO">
        UPDATE users_roles
        SET
        <if test="userId != null">
            user_id = #{userId},
        </if>
        <if test="roleId != null">
            role_id = #{roleId}
        </if>
        WHERE user_id = #{userId}
    </update>

    <!-- 通过主键删除数据 -->
    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE
        FROM users_roles
        WHERE user_id = #{userId}
    </delete>
</mapper>
