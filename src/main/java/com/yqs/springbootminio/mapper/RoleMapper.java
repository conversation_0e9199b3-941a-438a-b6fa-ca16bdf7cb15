package com.yqs.springbootminio.mapper;

import com.yqs.springbootminio.dao.RoleDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 角色表数据库访问层接口
 *
 * <AUTHOR>
 * @since 2025-05-14 10:27:04
 */
@Mapper
public interface RoleMapper {
    /**
     * 查询单条数据
     *
     * @param roleId 主键
     * @return 实例对象
     */
    RoleDO selectById(Long roleId);

    /**
     * 根据用户ID查询角色列表
     *
     * @param userId 用户ID
     * @return 角色列表
     */
    List<RoleDO> selectByUserId(Long userId);

    /**
     * 查询所有数据
     *
     * @return 对象列表
     */
    List<RoleDO> selectAll();

    /**
     * 根据条件查询数据
     *
     * @param roleDO 实例对象
     * @return 对象列表
     */
    List<RoleDO> queryAllByLimit(RoleDO roleDO);

    /**
     * 新增数据
     *
     * @param roleDO 实例对象
     * @return 影响行数
     */
    int insert(RoleDO roleDO);

    /**
     * 修改数据
     *
     * @param roleDO 实例对象
     * @return 影响行数
     */
    int update(RoleDO roleDO);

    /**
     * 通过主键删除数据
     *
     * @param roleId 主键
     * @return 影响行数
     */
    int deleteById(Long roleId);
}
