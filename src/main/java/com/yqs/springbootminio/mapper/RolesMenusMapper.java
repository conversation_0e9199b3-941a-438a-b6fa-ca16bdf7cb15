package com.yqs.springbootminio.mapper;

import com.yqs.springbootminio.dao.RolesMenusDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 角色菜单关联表数据库访问层接口
 *
 * <AUTHOR>
 * @since 2025-05-14 10:27:04
 */
@Mapper
public interface RolesMenusMapper {
    /**
     * 查询单条数据
     *
     * @param menuId 主键
     * @return 实例对象
     */
    RolesMenusDO selectById(Long menuId);

    /**
     * 查询所有数据
     *
     * @return 对象列表
     */
    List<RolesMenusDO> selectAll();

    /**
     * 根据条件查询数据
     *
     * @param rolesMenusDO 实例对象
     * @return 对象列表
     */
    List<RolesMenusDO> queryAllByLimit(RolesMenusDO rolesMenusDO);

    /**
     * 新增数据
     *
     * @param rolesMenusDO 实例对象
     * @return 影响行数
     */
    int insert(RolesMenusDO rolesMenusDO);

    /**
     * 修改数据
     *
     * @param rolesMenusDO 实例对象
     * @return 影响行数
     */
    int update(RolesMenusDO rolesMenusDO);

    /**
     * 通过主键删除数据
     *
     * @param menuId 主键
     * @return 影响行数
     */
    int deleteById(Long menuId);
}
