package com.yqs.springbootminio.mapper;

import com.yqs.springbootminio.dao.ChatMessageDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 聊天消息表数据库访问层接口
 *
 * <AUTHOR>
 * @since 2025-06-24
 */
@Mapper
public interface ChatMessageMapper {

    /**
     * 查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    ChatMessageDO selectById(Long id);

    /**
     * 根据聊天室ID查询消息列表（分页）
     *
     * @param roomId 聊天室ID
     * @param offset 偏移量
     * @param limit  限制数量
     * @return 消息列表
     */
    List<ChatMessageDO> selectByRoomId(@Param("roomId") Long roomId, 
                                       @Param("offset") Integer offset, 
                                       @Param("limit") Integer limit);

    /**
     * 根据聊天室ID查询消息总数
     *
     * @param roomId 聊天室ID
     * @return 消息总数
     */
    int countByRoomId(Long roomId);

    /**
     * 查询聊天室的最新消息
     *
     * @param roomId 聊天室ID
     * @return 最新消息
     */
    ChatMessageDO selectLatestByRoomId(Long roomId);

    /**
     * 新增数据
     *
     * @param chatMessageDO 实例对象
     * @return 影响行数
     */
    int insert(ChatMessageDO chatMessageDO);

    /**
     * 修改数据
     *
     * @param chatMessageDO 实例对象
     * @return 影响行数
     */
    int update(ChatMessageDO chatMessageDO);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Long id);

    /**
     * 标记消息为已读
     *
     * @param roomId   聊天室ID
     * @param readerId 阅读者ID
     * @return 影响行数
     */
    int markAsRead(@Param("roomId") Long roomId, @Param("readerId") Long readerId);

    /**
     * 查询未读消息数量
     *
     * @param roomId   聊天室ID
     * @param readerId 阅读者ID
     * @return 未读消息数量
     */
    int countUnreadMessages(@Param("roomId") Long roomId, @Param("readerId") Long readerId);

    /**
     * 查询聊天室的最新消息（用于客服工作台）
     *
     * @param roomId 聊天室ID
     * @return 最新消息
     */
    ChatMessageDO selectLatestMessageByRoomId(Long roomId);
}
