package com.yqs.springbootminio.mapper;

import com.yqs.springbootminio.dao.EmployeeDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface EmployeeMapper {

    /**
     * 插入员工信息
     *
     * @param employeeDO 员工对象
     * @return 影响的行数
     */
    int insert(EmployeeDO employeeDO);

    /**
     * 根据ID删除员工信息
     *
     * @param id 员工ID
     * @return 影响的行数
     */
    int deleteById(@Param("id") int id);

    /**
     * 更新员工信息
     *
     * @param employeeDO 员工对象
     * @return 影响的行数
     */
    int update(EmployeeDO employeeDO);

    /**
     * 根据ID查询员工信息
     *
     * @param id 员工ID
     * @return 员工对象
     */
    EmployeeDO selectById(@Param("id") int id);

    /**
     * 查询所有员工信息
     *
     * @return 员工列表
     */
    List<EmployeeDO> selectAll();

    /**
     * 查询总条数
     */
    Integer selectCount();
}