package com.yqs.springbootminio.mapper;

import com.yqs.springbootminio.dao.CustomerServiceDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【customer_service(客服表)】的数据库操作Mapper
* @createDate 2025-06-25 11:56:17
* @Entity com.yqs.springbootminio.dao.CustomerServiceDO
*/
@Mapper
public interface CustomerServiceMapper {

    /**
     * 查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    CustomerServiceDO selectById(Long id);

    /**
     * 根据用户ID查询客服信息
     *
     * @param userId 用户ID
     * @return 客服信息
     */
    CustomerServiceDO selectByUserId(Long userId);

    /**
     * 查询所有在线客服
     *
     * @return 在线客服列表
     */
    List<CustomerServiceDO> selectOnlineServices();

    /**
     * 查询可用的客服（在线且未达到最大并发数）
     *
     * @return 可用客服列表
     */
    List<CustomerServiceDO> selectAvailableServices();

    /**
     * 新增数据
     *
     * @param customerServiceDO 实例对象
     * @return 影响行数
     */
    int insert(CustomerServiceDO customerServiceDO);

    /**
     * 修改数据
     *
     * @param customerServiceDO 实例对象
     * @return 影响行数
     */
    int update(CustomerServiceDO customerServiceDO);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Long id);

    /**
     * 更新客服状态
     *
     * @param id     客服ID
     * @param status 状态
     * @return 影响行数
     */
    int updateStatus(@Param("id") Long id, @Param("status") Integer status);

    /**
     * 增加当前聊天数
     *
     * @param id 客服ID
     * @return 影响行数
     */
    int incrementChatCount(Long id);

    /**
     * 减少当前聊天数
     *
     * @param id 客服ID
     * @return 影响行数
     */
    int decrementChatCount(Long id);
}




