<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.yqs.springbootminio.mapper.EmployeeMapper">
    <!-- 结果集映射 -->
    <resultMap id="BaseResultMap" type="com.yqs.springbootminio.dao.EmployeeDO">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="age" property="age"/>
        <result column="gender" property="gender"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 插入员工信息 -->
    <insert id="insert" parameterType="com.yqs.springbootminio.dao.EmployeeDO">
        INSERT INTO employee (name, age, gender)
        VALUES (#{name}, #{age}, #{gender})
    </insert>

    <!-- 根据ID删除员工信息 -->
    <delete id="deleteById" parameterType="int">
        DELETE FROM employee WHERE id = #{id}
    </delete>

    <!-- 更新员工信息 -->
    <update id="update" parameterType="com.yqs.springbootminio.dao.EmployeeDO">
        UPDATE employee
        SET name = #{name}, age = #{age}, gender = #{gender}
        WHERE id = #{id}
    </update>

    <!-- 根据ID查询员工信息 -->
    <select id="selectById" parameterType="int"  resultMap="BaseResultMap">
        SELECT id, name, age, gender, create_time as createTime, update_time as updateTime
        FROM employee
        WHERE id = #{id}
    </select>

    <!-- 查询所有员工信息 -->
    <select id="selectAll" resultMap="BaseResultMap">
        SELECT *
        FROM employee
    </select>

    <select id="selectCount" resultType="java.lang.Integer">
        select count(*) from employee
    </select>


</mapper>