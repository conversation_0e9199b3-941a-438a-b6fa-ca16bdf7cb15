<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yqs.springbootminio.mapper.UserMapper">
    <!-- 结果集映射 -->
    <resultMap id="BaseResultMap" type="com.yqs.springbootminio.dao.UserDO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="user_name" property="userName" jdbcType="VARCHAR"/>
        <result column="nick_name" property="nickName" jdbcType="VARCHAR"/>
        <result column="password" property="password" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="email" property="email" jdbcType="VARCHAR"/>
        <result column="phone_number" property="phonenumber" jdbcType="VARCHAR"/>
        <result column="sex" property="sex" jdbcType="VARCHAR"/>
        <result column="avatar" property="avatar" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="DATE"/>
        <result column="update_time" property="updateTime" jdbcType="DATE"/>
        <result column="is_deleted" property="isDeleted" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 查询单条数据 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT *
        FROM user
        WHERE id = #{id}
    </select>

    <!-- 查询所有数据 -->
    <select id="selectAll" resultMap="BaseResultMap">
        SELECT *
        FROM user
    </select>

    <!-- 根据条件查询数据 -->
    <select id="queryAllByLimit" parameterType="com.yqs.springbootminio.dao.UserDO" resultMap="BaseResultMap">
        SELECT * FROM user
        <where>
            <if test="id != null">
                AND id = #{id}
            </if>
            <if test="userName != null and userName != ''">
                AND user_name LIKE CONCAT('%', #{userName}, '%')
            </if>
            <if test="nickName != null and nickName != ''">
                AND nick_name LIKE CONCAT('%', #{nickName}, '%')
            </if>
            <if test="email != null and email != ''">
                AND email LIKE CONCAT('%', #{email}, '%')
            </if>
            <if test="phonenumber != null and phonenumber != ''">
                AND phone_number LIKE CONCAT('%', #{phonenumber}, '%')
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
            <if test="isDeleted != null">
                AND is_deleted = #{isDeleted}
            </if>
        </where>
    </select>

    <select id="selectByUserName" resultMap="BaseResultMap">
        SELECT * FROM user WHERE user_name = #{userName}
    </select>

    <!-- 新增数据 -->
    <insert id="insert" parameterType="com.yqs.springbootminio.dao.UserDO">
        INSERT INTO user (user_name, nick_name, password, status, email, phone_number, sex, avatar, create_time,
                          update_time, is_deleted)
        VALUES (#{userName}, #{nickName}, #{password}, #{status}, #{email}, #{phonenumber}, #{sex}, #{avatar},
                #{createTime}, #{updateTime}, #{isDeleted})
    </insert>

    <!-- 修改数据 -->
    <update id="update" parameterType="com.yqs.springbootminio.dao.UserDO">
        UPDATE user
        SET
        <if test="userName != null">
            user_name = #{userName},
        </if>
        <if test="nickName != null">
            nick_name = #{nickName},
        </if>
        <if test="password != null">
            password = #{password},
        </if>
        <if test="status != null">
            status = #{status},
        </if>
        <if test="email != null">
            email = #{email},
        </if>
        <if test="phonenumber != null">
            phone_number = #{phonenumber},
        </if>
        <if test="sex != null">
            sex = #{sex},
        </if>
        <if test="avatar != null">
            avatar = #{avatar},
        </if>
        <if test="createTime != null">
            create_time = #{createTime},
        </if>
        <if test="updateTime != null">
            update_time = #{updateTime},
        </if>
        <if test="isDeleted != null">
            is_deleted = #{isDeleted}
        </if>
        WHERE id = #{id}
    </update>

    <!-- 通过主键删除数据 -->
    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE
        FROM user
        WHERE id = #{id}
    </delete>
</mapper>