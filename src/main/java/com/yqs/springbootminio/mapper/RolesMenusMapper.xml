<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yqs.springbootminio.mapper.RolesMenusMapper">
    <!-- 结果集映射 -->
    <resultMap id="BaseResultMap" type="com.yqs.springbootminio.dao.RolesMenusDO">
        <id column="menu_id" property="menuId" jdbcType="BIGINT"/>
        <result column="role_id" property="roleId" jdbcType="BIGINT"/>
    </resultMap>

    <!-- 查询单条数据 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT *
        FROM roles_menus
        WHERE menu_id = #{menuId}
    </select>

    <!-- 查询所有数据 -->
    <select id="selectAll" resultMap="BaseResultMap">
        SELECT *
        FROM roles_menus
    </select>

    <!-- 根据条件查询数据 -->
    <select id="queryAllByLimit" parameterType="com.yqs.springbootminio.dao.RolesMenusDO" resultMap="BaseResultMap">
        SELECT * FROM roles_menus
        <where>
            <if test="menuId != null">
                AND menu_id = #{menuId}
            </if>
            <if test="roleId != null">
                AND role_id = #{roleId}
            </if>
        </where>
    </select>

    <!-- 新增数据 -->
    <insert id="insert" parameterType="com.yqs.springbootminio.dao.RolesMenusDO">
        INSERT INTO roles_menus (menu_id, role_id)
        VALUES (#{menuId}, #{roleId})
    </insert>

    <!-- 修改数据 -->
    <update id="update" parameterType="com.yqs.springbootminio.dao.RolesMenusDO">
        UPDATE roles_menus
        SET
        <if test="menuId != null">
            menu_id = #{menuId},
        </if>
        <if test="roleId != null">
            role_id = #{roleId}
        </if>
        WHERE menu_id = #{menuId}
    </update>

    <!-- 通过主键删除数据 -->
    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE
        FROM roles_menus
        WHERE menu_id = #{menuId}
    </delete>
</mapper>
