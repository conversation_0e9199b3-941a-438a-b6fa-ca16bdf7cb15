package com.yqs.springbootminio.util;

import java.io.Serializable;
import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2025/5/15
 * @description
 */
public class Page implements Serializable {

    private static final long serialVersionUID = 2279972730030934855L;

    /**
     * total row count of the Query.
     * if the value is -1, will generate a count() SQL and execute it to get the count of whole Query.
     * if the value is larger than -1, will do nothing except Query.
     */
    private long rowCount = -1; // 总条数
    /**
     * this will start from 1
     */
    private int currentPage = 1;
    /**
     * default will query all entries.
     */
    private int pageSize = Integer.MAX_VALUE;
    /**
     * shift the entries from current page. It's better to reset it to 0 after use once.
     * Exp: currentPage=2, pageSize=10, offset=-3, it will start at 7 and end at 20
     *      currentPage=2, pageSize=10, offset=3, it will start at 13 and end at 20
     */
    private int offset = 0;
    /**
     * this will add order by block and link array columns.
     */
    private String[] orderBys;

    private CountMethod countMethod = CountMethod.Count;

    public Page() {
    }

    public Page(int currentPage, int pageSize) {
        this.currentPage = currentPage;
        this.pageSize = pageSize;
    }

    public Page(int currentPage, int pageSize, String... orderBy) {
        this.currentPage = currentPage;
        this.pageSize = pageSize;
        setOrderBys(orderBy);
    }

    public long getRowCount() {
        return rowCount;
    }

    public void setRowCount(long rowCount) {
        this.rowCount = rowCount;
    }

    public int getCurrentPage() {
        return currentPage;
    }

    public void setCurrentPage(int currentPage) {
        this.currentPage = currentPage;
    }

    public String[] getOrderBys() {
        return orderBys;
    }

    public void setOrderBys(String[] orderBys) {
        this.orderBys = orderBys;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public boolean hasMore() {
        if (countMethod==CountMethod.Count) {
            if(this.currentPage < 0) {
                this.currentPage = 0;
                return true;
            }
            return (long) this.pageSize * this.currentPage < this.rowCount;
        } else {
            return this.pageSize < this.rowCount;
        }
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        long result = 1;
        result = prime * result + currentPage;
        result = prime * result + Arrays.hashCode(orderBys);
        result = prime * result + pageSize;
        result = prime * result + rowCount;
        return Math.toIntExact(result);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj){
            return true;
        }
        if (obj == null){
            return false;
        }
        if (getClass() != obj.getClass()){
            return false;
        }
        Page other = (Page) obj;
        if (currentPage != other.currentPage){
            return false;
        }
        if (!Arrays.equals(orderBys, other.orderBys)){
            return false;
        }
        if (pageSize != other.pageSize){
            return false;
        }
        if (rowCount != other.rowCount){
            return false;
        }
        return offset == other.offset;
    }

    public int getOffset() {
        return offset;
    }

    public void setOffset(int offset) {
        this.offset = offset;
    }

    public CountMethod getCountMethod() {
        return countMethod;
    }

    public void setCountMethod(CountMethod countMethod) {
        this.countMethod = countMethod;
    }

    public static enum CountMethod {
        /**
         * 从数据库 执行 select count获取
         */
        Count(1),
        /**
         * 根据list长度获取和pageCount
         */
        NextPage(2);

        private int code;

        private CountMethod(int code){
            this.code = code;
        }

        public int getCode() {
            return code;
        }

        public void setCode(int code) {
            this.code = code;
        }
    }
}
