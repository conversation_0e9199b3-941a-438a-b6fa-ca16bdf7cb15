package com.yqs.springbootminio.util;

import java.io.Serializable;

/**
 * 统一结果返回类
 *
 * @param <T> 返回数据类型
 */
public class CommonResult<T> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 状态码
     */
    private String code;

    /**
     * 提示信息
     */
    private String message;

    /**
     * 数据
     */
    private T data;

    /**
     * 构造方法
     *
     * @param code    状态码
     * @param message 提示信息
     * @param data    数据
     */
    private CommonResult(String code, String message, T data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    /**
     * 成功返回结果
     *
     * @param data 数据内容
     * @return CommonResult
     */
    public static <T> CommonResult<T> success(T data) {
        return new CommonResult<>("200", "操作成功", data);
    }

    public static <T> CommonResult<T> success() {
        return new CommonResult<>("200", "操作成功", null);
    }

    public static <T> CommonResult<T> error(String code, String message) {
        return new CommonResult<>(code, message, null);
    }

    public static <T> CommonResult<T> error(String message) {
        return new CommonResult<>("500", message, null);
    }

    public static <T> CommonResult<T> failed(String code, String message) {
        return new CommonResult<>(code, message, null);
    }

    public static <T> CommonResult<T> failed(String message) {
        return new CommonResult<>("500", message, null);
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }
}