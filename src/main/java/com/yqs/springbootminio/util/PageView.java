package com.yqs.springbootminio.util;

import java.io.Serializable;
import java.util.Iterator;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/15
 * @description
 */
public class PageView<T> implements Serializable {

    private static final long serialVersionUID = 8682734550751989951L;

    private List<T> records;
    private Page page;
    public PageView(){
    }

    public PageView(List<T> records, Page page){
        this.page = page;
        setRecords(records);
    }

    public List<T> getRecords() {
        return records;
    }

    public void setRecords(List<T> records) {
        int size = records.size();
        if (this.page != null && this.page.getCountMethod() == Page.CountMethod.NextPage) {
            if (size>this.page.getPageSize()) {
                Iterator<T> iter = records.iterator();
                while (iter.hasNext()) {
                    iter.next();
                }
                iter.remove();
            }

            int currentPage = this.page.getCurrentPage();
            if (currentPage < 1) {
                currentPage = 1;
            }
            this.page.setRowCount((currentPage-1)*this.getPage().getPageSize()+size);
        }
        this.records = records;
    }

    public Page getPage() {
        return page;
    }

    public void setPage(Page page) {
        this.page = page;
    }
}
