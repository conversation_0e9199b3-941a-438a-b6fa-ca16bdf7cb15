package com.yqs.springbootminio.model;

import com.yqs.springbootminio.enums.RoleLevelEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.Set;

/**
 * 用户表(User)实体类
 *
 * <AUTHOR>
 * @since 2025-05-14 10:27:04
 */
@Data
public class User implements Serializable {
    private static final long serialVersionUID = -3949981933635420394L;
    /**
     * 主键
     */
    private Long id;
    /**
     * 用户名
     */
    private String userName;
    /**
     * 昵称
     */
    private String nickName;
    /**
     * 密码
     */
    private String password;
    /**
     * 账号状态（0未激活 1已激活）
     */
    private Integer status;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 手机号
     */
    private String phonenumber;
    /**
     * 用户性别（0男，1女，2未知）
     */
    private String sex;
    /**
     * 头像
     */
    private String avatar;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 删除标志（0代表未删除，1代表已删除）
     */
    private Integer isDeleted;

    /**
     * 角色名称
     */
    private Set<RoleLevelEnum> roleName;



}

