package com.yqs.springbootminio.model;

import com.yqs.springbootminio.enums.ExportStatus;
import lombok.Data;

import java.io.File;

/**
 * <AUTHOR>
 * @date 2025/6/14
 * @description 导出任务信息类
 */
@Data
public class ExportTask {

    // 任务ID - 唯一标识
    private String taskId;
    // 任务状态 - 枚举类型
    private ExportStatus status = ExportStatus.PROCESSING;
    // 进度 - 0-100
    private int progress;
    // 输出文件
    private File outputFile;
}
