package com.yqs.springbootminio.model;

import com.yqs.springbootminio.enums.UserStatusEnum;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.Collection;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/23
 * @description
 */
public class AdminUserDetails implements UserDetails {

    /**
     * 后台用户
     */
    private User adminUser;

    public User getAdminUser() {
        return adminUser;
    }

    public void setAdminUser(User adminUser) {
        this.adminUser = adminUser;
    }

    /**
     * 权限字符串
     */
    private Collection<? extends GrantedAuthority> authorities;

    public AdminUserDetails(User adminUser) {
        this.adminUser = adminUser;
    }

    public void setAuthorities(Set<String> perms) {
        this.authorities = perms.stream().filter(perm -> !perm.isEmpty()).map(SimpleGrantedAuthority::new)
                .collect(Collectors.toList());
    }

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        return this.authorities;
    }

    @Override
    public String getPassword() {
        return adminUser.getPassword();
    }

    @Override
    public String getUsername() {
        return adminUser.getUserName();
    }

    @Override
    public boolean isAccountNonExpired() {
        return true; // 账户未过期
    }

    @Override
    public boolean isAccountNonLocked() {
        return true; // 账户未锁定
    }

    @Override
    public boolean isCredentialsNonExpired() {
        return true; // 凭证未过期
    }

    @Override
    public boolean isEnabled() {
        // 根据用户状态判断是否启用（1激活 0未激活）
        return adminUser != null && UserStatusEnum.ACTIVE.getStatus() == adminUser.getStatus();
    }

}
