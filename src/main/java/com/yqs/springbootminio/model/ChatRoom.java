package com.yqs.springbootminio.model;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 聊天室(ChatRoom)实体类
 *
 * <AUTHOR>
 * @since 2025-06-24
 */
@Data
public class ChatRoom implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 聊天室ID
     */
    private Long id;

    /**
     * 聊天室编码
     */
    private String roomCode;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 客服ID
     */
    private Long customerServiceId;

    /**
     * 状态：0-关闭，1-活跃，2-等待客服
     */
    private Integer status;

    /**
     * 最后消息时间
     */
    private Date lastMessageTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 用户信息（关联查询时使用）
     */
    private User user;

    /**
     * 客服信息（关联查询时使用）
     */
    private User customerService;
}
