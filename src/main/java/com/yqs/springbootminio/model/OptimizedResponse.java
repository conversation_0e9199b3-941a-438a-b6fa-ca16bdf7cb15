package com.yqs.springbootminio.model;

import java.util.List;
import java.util.Map;

public class OptimizedResponse {
    private Meta meta;
    private Map<String, User> users;
    private List<Post> posts;
    private List<Link> links;

    // Getters and Setters
    public Meta getMeta() {
        return meta;
    }

    public void setMeta(Meta meta) {
        this.meta = meta;
    }

    public Map<String, User> getUsers() {
        return users;
    }

    public void setUsers(Map<String, User> users) {
        this.users = users;
    }

    public List<Post> getPosts() {
        return posts;
    }

    public void setPosts(List<Post> posts) {
        this.posts = posts;
    }

    public List<Link> getLinks() {
        return links;
    }

    public void setLinks(List<Link> links) {
        this.links = links;
    }

    // Inner classes for nested structures
    public static class Meta {
        private int totalPosts;
        private int page;
        private int perPage;
        private boolean hasMore;

        // Getters and Setters
        public int getTotalPosts() {
            return totalPosts;
        }

        public void setTotalPosts(int totalPosts) {
            this.totalPosts = totalPosts;
        }

        public int getPage() {
            return page;
        }

        public void setPage(int page) {
            this.page = page;
        }

        public int getPerPage() {
            return perPage;
        }

        public void setPerPage(int perPage) {
            this.perPage = perPage;
        }

        public boolean isHasMore() {
            return hasMore;
        }

        public void setHasMore(boolean hasMore) {
            this.hasMore = hasMore;
        }
    }

    public static class User {
        private int id;
        private String username;
        private String avatarTemplate;
        private int trustLevel;

        // Getters and Setters
        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public String getUsername() {
            return username;
        }

        public void setUsername(String username) {
            this.username = username;
        }

        public String getAvatarTemplate() {
            return avatarTemplate;
        }

        public void setAvatarTemplate(String avatarTemplate) {
            this.avatarTemplate = avatarTemplate;
        }

        public int getTrustLevel() {
            return trustLevel;
        }

        public void setTrustLevel(int trustLevel) {
            this.trustLevel = trustLevel;
        }
    }

    public static class Post {
        private int id;
        private int userId;
        private String cooked;
        private String createdAt;
        private String updatedAt;
        private List<Reaction> reactions;

        // Getters and Setters
        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public int getUserId() {
            return userId;
        }

        public void setUserId(int userId) {
            this.userId = userId;
        }

        public String getCooked() {
            return cooked;
        }

        public void setCooked(String cooked) {
            this.cooked = cooked;
        }

        public String getCreatedAt() {
            return createdAt;
        }

        public void setCreatedAt(String createdAt) {
            this.createdAt = createdAt;
        }

        public String getUpdatedAt() {
            return updatedAt;
        }

        public void setUpdatedAt(String updatedAt) {
            this.updatedAt = updatedAt;
        }

        public List<Reaction> getReactions() {
            return reactions;
        }

        public void setReactions(List<Reaction> reactions) {
            this.reactions = reactions;
        }

        // Inner class for Reaction
        public static class Reaction {
            private String id;
            private String type;
            private int count;

            // Getters and Setters
            public String getId() {
                return id;
            }

            public void setId(String id) {
                this.id = id;
            }

            public String getType() {
                return type;
            }

            public void setType(String type) {
                this.type = type;
            }

            public int getCount() {
                return count;
            }

            public void setCount(int count) {
                this.count = count;
            }
        }
    }

    public static class Link {
        private String url;
        private String title;
        private int clicks;

        // Getters and Setters
        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public int getClicks() {
            return clicks;
        }

        public void setClicks(int clicks) {
            this.clicks = clicks;
        }
    }
}