package com.yqs.springbootminio.model;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 聊天消息(ChatMessage)实体类
 *
 * <AUTHOR>
 * @since 2025-06-24
 */
@Data
public class ChatMessage implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 消息ID
     */
    private Long id;

    /**
     * 聊天室ID
     */
    private Long roomId;

    /**
     * 发送者ID
     */
    private Long senderId;

    /**
     * 发送者类型：1-用户，2-客服
     */
    private Integer senderType;

    /**
     * 消息类型：1-文本，2-图片，3-文件
     */
    private Integer messageType;

    /**
     * 消息内容
     */
    private String content;

    /**
     * 是否已读：0-未读，1-已读
     */
    private Integer isRead;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 发送者信息（关联查询时使用）
     */
    private User sender;
}
