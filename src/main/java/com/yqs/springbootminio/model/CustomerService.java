package com.yqs.springbootminio.model;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 客服表(CustomerService)实体类
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Data
public class CustomerService implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 客服ID
     */
    private Long id;

    /**
     * 关联用户ID
     */
    private Long userId;

    /**
     * 客服名称
     */
    private String serviceName;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 状态：0-离线，1-在线，2-忙碌
     */
    private Integer status;

    /**
     * 最大并发聊天数
     */
    private Integer maxConcurrentChats;

    /**
     * 当前聊天数
     */
    private Integer currentChatCount;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 关联的用户信息
     */
    private User user;
}
