package com.yqs.springbootminio.tests;

/**
 * <AUTHOR>
 * @date 2025/6/25
 * @description
 */
public abstract class Factory {

    public Factory() {
        System.out.println("我是一个抽象工厂的无参构造");
    }

    public void produce(){
        System.out.println("我是抽象工厂的produce方法");
    }

    static class Car extends Factory{
        public Car() {
            System.out.println("我继承抽象工厂的子类");
        }

        @Override
        public void produce() {
            super.produce();
            System.out.println("我制造汽车");
        }
    }

    public static void main(String[] args) {
        Car car = new Car();
        car.produce();
    }
}

