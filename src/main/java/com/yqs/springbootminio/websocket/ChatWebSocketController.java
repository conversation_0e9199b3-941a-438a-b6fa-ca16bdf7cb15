package com.yqs.springbootminio.websocket;

import com.yqs.springbootminio.enums.MessageTypeEnum;
import com.yqs.springbootminio.enums.SenderTypeEnum;
import com.yqs.springbootminio.model.ChatMessage;
import com.yqs.springbootminio.model.ChatRoom;
import com.yqs.springbootminio.model.User;
import com.yqs.springbootminio.service.ChatService;
import com.yqs.springbootminio.service.CustomerServiceService;
import com.yqs.springbootminio.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.DestinationVariable;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.messaging.simp.annotation.SubscribeMapping;
import org.springframework.stereotype.Controller;

import java.security.Principal;
import java.util.HashMap;
import java.util.Map;

/**
 * WebSocket聊天控制器
 *
 * <AUTHOR>
 * @since 2025-06-24
 */
@Slf4j
@Controller
public class ChatWebSocketController {

    @Autowired
    private ChatService chatService;

    @Autowired
    private UserService userService;

    @Autowired
    private CustomerServiceService customerServiceService;

    @Autowired
    private SimpMessagingTemplate messagingTemplate;

    /**
     * 处理发送消息
     */
    @MessageMapping("/chat.sendMessage/{roomCode}")
    public void sendMessage(@DestinationVariable String roomCode, 
                           @Payload ChatMessageRequest request, 
                           Principal principal) {
        try {
            // 获取当前用户
            WebSocketAuthInterceptor.WebSocketUserPrincipal userPrincipal = 
                (WebSocketAuthInterceptor.WebSocketUserPrincipal) principal;
            User currentUser = userPrincipal.getUser();

            // 获取聊天室
            ChatRoom chatRoom = chatService.getChatRoomByCode(roomCode);
            if (chatRoom == null) {
                log.warn("聊天室不存在，房间编码: {}", roomCode);
                return;
            }

            // 验证用户权限（使用新的权限检查方法）
            if (!customerServiceService.hasRoomPermission(currentUser.getId(),
                    chatRoom.getUserId(), chatRoom.getCustomerServiceId())) {
                log.warn("用户无权限在此聊天室发送消息，用户ID: {}, 房间编码: {}", currentUser.getId(), roomCode);
                return;
            }

            // 确定发送者类型（使用客服服务检查）
            Integer senderType = chatRoom.getUserId().equals(currentUser.getId()) ?
                SenderTypeEnum.USER.getType() : SenderTypeEnum.CUSTOMER_SERVICE.getType();

            // 发送消息
            ChatMessage message = chatService.sendMessage(
                chatRoom.getId(),
                currentUser.getId(),
                senderType,
                MessageTypeEnum.TEXT.getType(),
                request.getContent()
            );

            // 构建响应消息
            ChatMessageResponse response = new ChatMessageResponse();
            response.setId(message.getId());
            response.setRoomCode(roomCode);
            response.setSenderId(currentUser.getId());
            response.setSenderName(currentUser.getNickName() != null ? currentUser.getNickName() : currentUser.getUserName());
            response.setSenderAvatar(currentUser.getAvatar());
            response.setSenderType(senderType);
            response.setMessageType(message.getMessageType());
            response.setContent(message.getContent());
            response.setCreateTime(message.getCreateTime());

            // 广播消息到聊天室
            messagingTemplate.convertAndSend("/topic/chat/" + roomCode, response);

            log.info("消息发送成功，房间编码: {}, 发送者: {}, 内容: {}", roomCode, currentUser.getUserName(), request.getContent());

        } catch (Exception e) {
            log.error("发送消息失败，房间编码: {}, 错误: {}", roomCode, e.getMessage(), e);
        }
    }

    /**
     * 处理加入聊天室
     */
    @MessageMapping("/chat.joinRoom/{roomCode}")
    public void joinRoom(@DestinationVariable String roomCode, Principal principal) {
        try {
            WebSocketAuthInterceptor.WebSocketUserPrincipal userPrincipal = 
                (WebSocketAuthInterceptor.WebSocketUserPrincipal) principal;
            User currentUser = userPrincipal.getUser();

            // 获取聊天室
            ChatRoom chatRoom = chatService.getChatRoomByCode(roomCode);
            if (chatRoom == null) {
                log.warn("聊天室不存在，房间编码: {}", roomCode);
                return;
            }

            // 标记消息为已读
            chatService.markMessagesAsRead(chatRoom.getId(), currentUser.getId());

            // 发送加入通知
            Map<String, Object> joinNotification = new HashMap<>();
            joinNotification.put("type", "USER_JOINED");
            joinNotification.put("userId", currentUser.getId());
            joinNotification.put("userName", currentUser.getNickName() != null ? currentUser.getNickName() : currentUser.getUserName());
            joinNotification.put("message", currentUser.getUserName() + " 加入了聊天");

            messagingTemplate.convertAndSend("/topic/chat/" + roomCode, joinNotification);

            log.info("用户加入聊天室，用户: {}, 房间编码: {}", currentUser.getUserName(), roomCode);

        } catch (Exception e) {
            log.error("加入聊天室失败，房间编码: {}, 错误: {}", roomCode, e.getMessage(), e);
        }
    }

    /**
     * 订阅聊天室
     */
    @SubscribeMapping("/topic/chat/{roomCode}")
    public void subscribeToRoom(@DestinationVariable String roomCode, Principal principal) {
        log.info("用户订阅聊天室，房间编码: {}", roomCode);
    }

    /**
     * 检查是否为客服
     */
    private boolean isCustomerService(Long userId, Long customerServiceId) {
        return customerServiceId != null && customerServiceId.equals(userId);
    }

    /**
     * 聊天消息请求
     */
    public static class ChatMessageRequest {
        private String content;

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }
    }

    /**
     * 聊天消息响应
     */
    public static class ChatMessageResponse {
        private Long id;
        private String roomCode;
        private Long senderId;
        private String senderName;
        private String senderAvatar;
        private Integer senderType;
        private Integer messageType;
        private String content;
        private java.util.Date createTime;

        // Getters and Setters
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }
        public String getRoomCode() { return roomCode; }
        public void setRoomCode(String roomCode) { this.roomCode = roomCode; }
        public Long getSenderId() { return senderId; }
        public void setSenderId(Long senderId) { this.senderId = senderId; }
        public String getSenderName() { return senderName; }
        public void setSenderName(String senderName) { this.senderName = senderName; }
        public String getSenderAvatar() { return senderAvatar; }
        public void setSenderAvatar(String senderAvatar) { this.senderAvatar = senderAvatar; }
        public Integer getSenderType() { return senderType; }
        public void setSenderType(Integer senderType) { this.senderType = senderType; }
        public Integer getMessageType() { return messageType; }
        public void setMessageType(Integer messageType) { this.messageType = messageType; }
        public String getContent() { return content; }
        public void setContent(String content) { this.content = content; }
        public java.util.Date getCreateTime() { return createTime; }
        public void setCreateTime(java.util.Date createTime) { this.createTime = createTime; }
    }
}
