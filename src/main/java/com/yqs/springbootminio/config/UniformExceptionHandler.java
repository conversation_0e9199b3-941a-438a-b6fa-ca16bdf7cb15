package com.yqs.springbootminio.config;

import com.yqs.springbootminio.exception.BizException;
import com.yqs.springbootminio.util.CommonResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * <AUTHOR>
 * @date 2025/6/23
 * @description
 */
@RestControllerAdvice
public class UniformExceptionHandler {
    private static final Logger logger = LoggerFactory.getLogger(UniformExceptionHandler.class);

    @ExceptionHandler(BizException.class)
    @ResponseStatus(HttpStatus.OK)
    public <T> CommonResult<T> sendBizExceptionResponse(BizException bizException){
        logger.error("接口调用发生业务异常, errorCode:{}", bizException.getErrorCode(), bizException);
        return CommonResult.failed(bizException.getErrorCode(), bizException.getMessage());
    }

    /**
     * 处理Spring Security的访问拒绝异常
     * 重新抛出异常，让Spring Security的AccessDeniedHandler处理
     */
    @ExceptionHandler(AccessDeniedException.class)
    public void handleAccessDeniedException(AccessDeniedException ex) throws AccessDeniedException {
        // 重新抛出异常，让Spring Security的JwtAccessDeniedHandler处理
        throw ex;
    }

    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public <T> CommonResult<T> sendSysExceptionResponse(Exception exception){
        if(exception instanceof BizException){
            BizException bizException = (BizException) exception;
            logger.error("接口调用发生业务异常, errorCode:{}", bizException.getErrorCode(), bizException);
            return this.sendBizExceptionResponse((BizException)exception);
        }
        logger.error("接口调用发生系统异常", exception);
        return CommonResult.failed(exception.getMessage());
    }
}
