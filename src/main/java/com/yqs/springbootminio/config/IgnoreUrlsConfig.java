package com.yqs.springbootminio.config;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/23
 * @description 白名单URL配置类，支持多种配置方式
 */
@Getter
@Configuration
@Slf4j
public class IgnoreUrlsConfig {

    @Value("${secure.ignored.urls:}")
    private String urlsString;

    private List<String> urls = new ArrayList<>();

    @PostConstruct
    public void init() {
        if (urlsString != null && !urlsString.trim().isEmpty()) {
            // 支持逗号分隔的配置
            String[] urlArray = urlsString.split(",");
            this.urls = new ArrayList<>();
            for (String url : urlArray) {
                String trimmedUrl = url.trim();
                if (!trimmedUrl.isEmpty()) {
                    this.urls.add(trimmedUrl);
                }
            }
        }

        // 确保包含基本的静态资源路径
        if (!urls.contains("/static/**")) {
            urls.add("/static/**");
        }
        if (!urls.contains("/chat-test.html")) {
            urls.add("/chat-test.html");
        }
        if (!urls.contains("/*.html")) {
            urls.add("/*.html");
        }

        log.info("白名单URLs: " + urls);
    }
}
