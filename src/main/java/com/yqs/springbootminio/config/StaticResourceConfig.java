package com.yqs.springbootminio.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * 静态资源配置类
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Configuration
@Slf4j
public class StaticResourceConfig implements WebMvcConfigurer {

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 配置静态资源映射
        registry.addResourceHandler("/static/**")
                .addResourceLocations("classpath:/static/");
        
        // 专门为HTML文件配置映射
        registry.addResourceHandler("/*.html")
                .addResourceLocations("classpath:/static/");
        
        // 专门为chat-test.html配置映射
        registry.addResourceHandler("/chat-test.html")
                .addResourceLocations("classpath:/static/");
        
        // 配置其他可能的静态资源
        registry.addResourceHandler("/css/**")
                .addResourceLocations("classpath:/static/css/");
        
        registry.addResourceHandler("/js/**")
                .addResourceLocations("classpath:/static/js/");
        
        registry.addResourceHandler("/images/**")
                .addResourceLocations("classpath:/static/images/");
                
        log.info("静态资源配置已加载");
    }
}
