package com.yqs.springbootminio.controller;

import com.yqs.springbootminio.model.Employee;
import com.yqs.springbootminio.param.EmployeeParam;
import com.yqs.springbootminio.param.EmployeeVO;
import com.yqs.springbootminio.service.EmployeeService;
import com.yqs.springbootminio.util.CommonResult;
import com.yqs.springbootminio.util.CopyUtil;
import com.yqs.springbootminio.util.Page;
import com.yqs.springbootminio.util.PageView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/5/15
 * @description
 */
@RestController
@RequestMapping("/api/employee")
@Api(tags = "员工管理", description = "提供员工相关的分页查询等操作")
public class EmployeeController {

    @Autowired
    private EmployeeService employeeService;

    @PostMapping("/pageList")
    @ApiOperation("分页查询员工信息")
    @PreAuthorize("hasAuthority('employee:pageList')")
    public CommonResult<PageView<EmployeeVO>> pageList(@RequestBody EmployeeParam employeeParam) {
        Page page = employeeParam.getPage();
        PageView<Employee> employeePageView = employeeService.pageList(page.getCurrentPage(), page.getPageSize());
        List<EmployeeVO> employeeVOList = employeePageView.getRecords().stream().map(employee -> {
            return CopyUtil.copy(employee, EmployeeVO.class);
        }).collect(Collectors.toList());
        PageView<EmployeeVO> pageViewVO = new PageView<>(employeeVOList, employeePageView.getPage());
        return CommonResult.success(pageViewVO);
    }


}