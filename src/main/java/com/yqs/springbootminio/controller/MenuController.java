package com.yqs.springbootminio.controller;

import com.yqs.springbootminio.model.Menu;
import com.yqs.springbootminio.service.MenuService;
import com.yqs.springbootminio.util.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/23
 * @description 菜单管理控制器
 */
@RestController
@RequestMapping("/api/menu")
@Api(tags = "菜单管理", description = "提供菜单相关的查询操作")
public class MenuController {

    @Autowired
    private MenuService menuService;

    /**
     * 根据用户ID查询用户拥有的菜单权限
     *
     * @param userId 用户ID
     * @return 菜单列表
     */
    @GetMapping("/user/{userId}")
    @ApiOperation("根据用户ID查询用户拥有的菜单权限")
    @PreAuthorize("hasAuthority('menu:query')")
    public CommonResult<List<Menu>> getMenusByUserId(
            @ApiParam(value = "用户ID", required = true) @PathVariable Long userId) {
        List<Menu> menus = menuService.getMenusByUserId(userId);
        return CommonResult.success(menus);
    }

    /**
     * 查询单个菜单信息
     *
     * @param menuId 菜单ID
     * @return 菜单信息
     */
    @GetMapping("/{menuId}")
    @ApiOperation("根据菜单ID查询菜单信息")
    @PreAuthorize("hasAuthority('menu:query')")
    public CommonResult<Menu> getMenuById(
            @ApiParam(value = "菜单ID", required = true) @PathVariable Long menuId) {
        Menu menu = menuService.queryById(menuId);
        return CommonResult.success(menu);
    }
}
