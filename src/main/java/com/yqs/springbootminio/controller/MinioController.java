package com.yqs.springbootminio.controller;
import cn.hutool.extra.spring.SpringUtil;
import com.yqs.springbootminio.model.Employee;
import com.yqs.springbootminio.service.EmployeeService;
import com.yqs.springbootminio.service.StorageServiceContext;
import com.yqs.springbootminio.util.CommonResult;
import io.minio.MinioClient;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/minio")
@Api(tags = "MinIO 文件管理", description = "提供 MinIO 文件上传、下载、删除等操作")
public class MinioController {

    private static final Logger log = LoggerFactory.getLogger(MinioController.class);

    @Autowired
    private MinioClient minioClient;

    @Autowired
    private StorageServiceContext storageServiceContext;

    @Override
    protected Object clone() throws CloneNotSupportedException {
        return super.clone();
    }
//    @Autowired
//    private SpringUtil springUtil;

    @Value("${minio.bucket-name}")
    private String bucketName;

    @PostMapping("/upload")
    @ApiOperation("上传文件到 MinIO")
    public CommonResult<String> uploadFile(
        @ApiParam(name = "file", value = "要上传的文件", required = true)
        @RequestParam("file") MultipartFile file) throws Exception {
        String newFileName = storageServiceContext.getActiveService().uploadFile(file.getOriginalFilename(), file.getInputStream());
        return CommonResult.success("File uploaded successfully, new file name: " + newFileName);
    }

    @GetMapping("/download/{fileName}")
    @ApiOperation("从 MinIO 下载文件")
    public CommonResult<Void> downloadFile(
        @ApiParam(name = "fileName", value = "要下载的文件名", required = true)
        @PathVariable String fileName) {
        byte[] bytes = storageServiceContext.getActiveService().downloadFile(fileName);
        // 由于CommonResult无法直接包含byte[]数据，这里返回null作为占位符
        return CommonResult.success();
    }

    @DeleteMapping("/delete/{fileName}")
    @ApiOperation("从 MinIO 删除文件")
    public CommonResult<Void> deleteFile(
        @ApiParam(name = "fileName", value = "要删除的文件名", required = true)
        @PathVariable String fileName) {
        storageServiceContext.getActiveService().deleteFile(fileName);
        return CommonResult.success();
    }

    @GetMapping("/hello")
    @ApiOperation("测试接口")
    public CommonResult<String> hello() {
        EmployeeService employeeServiceImpl = SpringUtil.getBean("employeeServiceImpl");
        Employee employee = employeeServiceImpl.load(1);
        return CommonResult.success("Hello from MinioController!");
    }
}