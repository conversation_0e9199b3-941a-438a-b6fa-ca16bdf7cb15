package com.yqs.springbootminio.controller;

import com.yqs.springbootminio.enums.CustomerServiceStatusEnum;
import com.yqs.springbootminio.model.AdminUserDetails;
import com.yqs.springbootminio.model.CustomerService;
import com.yqs.springbootminio.model.User;
import com.yqs.springbootminio.service.CustomerServiceService;
import com.yqs.springbootminio.util.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 客服管理控制器
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Slf4j
@RestController
@RequestMapping("/api/customer-service")
@Api(tags = "客服管理", description = "提供客服相关的管理操作")
public class CustomerServiceController {

    @Autowired
    private CustomerServiceService customerServiceService;

    /**
     * 获取客服信息
     */
    @GetMapping("/info")
    @ApiOperation("获取当前用户的客服信息")
    public CommonResult<CustomerService> getCustomerServiceInfo() {
        try {
            User currentUser = getCurrentUser();
            if (currentUser == null) {
                return CommonResult.failed("401", "用户未登录");
            }

            CustomerService customerService = customerServiceService.queryByUserId(currentUser.getId());
            if (customerService == null) {
                return CommonResult.failed("404", "当前用户不是客服");
            }

            return CommonResult.success(customerService);

        } catch (Exception e) {
            log.error("获取客服信息失败", e);
            return CommonResult.failed("500", "获取客服信息失败：" + e.getMessage());
        }
    }

    /**
     * 更新客服状态
     */
    @PostMapping("/status")
    @ApiOperation("更新客服状态")
    public CommonResult<String> updateStatus(
            @ApiParam(value = "状态：0-离线，1-在线，2-忙碌", required = true) @RequestParam Integer status) {
        try {
            User currentUser = getCurrentUser();
            if (currentUser == null) {
                return CommonResult.failed("401", "用户未登录");
            }

            CustomerService customerService = customerServiceService.queryByUserId(currentUser.getId());
            if (customerService == null) {
                return CommonResult.failed("404", "当前用户不是客服");
            }

            // 验证状态值
            CustomerServiceStatusEnum statusEnum = CustomerServiceStatusEnum.getByStatus(status);
            if (statusEnum == null) {
                return CommonResult.failed("400", "无效的状态值");
            }

            customerServiceService.updateStatus(customerService.getId(), status);
            return CommonResult.success("状态更新成功");

        } catch (Exception e) {
            log.error("更新客服状态失败", e);
            return CommonResult.failed("500", "更新客服状态失败：" + e.getMessage());
        }
    }

    /**
     * 获取所有在线客服
     */
    @GetMapping("/online")
    @ApiOperation("获取所有在线客服")
    public CommonResult<List<CustomerService>> getOnlineServices() {
        try {
            List<CustomerService> onlineServices = customerServiceService.getOnlineServices();
            return CommonResult.success(onlineServices);

        } catch (Exception e) {
            log.error("获取在线客服列表失败", e);
            return CommonResult.failed("500", "获取在线客服列表失败：" + e.getMessage());
        }
    }

    /**
     * 获取可用客服
     */
    @GetMapping("/available")
    @ApiOperation("获取可用客服")
    public CommonResult<List<CustomerService>> getAvailableServices() {
        try {
            List<CustomerService> availableServices = customerServiceService.getAvailableServices();
            return CommonResult.success(availableServices);

        } catch (Exception e) {
            log.error("获取可用客服列表失败", e);
            return CommonResult.failed("500", "获取可用客服列表失败：" + e.getMessage());
        }
    }

    /**
     * 创建客服
     */
    @PostMapping("/create")
    @ApiOperation("创建客服")
    public CommonResult<String> createCustomerService(@RequestBody CustomerService customerService) {
        try {
            User currentUser = getCurrentUser();
            if (currentUser == null) {
                return CommonResult.failed("401", "用户未登录");
            }

            // 这里可以添加管理员权限验证
            // if (!isAdmin(currentUser)) {
            //     return CommonResult.failed("403", "无权限执行此操作");
            // }

            // 检查用户是否已经是客服
            CustomerService existingService = customerServiceService.queryByUserId(customerService.getUserId());
            if (existingService != null) {
                return CommonResult.failed("400", "该用户已经是客服");
            }

            // 设置默认值
            if (customerService.getMaxConcurrentChats() == null) {
                customerService.setMaxConcurrentChats(10);
            }
            if (customerService.getCurrentChatCount() == null) {
                customerService.setCurrentChatCount(0);
            }
            if (customerService.getStatus() == null) {
                customerService.setStatus(CustomerServiceStatusEnum.OFFLINE.getStatus());
            }

            customerServiceService.insert(customerService);
            return CommonResult.success("客服创建成功");

        } catch (Exception e) {
            log.error("创建客服失败", e);
            return CommonResult.failed("500", "创建客服失败：" + e.getMessage());
        }
    }

    /**
     * 删除客服
     */
    @DeleteMapping("/{id}")
    @ApiOperation("删除客服")
    public CommonResult<String> deleteCustomerService(
            @ApiParam(value = "客服ID", required = true) @PathVariable Long id) {
        try {
            User currentUser = getCurrentUser();
            if (currentUser == null) {
                return CommonResult.failed("401", "用户未登录");
            }

            // 这里可以添加管理员权限验证
            // if (!isAdmin(currentUser)) {
            //     return CommonResult.failed("403", "无权限执行此操作");
            // }

            customerServiceService.deleteById(id);
            return CommonResult.success("客服删除成功");

        } catch (Exception e) {
            log.error("删除客服失败", e);
            return CommonResult.failed("500", "删除客服失败：" + e.getMessage());
        }
    }

    /**
     * 获取当前登录用户
     */
    private User getCurrentUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated()) {
            return null;
        }

        Object principal = authentication.getPrincipal();
        if (!(principal instanceof AdminUserDetails)) {
            return null;
        }

        AdminUserDetails userDetails = (AdminUserDetails) principal;
        return userDetails.getAdminUser();
    }
}
