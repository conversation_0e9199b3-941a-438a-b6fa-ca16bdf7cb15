package com.yqs.springbootminio.controller;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 聊天测试控制器
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Controller
public class ChatTestController {

    /**
     * 简单的测试接口
     */
    @GetMapping("/test")
    @ResponseBody
    public String test() {
        return "测试接口正常工作！";
    }

    /**
     * API测试接口 - 用于前端连接测试
     */
    @GetMapping("/api/test")
    @ResponseBody
    public String apiTest() {
        return "API测试接口正常工作！";
    }

    /**
     * 聊天测试页面 - 方式1
     */
    @GetMapping("/chat-test")
    @ResponseBody
    public String chatTest() {
        return getChatTestHtml();
    }

    /**
     * 聊天测试页面 - 方式2
     */
    @GetMapping("/chat-test.html")
    @ResponseBody
    public String chatTestHtml() {
        return getChatTestHtml();
    }

    /**
     * 聊天测试页面 - 方式3
     */
    @GetMapping("/chat-page")
    @ResponseBody
    public String chatPage() {
        return getChatTestHtml();
    }

    /**
     * 获取聊天测试页面HTML内容
     */
    private String getChatTestHtml() {
        return "<!DOCTYPE html>\n" +
                "<html lang=\"zh-CN\">\n" +
                "<head>\n" +
                "    <meta charset=\"UTF-8\">\n" +
                "    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n" +
                "    <title>在线聊天测试</title>\n" +
                "    <script src=\"https://cdn.jsdelivr.net/npm/sockjs-client@1.6.1/dist/sockjs.min.js\"></script>\n" +
                "    <script src=\"https://cdn.jsdelivr.net/npm/stompjs@2.3.3/lib/stomp.min.js\"></script>\n" +
                "    <style>\n" +
                "        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }\n" +
                "        .chat-container { border: 1px solid #ddd; border-radius: 8px; height: 400px; overflow-y: auto; padding: 10px; margin-bottom: 10px; background-color: #f9f9f9; }\n" +
                "        .message { margin-bottom: 10px; padding: 8px; border-radius: 4px; }\n" +
                "        .message.user { background-color: #007bff; color: white; text-align: right; }\n" +
                "        .message.service { background-color: #28a745; color: white; text-align: left; }\n" +
                "        .message.system { background-color: #6c757d; color: white; text-align: center; font-style: italic; }\n" +
                "        .input-container { display: flex; gap: 10px; }\n" +
                "        .input-container input { flex: 1; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }\n" +
                "        .input-container button { padding: 8px 16px; background-color: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }\n" +
                "        .status { margin-bottom: 10px; padding: 8px; border-radius: 4px; background-color: #e9ecef; }\n" +
                "        .connected { background-color: #d4edda; color: #155724; }\n" +
                "        .disconnected { background-color: #f8d7da; color: #721c24; }\n" +
                "        .info { background-color: #d1ecf1; color: #0c5460; padding: 10px; border-radius: 4px; margin-bottom: 10px; }\n" +
                "    </style>\n" +
                "</head>\n" +
                "<body>\n" +
                "    <h1>在线聊天测试页面</h1>\n" +
                "    \n" +
                "    <div class=\"info\">\n" +
                "        <strong>使用说明：</strong><br>\n" +
                "        1. 先通过登录接口 POST /api/user/login 获取JWT Token<br>\n" +
                "        2. 将Token粘贴到下面的输入框中（不需要Bearer前缀）<br>\n" +
                "        3. 点击\"连接\"建立WebSocket连接<br>\n" +
                "        4. 点击\"创建聊天室\"创建新的聊天室<br>\n" +
                "        5. 点击\"加入聊天室\"开始聊天<br>\n" +
                "        6. 在输入框中输入消息并发送\n" +
                "    </div>\n" +
                "    \n" +
                "    <div class=\"status\" id=\"status\">未连接</div>\n" +
                "    \n" +
                "    <div>\n" +
                "        <label>JWT Token:</label>\n" +
                "        <input type=\"text\" id=\"tokenInput\" placeholder=\"请输入JWT Token\" style=\"width: 100%; margin-bottom: 10px;\">\n" +
                "        <button onclick=\"connect()\">连接</button>\n" +
                "        <button onclick=\"disconnect()\">断开连接</button>\n" +
                "    </div>\n" +
                "    \n" +
                "    <div>\n" +
                "        <label>房间编码:</label>\n" +
                "        <input type=\"text\" id=\"roomCodeInput\" placeholder=\"房间编码（自动生成）\" style=\"width: 100%; margin-bottom: 10px;\">\n" +
                "        <button onclick=\"createRoom()\">创建聊天室</button>\n" +
                "        <button onclick=\"joinRoom()\">加入聊天室</button>\n" +
                "        <button onclick=\"requestService()\">请求客服</button>\n" +
                "    </div>\n" +
                "    \n" +
                "    <div class=\"chat-container\" id=\"chatContainer\"></div>\n" +
                "    \n" +
                "    <div class=\"input-container\">\n" +
                "        <input type=\"text\" id=\"messageInput\" placeholder=\"输入消息...\" onkeypress=\"handleKeyPress(event)\">\n" +
                "        <button onclick=\"sendMessage()\">发送</button>\n" +
                "    </div>\n" +
                "\n" +
                "    <script>\n" +
                "        let stompClient = null;\n" +
                "        let currentRoomCode = null;\n" +
                "        let currentToken = null;\n" +
                "\n" +
                "        function connect() {\n" +
                "            const token = document.getElementById('tokenInput').value;\n" +
                "            if (!token) {\n" +
                "                alert('请输入JWT Token');\n" +
                "                return;\n" +
                "            }\n" +
                "            \n" +
                "            currentToken = token;\n" +
                "            const socket = new SockJS('/ws/chat');\n" +
                "            stompClient = Stomp.over(socket);\n" +
                "            \n" +
                "            const headers = {\n" +
                "                'Authorization': token\n" +
                "            };\n" +
                "            \n" +
                "            stompClient.connect(headers, function (frame) {\n" +
                "                updateStatus('已连接', true);\n" +
                "                console.log('Connected: ' + frame);\n" +
                "                addMessage('系统', 'WebSocket连接成功', 'system');\n" +
                "            }, function (error) {\n" +
                "                updateStatus('连接失败: ' + error, false);\n" +
                "                console.log('Connection error: ' + error);\n" +
                "                addMessage('系统', '连接失败: ' + error, 'system');\n" +
                "            });\n" +
                "        }\n" +
                "\n" +
                "        function disconnect() {\n" +
                "            if (stompClient !== null) {\n" +
                "                stompClient.disconnect();\n" +
                "            }\n" +
                "            updateStatus('已断开连接', false);\n" +
                "            addMessage('系统', 'WebSocket连接已断开', 'system');\n" +
                "        }\n" +
                "\n" +
                "        function createRoom() {\n" +
                "            if (!currentToken) {\n" +
                "                alert('请先连接WebSocket');\n" +
                "                return;\n" +
                "            }\n" +
                "            \n" +
                "            const authHeader = currentToken;\n" +
                "            \n" +
                "            fetch('/api/chat/room', {\n" +
                "                method: 'POST',\n" +
                "                headers: {\n" +
                "                    'Authorization': authHeader,\n" +
                "                    'Content-Type': 'application/json'\n" +
                "                }\n" +
                "            })\n" +
                "            .then(response => response.json())\n" +
                "            .then(data => {\n" +
                "                console.log('创建聊天室响应:', data);\n" +
                "                if (data.code === 200) {\n" +
                "                    currentRoomCode = data.data.roomCode;\n" +
                "                    document.getElementById('roomCodeInput').value = currentRoomCode;\n" +
                "                    addMessage('系统', '聊天室创建成功: ' + currentRoomCode, 'system');\n" +
                "                } else {\n" +
                "                    alert('创建聊天室失败: ' + data.message);\n" +
                "                    addMessage('系统', '创建聊天室失败: ' + data.message, 'system');\n" +
                "                }\n" +
                "            })\n" +
                "            .catch(error => {\n" +
                "                console.error('Error:', error);\n" +
                "                alert('创建聊天室失败: ' + error.message);\n" +
                "                addMessage('系统', '创建聊天室失败: ' + error.message, 'system');\n" +
                "            });\n" +
                "        }\n" +
                "\n" +
                "        function joinRoom() {\n" +
                "            const roomCode = document.getElementById('roomCodeInput').value;\n" +
                "            if (!roomCode) {\n" +
                "                alert('请输入房间编码');\n" +
                "                return;\n" +
                "            }\n" +
                "            \n" +
                "            if (stompClient === null || !stompClient.connected) {\n" +
                "                alert('请先连接WebSocket');\n" +
                "                return;\n" +
                "            }\n" +
                "            \n" +
                "            currentRoomCode = roomCode;\n" +
                "            \n" +
                "            // 订阅聊天室消息\n" +
                "            stompClient.subscribe('/topic/chat/' + roomCode, function (message) {\n" +
                "                const messageData = JSON.parse(message.body);\n" +
                "                console.log('收到消息:', messageData);\n" +
                "                if (messageData.type === 'USER_JOINED') {\n" +
                "                    addMessage('系统', messageData.message, 'system');\n" +
                "                } else {\n" +
                "                    const senderType = messageData.senderType === 1 ? 'user' : 'service';\n" +
                "                    addMessage(messageData.senderName || '未知', messageData.content, senderType);\n" +
                "                }\n" +
                "            });\n" +
                "            \n" +
                "            // 发送加入房间消息\n" +
                "            stompClient.send(\"/app/chat.joinRoom/\" + roomCode, {}, JSON.stringify({}));\n" +
                "            \n" +
                "            addMessage('系统', '已加入聊天室: ' + roomCode, 'system');\n" +
                "            \n" +
                "            // 加载聊天历史\n" +
                "            loadChatHistory(roomCode);\n" +
                "        }\n" +
                "\n" +
                "        function requestService() {\n" +
                "            if (!currentRoomCode) {\n" +
                "                alert('请先加入聊天室');\n" +
                "                return;\n" +
                "            }\n" +
                "            \n" +
                "            const authHeader = currentToken;\n" +
                "            \n" +
                "            fetch('/api/chat/auto-assign/' + currentRoomCode, {\n" +
                "                method: 'POST',\n" +
                "                headers: {\n" +
                "                    'Authorization': authHeader,\n" +
                "                    'Content-Type': 'application/json'\n" +
                "                }\n" +
                "            })\n" +
                "            .then(response => response.json())\n" +
                "            .then(data => {\n" +
                "                console.log('请求客服响应:', data);\n" +
                "                if (data.code === 200) {\n" +
                "                    addMessage('系统', '客服分配成功，请等待客服回复', 'system');\n" +
                "                } else {\n" +
                "                    addMessage('系统', '客服分配失败: ' + data.message, 'system');\n" +
                "                }\n" +
                "            })\n" +
                "            .catch(error => {\n" +
                "                console.error('Error:', error);\n" +
                "                addMessage('系统', '请求客服失败: ' + error.message, 'system');\n" +
                "            });\n" +
                "        }\n" +
                "\n" +
                "        function loadChatHistory(roomCode) {\n" +
                "            const authHeader = currentToken;\n" +
                "            \n" +
                "            fetch('/api/chat/history/' + roomCode + '?page=1&size=50', {\n" +
                "                headers: {\n" +
                "                    'Authorization': authHeader\n" +
                "                }\n" +
                "            })\n" +
                "            .then(response => response.json())\n" +
                "            .then(data => {\n" +
                "                console.log('聊天历史:', data);\n" +
                "                if (data.code === 200) {\n" +
                "                    const messages = data.data.messages;\n" +
                "                    messages.forEach(msg => {\n" +
                "                        const senderType = msg.senderType === 1 ? 'user' : 'service';\n" +
                "                        addMessage('历史-' + (msg.senderType === 1 ? '用户' : '客服'), msg.content, senderType);\n" +
                "                    });\n" +
                "                }\n" +
                "            })\n" +
                "            .catch(error => {\n" +
                "                console.error('Error loading chat history:', error);\n" +
                "            });\n" +
                "        }\n" +
                "\n" +
                "        function sendMessage() {\n" +
                "            const messageInput = document.getElementById('messageInput');\n" +
                "            const message = messageInput.value.trim();\n" +
                "            \n" +
                "            if (!message) {\n" +
                "                return;\n" +
                "            }\n" +
                "            \n" +
                "            if (!currentRoomCode) {\n" +
                "                alert('请先加入聊天室');\n" +
                "                return;\n" +
                "            }\n" +
                "            \n" +
                "            if (stompClient === null || !stompClient.connected) {\n" +
                "                alert('WebSocket未连接');\n" +
                "                return;\n" +
                "            }\n" +
                "            \n" +
                "            stompClient.send(\"/app/chat.sendMessage/\" + currentRoomCode, {}, JSON.stringify({\n" +
                "                'content': message\n" +
                "            }));\n" +
                "            \n" +
                "            messageInput.value = '';\n" +
                "        }\n" +
                "\n" +
                "        function addMessage(sender, content, type) {\n" +
                "            const chatContainer = document.getElementById('chatContainer');\n" +
                "            const messageDiv = document.createElement('div');\n" +
                "            messageDiv.className = 'message ' + type;\n" +
                "            messageDiv.innerHTML = '<strong>' + sender + ':</strong> ' + content + ' <small>(' + new Date().toLocaleTimeString() + ')</small>';\n" +
                "            chatContainer.appendChild(messageDiv);\n" +
                "            chatContainer.scrollTop = chatContainer.scrollHeight;\n" +
                "        }\n" +
                "\n" +
                "        function updateStatus(message, connected) {\n" +
                "            const statusDiv = document.getElementById('status');\n" +
                "            statusDiv.textContent = message;\n" +
                "            statusDiv.className = 'status ' + (connected ? 'connected' : 'disconnected');\n" +
                "        }\n" +
                "\n" +
                "        function handleKeyPress(event) {\n" +
                "            if (event.key === 'Enter') {\n" +
                "                sendMessage();\n" +
                "            }\n" +
                "        }\n" +
                "    </script>\n" +
                "</body>\n" +
                "</html>";
    }
}
