package com.yqs.springbootminio.controller;

import com.yqs.springbootminio.model.AdminUserDetails;
import com.yqs.springbootminio.model.User;
import com.yqs.springbootminio.param.UserParam;
import com.yqs.springbootminio.service.UserService;
import com.yqs.springbootminio.util.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/5/14
 * @description
 */
@RestController
@RequestMapping("/api/user")
@Api(tags = "用户管理", description = "提供用户相关的CRUD操作")
public class UserController {

    @Autowired
    private UserService userService;

    @ApiOperation("用户登录")
    @PostMapping("/login")
    public CommonResult<Map<String, String>> login(@RequestBody UserParam userParam) {
        String jwtToken = userService.login(userParam.getUserName(), userParam.getPassword());
        Map<String, String> tokenMap = new HashMap<>();
        // 直接返回JWT Token，不添加Bearer前缀
        tokenMap.put("token", jwtToken);
        return CommonResult.success(tokenMap);
    }

    @ApiOperation("获取用户信息")
    @GetMapping("/getInfo")
    public CommonResult<User> getInfo(){
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated()) {
            return CommonResult.failed("401", "未认证");
        }

        Object principal = authentication.getPrincipal();
        if (!(principal instanceof AdminUserDetails)) {
            return CommonResult.failed("401", "认证信息无效");
        }

        AdminUserDetails userDetails = (AdminUserDetails) principal;
        User user = userDetails.getAdminUser();

        // 不返回密码等敏感信息
        user.setPassword(null);
        return CommonResult.success(user);
    }

    @ApiOperation("用户登出")
    @PostMapping("/logout")
    public CommonResult<String> logout() {
        // 清除SecurityContext
        SecurityContextHolder.clearContext();
        return CommonResult.success("登出成功");
    }

}
