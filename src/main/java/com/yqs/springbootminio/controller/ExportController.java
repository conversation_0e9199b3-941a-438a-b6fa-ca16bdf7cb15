package com.yqs.springbootminio.controller;

import com.yqs.springbootminio.enums.ExportStatus;
import com.yqs.springbootminio.model.ExportTask;
import com.yqs.springbootminio.service.ExportService;
import com.yqs.springbootminio.util.CommonResult;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.server.ResponseStatusException;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.Collections;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/6/14
 * @description 导出控制器
 */
@RestController
@RequestMapping("/api/export")
public class ExportController {
    @Autowired
    private ExportService exportService;

    // 创建导出任务
    @PostMapping("/create")
    @PreAuthorize("hasAuthority('employee:export')")
    @ApiOperation(value = "创建导出任务", notes = "创建一个导出任务并返回任务ID")
    public CommonResult<Map<String, Object>> createExport() throws InterruptedException {
        String taskId = exportService.createTask();
        if (taskId == null) {
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "创建任务失败");
        }
        return CommonResult.success(Collections.singletonMap("taskId", taskId));
    }

    // 查询导出进度
    @GetMapping("/progress/{taskId}")
    @ApiOperation(value = "查询导出进度", notes = "查询指定导出任务的进度")
    public CommonResult<Map<String, Object>> getProgress(@PathVariable String taskId) {
        ExportTask task = exportService.getTask(taskId);
        if (task == null) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "任务不存在");
        }

        return CommonResult.success(Map.of(
                "progress", task.getProgress(),
                "status", task.getStatus().name()
        ));
    }

    // 下载文件
    @GetMapping("/download/{taskId}")
    @ApiOperation(value = "下载文件", notes = "下载指定导出任务的文件")
    public void downloadFile(@PathVariable String taskId, HttpServletResponse response) {
        try {
            ExportTask task = exportService.getTask(taskId);
            if (task == null || task.getStatus() != ExportStatus.COMPLETED) {
                response.sendError(HttpStatus.NOT_FOUND.value());
                return;
            }

            File file = task.getOutputFile();

            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment; filename=employee_export.xlsx");
            response.setContentLength((int) file.length());

            // 流式传输文件内容
            try (InputStream is = new FileInputStream(file);
                 OutputStream os = response.getOutputStream()) {
                byte[] buffer = new byte[4096];
                int bytesRead;
                while ((bytesRead = is.read(buffer)) != -1) {
                    os.write(buffer, 0, bytesRead);
                }
                os.flush();
            }

            // 删除临时文件
            file.delete();
        } catch (IOException e) {
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "文件下载失败");
        }
    }

}
