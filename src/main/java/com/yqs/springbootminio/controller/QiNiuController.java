package com.yqs.springbootminio.controller;

import com.yqs.springbootminio.service.StorageServiceContext;
import com.yqs.springbootminio.util.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping("/api/qiniu")
@Api(tags = "七牛云文件管理", description = "提供七牛云文件上传、下载、删除等操作")
public class QiNiuController {
    @Autowired
    private StorageServiceContext storageServiceContext;

    @PostMapping("/upload")
    @ApiOperation("上传文件到七牛云")
    public CommonResult<String> uploadToQiniu(
        @ApiParam(name = "file", value = "要上传的文件", required = true)
        @RequestParam("file") MultipartFile file) throws Exception {
        if (file.isEmpty()) {
            throw new RuntimeException("File is empty");
        }
        String newFileName = storageServiceContext.getActiveService().uploadFile(file.getOriginalFilename(), file.getInputStream());
        return CommonResult.success("File uploaded successfully, newFileName: " + newFileName);
    }

    @GetMapping("/download/{fileName}")
    @ApiOperation("从七牛云下载文件")
    public CommonResult<Void> downloadFile(
        @ApiParam(name = "fileName", value = "要下载的文件名", required = true)
        @PathVariable String fileName) {
        try {
            byte[] fileBytes = storageServiceContext.getActiveService().downloadFile(fileName);
            // 由于CommonResult无法直接包含byte[]数据，这里返回null作为占位符
            return CommonResult.success();
        } catch (Exception e) {
            return CommonResult.failed("File download failed: " + e.getMessage());
        }
    }

    /**
     * 删除文件接口
     *
     * @param fileName 要删除的文件名
     * @return 包含删除结果的响应实体
     */
    @DeleteMapping("/delete/{fileName}")
    @ApiOperation("从七牛云删除文件")
    public CommonResult<String> deleteFile(
        @ApiParam(name = "fileName", value = "要删除的文件名", required = true)
        @PathVariable String fileName) {
        try {
            storageServiceContext.getActiveService().deleteFile(fileName);
            return CommonResult.success("File deleted successfully: " + fileName);
        } catch (Exception e) {
            return CommonResult.failed("File deletion failed: " + e.getMessage());
        }
    }
}