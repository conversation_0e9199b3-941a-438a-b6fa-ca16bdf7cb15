//package com.yqs.springbootminio.example;
//
//import java.util.ArrayList;
//
///**
// * <AUTHOR>
// * @date 2025/4/22
// * @description
// */
//public class Duck extends AbstractAnimal{
//
//    private static Duck duck;
//    @Override
//    public void eat() {
//
//    }
//
////    public String getName(){
////        return "Duck";
////    }
//
//    public static void main (String[] args) {
//        int i = 0;
//        int j = i++;
//        int k = ++i;
//        System.out.println(i);
//        System.out.println(j);
//        System.out.println(k);
//
//        ArrayList<String> list = new ArrayList<>();
//
//
//    }
//}
