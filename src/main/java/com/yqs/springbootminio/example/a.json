domainsNotWorking=z-lib.fm%2Ccnlib.icu%2Carticles24t2d47kb6rbabobokvrnymh2smkleosntcu6qxou6sxewyd.onion%2Cquasieconomist.org%2Cz-lib.bz%2Cbookszlibb74ugqojhzhg2a63w5i2atv5bqarulgczawnbmsb6s6qead.onion%2Cforthriver.xyz%2Chomeonet.tech;
remix_userkey=f2242babd88dc745c51a4298517a6534;
remix_userid=9758672;
selectedSiteMode=books;
prefers_color_scheme=light;
siteLanguage=zh;

z-lib.fm%2Ccnlib.icu%2Carticles24t2d47kb6rbabobokvrnymh2smkleosntcu6qxou6sxewyd.onion%2Cquasieconomist.org%2Cz-lib.bz%2Cbookszlibb74ugqojhzhg2a63w5i2atv5bqarulgczawnbmsb6s6qead.onion%2Cforthriver.xyz%2Chomeonet.tech

curl --location --request POST 'https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation' \
--header 'Content-Type: application/json;charset=utf-8' \
--data-raw '{
"model": "baichuan2-7b-chat-v1",
"input": {
"messages":[
{
"role": "system",
"content": "You are a helpful assistant."
},
{
"role": "user",
"content": "你好，请介绍一下蔡徐坤"
}
]
},
"parameters": {
"result_format": "message"
}
}'





mcp_server_mysql": {
"command": "/path/to/node",
"args": [
"/full/path/to/mcp-server-mysql/dist/index.js"
],
"env": {
"MYSQL_HOST": "127.0.0.1",
"MYSQL_PORT": "3306",
"MYSQL_USER": "root",
"MYSQL_PASS": "your_password",
"MYSQL_DB": "your_database",
"ALLOW_INSERT_OPERATION": "false",
"ALLOW_UPDATE_OPERATION": "false",
"ALLOW_DELETE_OPERATION": "false",
"PATH": "/Users/<USER>/Library/Application Support/Herd/config/nvm/versions/node/v22.9.0/bin:/usr/bin:/bin", // <--- Important to add the following, run in your terminal `echo "$(which node)/../"` to get the path
"NODE_PATH": "/Users/<USER>/Library/Application Support/Herd/config/nvm/versions/node/v22.9.0/lib/node_modules" // <--- Important to add the following, run in your terminal `echo "$(which node)/../../lib/node_modules"`
}
}
}