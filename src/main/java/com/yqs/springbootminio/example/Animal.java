package com.yqs.springbootminio.example;

/**
 * <AUTHOR>
 * @date 2025/4/30
 * @description
 */
public class Animal {
    public String name;
    int age;

    public Animal(String name, int age) {
        this.name = name;
        this.age = age;
    }

    public Animal() {
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getAge() {
        return age;
    }

    public void setAge(int age) {
        this.age = age;
    }




    protected void eat(){
        System.out.println("吃");
    }

    void breathe(){
        System.out.println("呼吸");
    }

    private void claw(){
        System.out.println("抓");
    }
}
