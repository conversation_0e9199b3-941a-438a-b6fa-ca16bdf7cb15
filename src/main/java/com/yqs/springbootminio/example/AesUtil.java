package com.yqs.springbootminio.example;

import cn.hutool.core.codec.Base64;
import cn.hutool.crypto.Mode;
import cn.hutool.crypto.Padding;
import cn.hutool.crypto.symmetric.AES;

import java.nio.charset.StandardCharsets;

public class AesUtil {
    // 密钥（必须为 16、24 或 32 字节）
    private static final String KEY = "MGJK2021MGJK2021";
    // 偏移量 IV（CBC 模式下需要 16 字节）
    //private static final String IV = "initial-vector!!";

    // 初始化 AES 实例（使用 CBC 模式）
//    private static final AES AES_INSTANCE = new AES(Mode.CBC, Padding.PKCS5Padding,
//            KEY.getBytes(StandardCharsets.UTF_8),
//            IV.getBytes(StandardCharsets.UTF_8));

    private static final AES AES_INSTANCE = new AES(Mode.ECB, Padding.PKCS5Padding,
            KEY.getBytes(StandardCharsets.UTF_8));

    /**
     * 加密方法
     *
     * @param content 明文内容
     * @return Base64 编码的加密字符串
     */
    public static String encrypt(String content) {
        byte[] encrypted = AES_INSTANCE.encrypt(content);
        return Base64.encode(encrypted);
    }

    /**
     * 解密方法
     *
     * @param encryptedContent Base64 编码的加密字符串
     * @return 解密后的明文
     */
    public static String decrypt(String encryptedContent) {
        byte[] decrypted = AES_INSTANCE.decrypt(Base64.decode(encryptedContent));
        return new String(decrypted, StandardCharsets.UTF_8);
    }
}
