package com.yqs.springbootminio.example;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;

public class TimeDifferenceCalculator {

    public static void calculateTimeDifference() {
        // 定义目标时间
        LocalDateTime targetTime = LocalDateTime.parse("2020-05-05 20:00:00", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

        // 获取当前时间
        LocalDateTime now = LocalDateTime.now();

        // 计算时间差
        Duration duration = Duration.between(targetTime, now);

        // 转换为天、小时、分钟和秒
        long days = duration.toDays();
        long hours = duration.toHours() % 24;
        long minutes = duration.toMinutes() % 60;
        long seconds = duration.getSeconds() % 60;

        // 输出结果
        System.out.println("从相识到现在已经过去了：" + days + "天" + hours + "小时" + minutes + "分钟" + seconds + "秒");
    }

    public static void main(String[] args) {
        calculateTimeDifference(); // 调用方法测试
        System.out.println(new Date());
    }
}
