package com.yqs.springbootminio.example;

/**
 * <AUTHOR>
 * @date 2025/4/25
 * @description
 */
public class Demo {

    public static void main(String[] args) {
        ClassLoader classLoader = String.class.getClassLoader();
        System.out.println(classLoader);
        System.out.println(Singleton.class.getClassLoader());
        String message = "本人目前是一名学习编程的开发人员，也是一个喜欢分享交流的人，" +
                "想加入该论坛与网友们一起分享自己学习技术的所感所得，加入论坛结交更多有趣之" +
                "人对我来说也是提升个人眼界的一种方式。希望能给个机会加入论坛。";
        System.out.println(message.length());
//        System.out.println(Base64.encode("{\"id\":138982,\"session\":\"ae3671f42d2e4e5bb8d0e3e2ead3bc8f\"}"));
//        System.out.println(Base64Decoder.decodeStr("eyJpZCI6MTM4OTgyLCJzZXNzaW9uIjoiYWUzNjcxZjQyZDJlNGU1YmI4ZDBlM2UyZWFkM2JjOGYifQ"));
//        System.out.println(Base64Decoder.decodeStr("eyJpZCI6MTM4OTgyLCJzZXNzaW9uIjoiYWUzNjcxZjQyZDJlNGU1YmI4ZDBlM2UyZWFkM2JjOGYifQ=="));

        /*AES aes = new AES();
        byte[] encrypt = aes.encrypt("123456");
        String str = StrUtil.str(encrypt, "UTF-8");
        System.out.println(str);
        System.out.println(new String(encrypt));*/

        String encrypt = AesUtil.encrypt("123456");
        System.out.println(encrypt);
        //System.out.println(AesUtil.decrypt("IrSoH8Ow2InfyzhWxclymA=="));


    }
}
