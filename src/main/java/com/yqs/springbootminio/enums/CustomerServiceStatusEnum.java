package com.yqs.springbootminio.enums;

/**
 * 客服状态枚举
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
public enum CustomerServiceStatusEnum {
    OFFLINE(0, "离线"),
    ONLINE(1, "在线"),
    BUSY(2, "忙碌");

    private final Integer status;
    private final String description;

    CustomerServiceStatusEnum(Integer status, String description) {
        this.status = status;
        this.description = description;
    }

    public Integer getStatus() {
        return status;
    }

    public String getDescription() {
        return description;
    }

    public static CustomerServiceStatusEnum getByStatus(Integer status) {
        for (CustomerServiceStatusEnum statusEnum : values()) {
            if (statusEnum.getStatus().equals(status)) {
                return statusEnum;
            }
        }
        return null;
    }
}
