package com.yqs.springbootminio.enums;

/**
 * 聊天室状态枚举
 *
 * <AUTHOR>
 * @since 2025-06-24
 */
public enum ChatRoomStatusEnum {
    CLOSED(0, "关闭"),
    ACTIVE(1, "活跃"),
    WAITING_SERVICE(2, "等待客服");

    private final Integer status;
    private final String description;

    ChatRoomStatusEnum(Integer status, String description) {
        this.status = status;
        this.description = description;
    }

    public Integer getStatus() {
        return status;
    }

    public String getDescription() {
        return description;
    }

    public static ChatRoomStatusEnum getByStatus(Integer status) {
        for (ChatRoomStatusEnum statusEnum : values()) {
            if (statusEnum.getStatus().equals(status)) {
                return statusEnum;
            }
        }
        return null;
    }
}
