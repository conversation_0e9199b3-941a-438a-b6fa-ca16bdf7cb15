package com.yqs.springbootminio.enums;

/**
 * 消息类型枚举
 *
 * <AUTHOR>
 * @since 2025-06-24
 */
public enum MessageTypeEnum {
    TEXT(1, "文本"),
    IMAGE(2, "图片"),
    FILE(3, "文件");

    private final Integer type;
    private final String description;

    MessageTypeEnum(Integer type, String description) {
        this.type = type;
        this.description = description;
    }

    public Integer getType() {
        return type;
    }

    public String getDescription() {
        return description;
    }

    public static MessageTypeEnum getByType(Integer type) {
        for (MessageTypeEnum typeEnum : values()) {
            if (typeEnum.getType().equals(type)) {
                return typeEnum;
            }
        }
        return null;
    }
}
