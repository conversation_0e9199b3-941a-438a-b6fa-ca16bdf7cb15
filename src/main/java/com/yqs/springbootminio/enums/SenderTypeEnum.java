package com.yqs.springbootminio.enums;

/**
 * 发送者类型枚举
 *
 * <AUTHOR>
 * @since 2025-06-24
 */
public enum SenderTypeEnum {
    USER(1, "用户"),
    CUSTOMER_SERVICE(2, "客服");

    private final Integer type;
    private final String description;

    SenderTypeEnum(Integer type, String description) {
        this.type = type;
        this.description = description;
    }

    public Integer getType() {
        return type;
    }

    public String getDescription() {
        return description;
    }

    public static SenderTypeEnum getByType(Integer type) {
        for (SenderTypeEnum typeEnum : values()) {
            if (typeEnum.getType().equals(type)) {
                return typeEnum;
            }
        }
        return null;
    }
}
