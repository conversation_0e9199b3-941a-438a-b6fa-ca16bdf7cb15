package com.yqs.springbootminio.dao;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/5/10
 * @description
 */
public class EmployeeDO {
    private Integer id;
    private String name;
    private Integer age;
    // 新增字段
    private Integer gender;
    private Date createTime;
    private Date updateTime;

    public EmployeeDO() {
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getAge() {
        return age;
    }

    public void setAge(Integer age) {
        this.age = age;
    }
    // 新增getter和setter方法
    public Integer getGender() {
        return gender;
    }

    public void setGender(Integer gender) {
        this.gender = gender;
    }

    public java.util.Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(java.util.Date createTime) {
        this.createTime = createTime;
    }

    public java.util.Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(java.util.Date updateTime) {
        this.updateTime = updateTime;
    }
}