package com.yqs.springbootminio.dao;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 客服表
 * @TableName customer_service
 */
@Data
public class CustomerServiceDO implements Serializable {
    /**
     * 客服ID
     */
    private Long id;

    /**
     * 关联用户ID
     */
    private Long userId;

    /**
     * 客服名称
     */
    private String serviceName;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 状态：0-离线，1-在线，2-忙碌
     */
    private Integer status;

    /**
     * 最大并发聊天数
     */
    private Integer maxConcurrentChats;

    /**
     * 当前聊天数
     */
    private Integer currentChatCount;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}