package com.yqs.springbootminio.example2;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/20
 * @description
 */
public class Cat {

    public String name;
    public int age;

    public Cat() {
        System.out.println("Cat的无参构造方法");
    }

    public Cat(String name, int age) {
        this.name = name;
        this.age = age;
        System.out.println(name +"Cat的有参构造方法");
    }
}

class SmallCat extends Cat{

    public SmallCat(String name, int age) {
        super(name, age);
    }

    public static void main(String[] args) {
        Cat cat = new SmallCat("小猫", 1);
    }
}
class BigCat extends Cat{

    public <T> T get(Class<T> clazz) throws Exception{
        return clazz.getDeclaredConstructor().newInstance();
    }

    public <T> List<T> getList(){
        return new ArrayList<>();
    }

    public List<Object> getList(int i){
        return new ArrayList<>();
    }

    public static void main(String[] args) throws Exception {
//        BigCat bigCat = new BigCat();
//        Object o = bigCat.get(Object.class);
//        System.out.println(o.equals("ss"));
        BigCat bigCat = new BigCat();
        List<Object> list = bigCat.getList();
        list.add("a");
        list.add(12);
        System.out.println(list.size());

        BigCat bigCat2 = new BigCat();
        List<Object> list2 = bigCat2.getList();
        list2.add(11);
        list2.add(false);
        list2.add("b");
        list2.forEach(System.out::println);
    }

}

class Foo{
    private String name = "a";
    private int age = 18;

    public static void main(String[] args) {
        int count = 0;
        String name="hello";
        Foo foo1 = new Foo();
        Foo foo2 = new Foo();
        operate(foo1, foo2, count,  name);
        System.out.println(foo1.name+" " + foo1.age);//b 20
        System.out.println(foo2.name+" " + foo2.age);//a 18
        System.out.println(count);
        System.out.println(name);


    }
    static void operate(Foo foo1, Foo foo2, int count, String name){
        count++;
        name = "world";
        foo1.name = "b";
        foo1.age = 20;
        foo2 = new Foo();

    }
}