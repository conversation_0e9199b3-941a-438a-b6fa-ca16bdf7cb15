package com.yqs.springbootminio.example2;

import java.io.File;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @date 2025/4/18
 * @description
 */
public class Test1 {

    public static void main(String[] args) {
        System.out.println(UUID.randomUUID().toString());
        System.out.println(UUID.randomUUID().toString().replace("-", ""));
        System.out.println(File.separator);

        int dotIndex = "fileName.png".lastIndexOf('.');
        if (dotIndex > 0) {
            String fileExtension = "fileName.png".substring(dotIndex);
            System.out.println(fileExtension);
        }

        AtomicInteger atomicInteger = new AtomicInteger(0);
        for (int i = 0; i < 10; i++) {
            atomicInteger.incrementAndGet();
        }
        System.out.println(atomicInteger.get());


    }


}

class Test2 {

    TreeSet<Integer> set1 = new TreeSet<>();
    TreeSet<Integer> set2 = new TreeSet<>();

    public void test() {
        set1.add(1);
        set2.add(2);
        SortedSet<Integer> set3 = set1.subSet(3, 4);
        System.out.println(set3);
    }

    public static void main(String[] args) {
        //new Test2().test();
        // 按字符串长度排序
        Set<String> customTreeSet = new TreeSet<>(Comparator.comparing(String::length));
        customTreeSet.add("Apple");
        customTreeSet.add("Banana");
        customTreeSet.add("Peach");

        System.out.println(customTreeSet);  // 输出 [Peach, Apple, Banana]
    }

}

class Test3 {
    public void add(byte b) {
        b = b++;
    }

    public void test() {
        byte a = 120;
        byte b = 120;
        add(++a);
        System.out.print(a + " ");
        add(b);
        System.out.print(b + "");
    }

    public static void main(String[] args) {
        new Test3().test();
    }
}

class Test4 {

    int i = 5;

    public static void main(String[] args) {
        Test4 testA = new Test4();
        testA.i = 10;
        testA.test(testA);
        System.out.println(testA.i);//10
        int[] arr = {1, 2, 3};
        char c = 'a';
        System.out.println(Character.toString(c));

    }

    public void test(Test4 test4) {
        test4 = new Test4();
        System.out.println(test4.i);//5
    }
}

class Test5 {
    public static void main(String[] args) throws InterruptedException {
        Thread worker = new Thread(() -> {
            System.out.println("工作线程开始执行");
            // 模拟工作
            try {
                //Thread.sleep(2000);
            } catch (Exception e) {
            }
            System.out.println("工作线程执行完毕");
        });

        worker.start();
        System.out.println("主线程等待工作线程完成");
        //worker.join(); // 子线程加入，主线程在此阻塞直到子线程执行完毕
        System.out.println("主线程继续执行");
    }
}

class Test6 {
    String s;
    public static void main(String[] args) throws Exception {
//        Thread worker = new Thread(() -> {
//            System.out.println("工作线程即将挂起");
//            LockSupport.park(); // 挂起当前线程
//            System.out.println("工作线程被唤醒");
//        });
//
//        worker.start();
//
//        Thread.sleep(2000);
//        System.out.println("主线程唤醒工作线程");
//        LockSupport.unpark(worker); // 发放许可

//        String s1 = "helloworld";
//        String s2 = "hello";
//        String s3 = (s2+"world").intern();
//        System.out.println(s1==s3);
        Integer a=1;
        Double b=1.0;
        //System.out.println(a == b);
        System.out.println("s=" +new Test6().s);

    }
    protected class A{

    }
}



