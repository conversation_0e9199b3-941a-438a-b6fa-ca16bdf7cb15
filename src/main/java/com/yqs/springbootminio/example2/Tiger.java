package com.yqs.springbootminio.example2;

/**
 * <AUTHOR>
 * @date 2025/6/21
 * @description
 */
public class Tiger {
    private String name;

    public Tiger() {
        System.out.println("Tiger constructor");
    }

    public Tiger(String name) {
        this.name = name;
    }
}

class SmallTiger extends Tiger {

    public SmallTiger(String name) {
    }

    public static void main(String[] args) {
        SmallTiger smallTiger = new SmallTiger("小莱湖");
    }
}
