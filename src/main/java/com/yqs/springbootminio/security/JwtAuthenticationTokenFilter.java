package com.yqs.springbootminio.security;

import cn.hutool.core.util.StrUtil;
import com.yqs.springbootminio.enums.UserStatusEnum;
import com.yqs.springbootminio.model.AdminUserDetails;
import com.yqs.springbootminio.model.Menu;
import com.yqs.springbootminio.model.User;
import com.yqs.springbootminio.service.MenuService;
import com.yqs.springbootminio.service.UserService;
import com.yqs.springbootminio.util.JwtTokenUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/23
 * @description
 */
@Component
@Slf4j
public class JwtAuthenticationTokenFilter extends OncePerRequestFilter {
    @Autowired
    private JwtTokenUtil jwtTokenUtil;
    @Autowired
    private UserService userService;
    @Autowired
    private MenuService menuService;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain chain) throws ServletException, IOException {
        String authorizationToken = request.getHeader("Authorization");
        Long userId = jwtTokenUtil.getUserIdFromToken(authorizationToken);
        if (userId != null && jwtTokenUtil.validateTokenCheckExpire(authorizationToken, userId)) {
            // 检查是否已经认证过（避免重复查询数据库）
            Authentication existingAuth = SecurityContextHolder.getContext().getAuthentication();
            long threadId = Thread.currentThread().getId();
            String threadName = Thread.currentThread().getName();

            log.info("=== JWT Filter Debug ===");
            log.info("Thread ID: {}, Thread Name: {}", threadId, threadName);
            log.info("Request URI: {}", request.getRequestURI());
            log.info("Existing Authentication: {}", existingAuth);
            log.info("Session ID: {}", request.getSession(false) != null ? request.getSession().getId() : "No Session");

            if (existingAuth != null && existingAuth.isAuthenticated() &&
                existingAuth.getPrincipal() instanceof AdminUserDetails) {
                AdminUserDetails existingUserDetails = (AdminUserDetails) existingAuth.getPrincipal();
                if (existingUserDetails.getAdminUser().getId().equals(userId)) {
                    // 已经认证过同一用户，直接跳过
                    return;
                }
            }

            // 查询用户信息
            User loginUser = userService.queryById(userId);
            if (loginUser == null || UserStatusEnum.NOT_ACTIVE.getStatus() == loginUser.getStatus()) {
                // 用户不存在或未激活，清除认证信息
                SecurityContextHolder.clearContext();
                return;
            }

            AdminUserDetails userDetails = new AdminUserDetails(loginUser);

            // 权限处理
            List<Menu> menuList = menuService.getMenusByUserId(userId);
            Set<String> perms = menuList.stream()
                .map(Menu::getPermission)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toSet());
            userDetails.setAuthorities(perms);

            UsernamePasswordAuthenticationToken authenticationToken =
                new UsernamePasswordAuthenticationToken(userDetails, null, userDetails.getAuthorities());
            authenticationToken.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
            SecurityContextHolder.getContext().setAuthentication(authenticationToken);
        }
        chain.doFilter(request, response);


    }
}
