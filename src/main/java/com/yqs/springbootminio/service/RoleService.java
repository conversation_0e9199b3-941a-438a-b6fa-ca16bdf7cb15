package com.yqs.springbootminio.service;

import com.yqs.springbootminio.model.Role;

/**
 * 角色表服务接口
 *
 * <AUTHOR>
 * @date 2025/5/14
 * @description
 */
public interface RoleService {
    /**
     * 通过ID查询单条数据
     *
     * @param roleId 主键
     * @return 实例对象
     */
    Role queryById(Long roleId);

    /**
     * 新增数据
     *
     * @param role 实例对象
     * @return 影响行数
     */
    int insert(Role role);

    /**
     * 修改数据
     *
     * @param role 实例对象
     * @return 影响行数
     */
    int update(Role role);

    /**
     * 通过主键删除数据
     *
     * @param roleId 主键
     * @return 影响行数
     */
    int deleteById(Long roleId);
}
