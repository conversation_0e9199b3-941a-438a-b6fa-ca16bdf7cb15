package com.yqs.springbootminio.service;

import com.yqs.springbootminio.enums.ExportStatus;
import com.yqs.springbootminio.model.Employee;
import com.yqs.springbootminio.model.ExportTask;
import com.yqs.springbootminio.util.PageView;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileOutputStream;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @date 2025/6/14
 * @description 任务管理服务
 */
@Service
public class ExportService {
    private final Map<String, ExportTask> taskMap = new ConcurrentHashMap<>();
    private final ExecutorService executor = Executors.newCachedThreadPool();

    @Autowired
    private EmployeeService employeeService;

    // 创建导出任务
    public String createTask() throws InterruptedException {
        String taskId = UUID.randomUUID().toString();
        ExportTask task = new ExportTask();
        task.setTaskId(taskId);
        taskMap.put(taskId, task);

        // 异步执行导出
        executor.submit(() -> performExport(task));
        return taskId;
    }

    // 执行导出
    private void performExport(ExportTask task) {
        try {
            // 1. 准备临时文件
            File file = File.createTempFile("export_", ".xlsx");
            task.setOutputFile(file);

            // 2. 创建Excel工作簿
            try (SXSSFWorkbook workbook = new SXSSFWorkbook(100)) {
                Sheet sheet = workbook.createSheet("员工信息");

                // 创建表头
                Row headerRow = sheet.createRow(0);
                String[] headers = {"ID", "姓名", "年龄", "性别", "创建时间"};
                for (int i = 0; i < headers.length; i++) {
                    headerRow.createCell(i).setCellValue(headers[i]);
                }

                AtomicInteger processedCount = new AtomicInteger(0);
                // 3. 大数据量导出（从数据库分页查询）
                int totalRecords = employeeService.getCount(); // 假设总数据量
                // 每次查询200条数据
                int pageSize = 200;

                for (int page = 0; page < (totalRecords + pageSize - 1) / pageSize; page++) {
                    // 分页查询数据
                    PageView<Employee> employeePageView = employeeService.pageList(page + 1, pageSize);
                    List<Employee> data = employeePageView.getRecords();

                    // 写入数据
                    for (int i = 0; i < data.size(); i++) {
                        Employee emp = data.get(i);
                        Row row = sheet.createRow(page * pageSize + i + 1);
                        row.createCell(0).setCellValue(emp.getId());
                        row.createCell(1).setCellValue(emp.getName());
                        row.createCell(2).setCellValue(emp.getAge());
                        row.createCell(3).setCellValue(emp.getGender());
                        row.createCell(4).setCellValue(emp.getCreateTime().toString());
                    }

                    // 更新进度
                    //task.setProgress((page + 1) * 100 / (totalRecords / pageSize));

                    int progress = (page + 1) * 100 / ((totalRecords + pageSize - 1) / pageSize);
                    //int progress = (processedCount.incrementAndGet() * 100) / totalRecords;
                    task.setProgress(progress);
                    //Thread.sleep(100); // 模拟处理延迟
                }

                // 4. 写入文件
                try (FileOutputStream fos = new FileOutputStream(file)) {
                    workbook.write(fos);
                }
            }

            // 5. 标记任务完成
            task.setProgress(100);
            task.setStatus(ExportStatus.COMPLETED);
        } catch (Exception e) {
            task.setStatus(ExportStatus.FAILED);
        }
    }

    private List<Employee> mockData(int page, int pageSize) {
        // 使用EmployeeService进行分页查询
        PageView<Employee> employeePageView = employeeService.pageList(page, pageSize);
        return employeePageView.getRecords();
    }

    // 获取任务状态
    public ExportTask getTask(String taskId) {
        return taskMap.get(taskId);
    }
}
