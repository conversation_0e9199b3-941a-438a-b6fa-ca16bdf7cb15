package com.yqs.springbootminio.service;

import com.yqs.springbootminio.model.UsersRoles;

/**
 * 用户角色关联服务接口
 *
 * <AUTHOR>
 * @date 2025/5/14
 * @description
 */
public interface UsersRolesService {
    /**
     * 通过用户ID查询单条数据
     *
     * @param userId 主键
     * @return 实例对象
     */
    UsersRoles queryById(Long userId);

    /**
     * 新增数据
     *
     * @param usersRoles 实例对象
     * @return 影响行数
     */
    int insert(UsersRoles usersRoles);

    /**
     * 修改数据
     *
     * @param usersRoles 实例对象
     * @return 影响行数
     */
    int update(UsersRoles usersRoles);

    /**
     * 通过用户ID删除数据
     *
     * @param userId 主键
     * @return 影响行数
     */
    int deleteById(Long userId);
}
