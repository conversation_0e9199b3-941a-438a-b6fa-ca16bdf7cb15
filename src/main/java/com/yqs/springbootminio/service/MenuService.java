package com.yqs.springbootminio.service;

import com.yqs.springbootminio.model.Menu;

import java.util.List;

/**
 * 系统菜单服务接口
 *
 * <AUTHOR>
 * @date 2025/5/14
 * @description
 */
public interface MenuService {
    /**
     * 通过ID查询单条数据
     *
     * @param menuId 主键
     * @return 实例对象
     */
    Menu queryById(Long menuId);

    /**
     * 新增数据
     *
     * @param menu 实例对象
     * @return 影响行数
     */
    int insert(Menu menu);

    /**
     * 修改数据
     *
     * @param menu 实例对象
     * @return 影响行数
     */
    int update(Menu menu);

    /**
     * 通过主键删除数据
     *
     * @param menuId 主键
     * @return 影响行数
     */
    int deleteById(Long menuId);

    /**
     * 根据用户ID查询用户拥有的菜单权限
     *
     * @param userId 用户ID
     * @return 菜单列表
     */
    List<Menu> getMenusByUserId(Long userId);
}
