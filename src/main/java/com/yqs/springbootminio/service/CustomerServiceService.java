package com.yqs.springbootminio.service;

import com.yqs.springbootminio.model.CustomerService;

import java.util.List;

/**
 * 客服服务接口
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
public interface CustomerServiceService {

    /**
     * 根据ID查询客服信息
     *
     * @param id 客服ID
     * @return 客服信息
     */
    CustomerService queryById(Long id);

    /**
     * 根据用户ID查询客服信息
     *
     * @param userId 用户ID
     * @return 客服信息
     */
    CustomerService queryByUserId(Long userId);

    /**
     * 查询所有在线客服
     *
     * @return 在线客服列表
     */
    List<CustomerService> getOnlineServices();

    /**
     * 查询可用的客服（在线且未达到最大并发数）
     *
     * @return 可用客服列表
     */
    List<CustomerService> getAvailableServices();

    /**
     * 自动分配客服
     *
     * @return 分配的客服，如果没有可用客服则返回null
     */
    CustomerService autoAssignService();

    /**
     * 新增客服
     *
     * @param customerService 客服信息
     * @return 影响行数
     */
    int insert(CustomerService customerService);

    /**
     * 更新客服信息
     *
     * @param customerService 客服信息
     * @return 影响行数
     */
    int update(CustomerService customerService);

    /**
     * 删除客服
     *
     * @param id 客服ID
     * @return 影响行数
     */
    int deleteById(Long id);

    /**
     * 更新客服状态
     *
     * @param id     客服ID
     * @param status 状态
     * @return 影响行数
     */
    int updateStatus(Long id, Integer status);

    /**
     * 增加客服当前聊天数
     *
     * @param id 客服ID
     * @return 影响行数
     */
    int incrementChatCount(Long id);

    /**
     * 减少客服当前聊天数
     *
     * @param id 客服ID
     * @return 影响行数
     */
    int decrementChatCount(Long id);

    /**
     * 检查用户是否为客服
     *
     * @param userId 用户ID
     * @return 是否为客服
     */
    boolean isCustomerService(Long userId);

    /**
     * 检查用户是否有权限访问指定聊天室
     *
     * @param userId            用户ID
     * @param roomUserId        聊天室用户ID
     * @param customerServiceId 聊天室客服ID
     * @return 是否有权限
     */
    boolean hasRoomPermission(Long userId, Long roomUserId, Long customerServiceId);
}
