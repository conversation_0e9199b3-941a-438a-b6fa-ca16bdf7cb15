package com.yqs.springbootminio.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Map;

/**
 * <AUTHOR>
 * 策略上下文类 用于根据配置动态选择具体的存储服务实现
 * 使用策略模式，根据配置文件中的activeStorageService属性来选择具体的存储服务
 */
@Component
public class StorageServiceContext {

    private static final Logger logger = LoggerFactory.getLogger(StorageServiceContext.class);
    @Autowired
    private Map<String, FileStorageService> storageServices;
    @Value("${storage.service}")
    private String activeStorageService;
    private FileStorageService fileStorageService;

    @PostConstruct
    public void init() {
        fileStorageService = storageServices.get(activeStorageService);
        if (fileStorageService == null) {
            logger.error("No storage service found for name: {}", activeStorageService);
            throw new IllegalArgumentException("No storage service found for name: " + activeStorageService);
        }
    }

    public FileStorageService getActiveService() {
        return fileStorageService;
    }
}
