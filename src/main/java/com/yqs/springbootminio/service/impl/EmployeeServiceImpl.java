package com.yqs.springbootminio.service.impl;


import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.yqs.springbootminio.dao.EmployeeDO;
import com.yqs.springbootminio.mapper.EmployeeMapper;
import com.yqs.springbootminio.model.Employee;
import com.yqs.springbootminio.service.EmployeeService;
import com.yqs.springbootminio.util.CopyUtil;
import com.yqs.springbootminio.util.Page;
import com.yqs.springbootminio.util.PageView;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/10
 * @description 员工服务实现类
 */
@Service
public class EmployeeServiceImpl implements EmployeeService {

    @Autowired
    private EmployeeMapper employeeMapper;

    @Override
    public Object insert(Employee employee) {
        EmployeeDO employeeDO = CopyUtil.copy(employee, EmployeeDO.class);
        int rows = employeeMapper.insert(employeeDO);
        return getResponse(rows);
    }

    @Override
    public Object delete(int id) {
        int rows = employeeMapper.deleteById(id);
        return getResponse(rows);
    }

    @Override
    public Object update(Employee employee) {
        EmployeeDO employeeDO = CopyUtil.copy(employee, EmployeeDO.class);
        int rows = employeeMapper.update(employeeDO);
        return getResponse(rows);
    }

    @Override
    public Employee load(int id) {
        EmployeeDO employeeDO = employeeMapper.selectById(id);
        return CopyUtil.copy(employeeDO, Employee.class);
    }

    @Override
    public PageView<Employee> pageList(int pageNum, int pagesize) {
        Page page = new Page(pageNum, pagesize);
        // 开启分页
        PageHelper.startPage(pageNum, pagesize);
        // 执行查询（此时查询已自动分页）
        List<EmployeeDO> employeeDOList = employeeMapper.selectAll();
        // 转换为业务对象并创建分页信息
        PageInfo<EmployeeDO> pageInfo = new PageInfo<>(employeeDOList);
        page.setRowCount(pageInfo.getTotal());
        PageView<Employee> pageView = new PageView<>();
        pageView.setPage(page);
        pageView.setRecords(CopyUtil.copyList(employeeDOList, Employee.class));
        return pageView;
    }

    @Override
    public Integer getCount() {
        return employeeMapper.selectCount();
    }

    /**
     * 获取统一的响应结果
     *
     * @param rows 影响的行数
     * @return 响应结果
     */
    private Object getResponse(int rows) {
        if (rows > 0) {
            return "操作成功";
        } else {
            return "操作失败";
        }
    }
}