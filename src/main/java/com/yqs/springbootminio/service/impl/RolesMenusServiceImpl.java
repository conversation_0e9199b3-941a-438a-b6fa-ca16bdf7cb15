package com.yqs.springbootminio.service.impl;

import com.yqs.springbootminio.dao.RolesMenusDO;
import com.yqs.springbootminio.mapper.RolesMenusMapper;
import com.yqs.springbootminio.model.RolesMenus;
import com.yqs.springbootminio.service.RolesMenusService;
import com.yqs.springbootminio.util.CopyUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 角色菜单关联服务实现类
 *
 * <AUTHOR>
 * @date 2025/5/14
 * @description
 */
@Service
public class RolesMenusServiceImpl implements RolesMenusService {

    @Autowired
    private RolesMenusMapper rolesMenusMapper;

    @Override
    public RolesMenus queryById(Long menuId) {
        RolesMenusDO rolesMenusDO = rolesMenusMapper.selectById(menuId);
        return CopyUtil.copy(rolesMenusDO, RolesMenus.class);
    }

    @Override
    public int insert(RolesMenus rolesMenus) {
        RolesMenusDO rolesMenusDO = CopyUtil.copy(rolesMenus, RolesMenusDO.class);
        return rolesMenusMapper.insert(rolesMenusDO);
    }

    @Override
    public int update(RolesMenus rolesMenus) {
        RolesMenusDO rolesMenusDO = CopyUtil.copy(rolesMenus, RolesMenusDO.class);
        return rolesMenusMapper.update(rolesMenusDO);
    }

    @Override
    public int deleteById(Long menuId) {
        return rolesMenusMapper.deleteById(menuId);
    }
}
