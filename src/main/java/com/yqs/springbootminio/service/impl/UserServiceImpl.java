package com.yqs.springbootminio.service.impl;

import com.yqs.springbootminio.dao.UserDO;
import com.yqs.springbootminio.enums.UserStatusEnum;
import com.yqs.springbootminio.exception.BizException;
import com.yqs.springbootminio.mapper.UserMapper;
import com.yqs.springbootminio.model.AdminUserDetails;
import com.yqs.springbootminio.model.Menu;
import com.yqs.springbootminio.model.User;
import com.yqs.springbootminio.service.MenuService;
import com.yqs.springbootminio.service.UserService;
import com.yqs.springbootminio.util.CopyUtil;
import com.yqs.springbootminio.util.JwtTokenUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2025/5/14
 * @description
 */
@Service
public class UserServiceImpl implements UserService {

    @Autowired
    private UserMapper userMapper;
    @Autowired
    private PasswordEncoder passwordEncoder;
    @Autowired
    private JwtTokenUtil jwtTokenUtil;
    @Autowired
    private MenuService menuService;


    @Override
    public User queryById(Long id) {
        UserDO userDO = userMapper.selectById(id);

        return CopyUtil.copy(userDO, User.class);
    }

    @Override
    public int insert(User user) {
        return 0;
    }

    @Override
    public int update(User user) {
        return 0;
    }

    @Override
    public int deleteById(Long id) {
        return 0;
    }

    @Override
    public String login(String userName, String password) {
        // 1. 检查用户是否存在
        User adminUser = queryByUserName(userName);
        if (adminUser == null) {
            throw new BizException(String.valueOf(HttpStatus.UNAUTHORIZED.value()), "用户名或密码错误");
        }

        // 2. 检查用户状态
        if (UserStatusEnum.NOT_ACTIVE.getStatus() == adminUser.getStatus()) {
            throw new BizException(String.valueOf(HttpStatus.FORBIDDEN.value()), "账户未激活，请联系管理员");
        }

        // 3. 验证密码
        AdminUserDetails userDetails = new AdminUserDetails(adminUser);
        if (!passwordEncoder.matches(password, userDetails.getPassword())) {
            throw new BizException(String.valueOf(HttpStatus.UNAUTHORIZED.value()), "用户名或密码错误");
        }

        // 4. 设置权限
//        List<Menu> menuList = menuService.getMenusByUserId(adminUser.getId());
//        Set<String> perms = menuList.stream()
//            .map(Menu::getPermission)
//            .filter(perm -> perm != null && !perm.trim().isEmpty())
//            .collect(java.util.stream.Collectors.toSet());
//        userDetails.setAuthorities(perms);

        // 5. 设置认证信息
        UsernamePasswordAuthenticationToken authenticationToken =
            new UsernamePasswordAuthenticationToken(userDetails, null, userDetails.getAuthorities());
        SecurityContextHolder.getContext().setAuthentication(authenticationToken);

        // 6. 生成JWT token
        String token = jwtTokenUtil.generateToken(userDetails.getAdminUser().getId(), userDetails.getAuthorities());
        return token;
    }

    @Override
    public User queryByUserName(String userName) {
        UserDO userDO = userMapper.selectByUserName(userName);
        return CopyUtil.copy(userDO, User.class);
    }

    @Override
    public List<Menu> getUserMenus(Long userId) {
        return menuService.getMenusByUserId(userId);
    }
}
