package com.yqs.springbootminio.service.impl;

import com.yqs.springbootminio.dao.MenuDO;
import com.yqs.springbootminio.mapper.MenuMapper;
import com.yqs.springbootminio.model.Menu;
import com.yqs.springbootminio.service.MenuService;
import com.yqs.springbootminio.util.CopyUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 系统菜单服务实现类
 *
 * <AUTHOR>
 * @date 2025/5/14
 * @description
 */
@Service
public class MenuServiceImpl implements MenuService {

    @Autowired
    private MenuMapper menuMapper;

    @Override
    public Menu queryById(Long menuId) {
        MenuDO menuDO = menuMapper.selectById(menuId);
        return CopyUtil.copy(menuDO, Menu.class);
    }

    @Override
    public int insert(Menu menu) {
        MenuDO menuDO = CopyUtil.copy(menu, MenuDO.class);
        return menuMapper.insert(menuDO);
    }

    @Override
    public int update(Menu menu) {
        MenuDO menuDO = CopyUtil.copy(menu, MenuDO.class);
        return menuMapper.update(menuDO);
    }

    @Override
    public int deleteById(Long menuId) {
        return menuMapper.deleteById(menuId);
    }

    @Override
    public List<Menu> getMenusByUserId(Long userId) {
        List<MenuDO> menuDOList = menuMapper.selectMenusByUserId(userId);
        return CopyUtil.copyList(menuDOList, Menu.class);
    }
}
