package com.yqs.springbootminio.service.impl;

import com.yqs.springbootminio.service.FileStorageService;
import io.minio.GetObjectArgs;
import io.minio.MinioClient;
import io.minio.PutObjectArgs;
import io.minio.RemoveObjectArgs;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.util.UUID;

/**
 * <AUTHOR>
 */
//@ConditionalOnProperty(value = "minio.enable")
@Service
public class MinioServiceImpl implements FileStorageService {

    @Autowired
    private MinioClient minioClient;

    @Value("${minio.bucket-name}")
    private String bucketName;

    @Override
    public String uploadFile(String fileName, InputStream inputStream) throws Exception {
        String fileExtension = "";
        if (fileName != null) {
            int dotIndex = fileName.lastIndexOf('.');
            if (dotIndex > 0) {
                fileExtension = fileName.substring(dotIndex);
            }
        }
        String newFileName = UUID.randomUUID().toString().replace("-", "");
        newFileName += fileExtension;

        minioClient.putObject(
                PutObjectArgs.builder()
                        .object(newFileName)
                        .bucket(bucketName)
                        .stream(inputStream, inputStream.available(), -1)
                        //.contentType("application/octet-stream")
                        .build());
        return newFileName;
    }

    @Override
    public void deleteFile(String fileName) {
        try {
            minioClient.removeObject(
                    RemoveObjectArgs.builder()
                            .bucket(bucketName)
                            .object(fileName)
                            .build());
        } catch (Exception e) {
            throw new RuntimeException("Error deleting file from Minio", e);
        }
    }

    @Override
    public byte[] downloadFile(String fileName) {
        try (InputStream stream = minioClient.getObject(
                GetObjectArgs.builder()
                        .bucket(bucketName)
                        .object(fileName)
                        .build())) {
            // 你可以将文件内容读取到字节数组或其他格式
            byte[] content = stream.readAllBytes();
            // 返回文件内容或其他处理逻辑
            return content; // 示例：返回文件内容的字符串表示
        } catch (Exception e) {
            throw new RuntimeException("Error downloading file from Minio", e);
        }
    }

}
