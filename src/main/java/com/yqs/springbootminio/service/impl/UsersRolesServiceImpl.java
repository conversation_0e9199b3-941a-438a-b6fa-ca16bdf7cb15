package com.yqs.springbootminio.service.impl;

import com.yqs.springbootminio.dao.UsersRolesDO;
import com.yqs.springbootminio.mapper.UsersRolesMapper;
import com.yqs.springbootminio.model.UsersRoles;
import com.yqs.springbootminio.service.UsersRolesService;
import com.yqs.springbootminio.util.CopyUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 用户角色关联服务实现类
 *
 * <AUTHOR>
 * @date 2025/5/14
 * @description
 */
@Service
public class UsersRolesServiceImpl implements UsersRolesService {

    @Autowired
    private UsersRolesMapper usersRolesMapper;

    @Override
    public UsersRoles queryById(Long userId) {
        UsersRolesDO usersRolesDO = usersRolesMapper.selectById(userId);
        return CopyUtil.copy(usersRolesDO, UsersRoles.class);
    }

    @Override
    public int insert(UsersRoles usersRoles) {
        UsersRolesDO usersRolesDO = CopyUtil.copy(usersRoles, UsersRolesDO.class);
        return usersRolesMapper.insert(usersRolesDO);
    }

    @Override
    public int update(UsersRoles usersRoles) {
        UsersRolesDO usersRolesDO = CopyUtil.copy(usersRoles, UsersRolesDO.class);
        return usersRolesMapper.update(usersRolesDO);
    }

    @Override
    public int deleteById(Long userId) {
        return usersRolesMapper.deleteById(userId);
    }
}
