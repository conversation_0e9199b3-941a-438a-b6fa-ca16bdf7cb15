package com.yqs.springbootminio.service.impl;

import com.yqs.springbootminio.dao.CustomerServiceDO;
import com.yqs.springbootminio.enums.CustomerServiceStatusEnum;
import com.yqs.springbootminio.mapper.CustomerServiceMapper;
import com.yqs.springbootminio.model.CustomerService;
import com.yqs.springbootminio.service.CustomerServiceService;
import com.yqs.springbootminio.util.CopyUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 客服服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Slf4j
@Service
public class CustomerServiceServiceImpl implements CustomerServiceService {

    @Autowired
    private CustomerServiceMapper customerServiceMapper;

    @Override
    public CustomerService queryById(Long id) {
        CustomerServiceDO customerServiceDO = customerServiceMapper.selectById(id);
        return CopyUtil.copy(customerServiceDO, CustomerService.class);
    }

    @Override
    public CustomerService queryByUserId(Long userId) {
        CustomerServiceDO customerServiceDO = customerServiceMapper.selectByUserId(userId);
        return CopyUtil.copy(customerServiceDO, CustomerService.class);
    }

    @Override
    public List<CustomerService> getOnlineServices() {
        List<CustomerServiceDO> customerServiceDOList = customerServiceMapper.selectOnlineServices();
        return customerServiceDOList.stream()
                .map(customerServiceDO -> CopyUtil.copy(customerServiceDO, CustomerService.class))
                .collect(Collectors.toList());
    }

    @Override
    public List<CustomerService> getAvailableServices() {
        List<CustomerServiceDO> customerServiceDOList = customerServiceMapper.selectAvailableServices();
        return customerServiceDOList.stream()
                .map(customerServiceDO -> CopyUtil.copy(customerServiceDO, CustomerService.class))
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public CustomerService autoAssignService() {
        List<CustomerService> availableServices = getAvailableServices();
        if (availableServices.isEmpty()) {
            log.warn("没有可用的客服");
            return null;
        }

        // 选择当前聊天数最少的客服
        CustomerService selectedService = availableServices.get(0);
        
        // 增加客服的当前聊天数
        incrementChatCount(selectedService.getId());
        
        log.info("自动分配客服成功，客服ID: {}, 客服名称: {}", selectedService.getId(), selectedService.getServiceName());
        return selectedService;
    }

    @Override
    public int insert(CustomerService customerService) {
        CustomerServiceDO customerServiceDO = CopyUtil.copy(customerService, CustomerServiceDO.class);
        return customerServiceMapper.insert(customerServiceDO);
    }

    @Override
    public int update(CustomerService customerService) {
        CustomerServiceDO customerServiceDO = CopyUtil.copy(customerService, CustomerServiceDO.class);
        return customerServiceMapper.update(customerServiceDO);
    }

    @Override
    public int deleteById(Long id) {
        return customerServiceMapper.deleteById(id);
    }

    @Override
    public int updateStatus(Long id, Integer status) {
        return customerServiceMapper.updateStatus(id, status);
    }

    @Override
    @Transactional
    public int incrementChatCount(Long id) {
        int result = customerServiceMapper.incrementChatCount(id);
        
        // 检查是否需要更新状态为忙碌
        CustomerService customerService = queryById(id);
        if (customerService != null && 
            customerService.getCurrentChatCount() >= customerService.getMaxConcurrentChats() &&
            CustomerServiceStatusEnum.ONLINE.getStatus().equals(customerService.getStatus())) {
            updateStatus(id, CustomerServiceStatusEnum.BUSY.getStatus());
            log.info("客服达到最大并发数，状态更新为忙碌，客服ID: {}", id);
        }
        
        return result;
    }

    @Override
    @Transactional
    public int decrementChatCount(Long id) {
        int result = customerServiceMapper.decrementChatCount(id);
        
        // 检查是否需要更新状态为在线
        CustomerService customerService = queryById(id);
        if (customerService != null && 
            customerService.getCurrentChatCount() < customerService.getMaxConcurrentChats() &&
            CustomerServiceStatusEnum.BUSY.getStatus().equals(customerService.getStatus())) {
            updateStatus(id, CustomerServiceStatusEnum.ONLINE.getStatus());
            log.info("客服聊天数减少，状态更新为在线，客服ID: {}", id);
        }
        
        return result;
    }

    @Override
    public boolean isCustomerService(Long userId) {
        CustomerService customerService = queryByUserId(userId);
        return customerService != null;
    }

    @Override
    public boolean hasRoomPermission(Long userId, Long roomUserId, Long customerServiceId) {
        // 如果是聊天室的用户，有权限
        if (userId.equals(roomUserId)) {
            return true;
        }
        
        // 如果是分配的客服，有权限
        if (customerServiceId != null && customerServiceId.equals(userId)) {
            return true;
        }
        
        // 检查是否为客服（即使未分配到此聊天室，客服也可能需要查看）
        return isCustomerService(userId);
    }
}
