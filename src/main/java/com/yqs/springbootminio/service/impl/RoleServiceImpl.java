package com.yqs.springbootminio.service.impl;

import com.yqs.springbootminio.dao.RoleDO;
import com.yqs.springbootminio.mapper.RoleMapper;
import com.yqs.springbootminio.model.Role;
import com.yqs.springbootminio.service.RoleService;
import com.yqs.springbootminio.util.CopyUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 角色表服务实现类
 *
 * <AUTHOR>
 * @date 2025/5/14
 * @description
 */
@Service
public class RoleServiceImpl implements RoleService {

    @Autowired
    private RoleMapper roleMapper;

    @Override
    public Role queryById(Long roleId) {
        RoleDO roleDO = roleMapper.selectById(roleId);
        return CopyUtil.copy(roleDO, Role.class);
    }

    @Override
    public int insert(Role role) {
        RoleDO roleDO = CopyUtil.copy(role, RoleDO.class);
        return roleMapper.insert(roleDO);
    }

    @Override
    public int update(Role role) {
        RoleDO roleDO = CopyUtil.copy(role, RoleDO.class);
        return roleMapper.update(roleDO);
    }

    @Override
    public int deleteById(Long roleId) {
        return roleMapper.deleteById(roleId);
    }
}
