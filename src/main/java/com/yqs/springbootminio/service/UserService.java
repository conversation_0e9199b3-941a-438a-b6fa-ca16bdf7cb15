package com.yqs.springbootminio.service;


import com.yqs.springbootminio.model.Menu;
import com.yqs.springbootminio.model.User;

import java.util.List;

/**
 * 用户表(User)表服务接口
 *
 * <AUTHOR>
 * @since 2025-05-14 10:30:11
 */
public interface UserService {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    User queryById(Long id);


    /**
     * 新增数据
     *
     * @param user 实例对象
     * @return 实例对象
     */
    int insert(User user);

    /**
     * 修改数据
     *
     * @param user 实例对象
     * @return 实例对象
     */
    int update(User user);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    int deleteById(Long id);

    /**
     * 登录
     * @param userName
     * @param password
     * @return
     */
    String login(String userName, String password);

    /**
     * 通过用户名查询单条数据
     *
     * @param userName
     * @return
     */
    User queryByUserName(String userName);

    /**
     * 根据用户ID获取用户的菜单权限
     *
     * @param userId 用户ID
     * @return 菜单权限列表
     */
    List<Menu> getUserMenus(Long userId);
}
