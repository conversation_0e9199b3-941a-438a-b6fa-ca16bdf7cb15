package com.yqs.springbootminio.service;

/**
 * <AUTHOR>
 * @date 2025/5/10
 * @description
 */

import com.yqs.springbootminio.model.Employee;
import com.yqs.springbootminio.util.PageView;

/**
 * <AUTHOR> @description 员工信息表
 * @date 2025-05-10
 */
public interface EmployeeService {

    /**
     * 新增
     */
    public Object insert(Employee employee);

    /**
     * 删除
     */
    public Object delete(int id);

    /**
     * 更新
     */
    public Object update(Employee employee);

    /**
     * 根据主键 id 查询
     */
    public Employee load(int id);

    /**
     * 分页查询
     */
    public PageView<Employee> pageList(int pageNum, int pagesize);

    public Integer getCount();

}