package com.yqs.springbootminio.service;

import com.yqs.springbootminio.model.RolesMenus;

/**
 * 角色菜单关联服务接口
 *
 * <AUTHOR>
 * @date 2025/5/14
 * @description
 */
public interface RolesMenusService {
    /**
     * 通过菜单ID查询单条数据
     *
     * @param menuId 主键
     * @return 实例对象
     */
    RolesMenus queryById(Long menuId);

    /**
     * 新增数据
     *
     * @param rolesMenus 实例对象
     * @return 影响行数
     */
    int insert(RolesMenus rolesMenus);

    /**
     * 修改数据
     *
     * @param rolesMenus 实例对象
     * @return 影响行数
     */
    int update(RolesMenus rolesMenus);

    /**
     * 通过菜单ID删除数据
     *
     * @param menuId 主键
     * @return 影响行数
     */
    int deleteById(Long menuId);
}
