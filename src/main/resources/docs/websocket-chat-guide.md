# WebSocket在线聊天系统完整指南

## 📖 目录
1. [系统概述](#系统概述)
2. [核心概念](#核心概念)
3. [后端准备工作](#后端准备工作)
4. [前端开发指南](#前端开发指南)
5. [简化使用方式](#简化使用方式)
6. [接口文档](#接口文档)
7. [测试流程](#测试流程)
8. [常见问题](#常见问题)

## 🎯 系统概述

### 什么是WebSocket？
WebSocket是一种在单个TCP连接上进行全双工通信的协议。与传统的HTTP请求-响应模式不同，WebSocket允许服务器主动向客户端推送数据，非常适合实时聊天场景。

### 我们的聊天系统架构
```
前端Vue应用 ←→ WebSocket连接 ←→ Spring Boot后端 ←→ MySQL数据库
```

### 核心流程

#### 🚀 简化版流程（推荐）
1. 用户登录获取JWT Token
2. 用户点击"在线客服"按钮
3. 系统自动完成聊天室创建、WebSocket连接、客服分配
4. 用户直接发送消息，等待客服回复

#### 📋 标准版流程
1. 用户登录获取JWT Token
2. 前端建立WebSocket连接（携带Token）
3. 创建或加入聊天室
4. 实时收发消息
5. 查看历史消息

## 🧠 核心概念

### 1. 聊天室（ChatRoom）
- 每个用户与客服的对话都在一个独立的聊天室中
- 聊天室有唯一的房间编码（roomCode）
- 状态：等待客服、活跃、关闭

### 2. 消息类型
- **用户消息**：senderType = 1
- **客服消息**：senderType = 2
- **系统消息**：如"用户加入聊天"

### 3. WebSocket通信模式
- **订阅**：`/topic/chat/{roomCode}` - 接收消息
- **发送**：`/app/chat.sendMessage/{roomCode}` - 发送消息
- **加入**：`/app/chat.joinRoom/{roomCode}` - 加入聊天室

## 🔧 后端准备工作

### 1. 数据库初始化
```sql
-- 执行建表脚本
source src/main/resources/sql/chat_tables.sql;

-- 创建测试客服
INSERT INTO customer_service (user_id, service_name, status, max_concurrent_chats, current_chat_count) 
VALUES (1, '客服小助手', 1, 10, 0);
```

### 2. 配置检查
确保以下配置正确：
```properties
# application.properties
secure.ignored.urls=/isOk,/**/login,/swagger**/**,/v2/**,/doc.html,/ws/**,/static/**,/test,/chat-page,/chat-test
```

### 3. 启动应用
```bash
mvn spring-boot:run
```

### 4. 验证后端
访问测试接口：
- `http://localhost:8080/test` - 基础测试
- `http://localhost:8080/chat-test` - 聊天测试页面

## 💻 前端开发指南

### 1. 安装依赖
```bash
npm install sockjs-client @stomp/stompjs
# 或者
npm install sockjs-client stompjs
```

### 2. 创建聊天服务类
```javascript
// src/services/ChatService.js
import SockJS from 'sockjs-client'
import { Stomp } from '@stomp/stompjs'

class ChatService {
  constructor() {
    this.stompClient = null
    this.connected = false
    this.currentRoomCode = null
    this.messageHandlers = []
  }

  // 连接WebSocket
  connect(token) {
    return new Promise((resolve, reject) => {
      const socket = new SockJS(`${process.env.VUE_APP_API_BASE_URL}/ws/chat`)
      this.stompClient = Stomp.over(socket)
      
      const headers = {
        'Authorization': ` ${token}`
      }
      
      this.stompClient.connect(headers, 
        (frame) => {
          console.log('WebSocket连接成功:', frame)
          this.connected = true
          resolve(frame)
        },
        (error) => {
          console.error('WebSocket连接失败:', error)
          this.connected = false
          reject(error)
        }
      )
    })
  }

  // 断开连接
  disconnect() {
    if (this.stompClient && this.connected) {
      this.stompClient.disconnect()
      this.connected = false
    }
  }

  // 加入聊天室
  joinRoom(roomCode) {
    if (!this.connected) {
      throw new Error('WebSocket未连接')
    }
    
    this.currentRoomCode = roomCode
    
    // 订阅聊天室消息
    this.stompClient.subscribe(`/topic/chat/${roomCode}`, (message) => {
      const messageData = JSON.parse(message.body)
      this.handleMessage(messageData)
    })
    
    // 发送加入房间消息
    this.stompClient.send(`/app/chat.joinRoom/${roomCode}`, {}, JSON.stringify({}))
  }

  // 发送消息
  sendMessage(content) {
    if (!this.connected || !this.currentRoomCode) {
      throw new Error('未连接或未加入聊天室')
    }
    
    this.stompClient.send(`/app/chat.sendMessage/${this.currentRoomCode}`, {}, JSON.stringify({
      content: content
    }))
  }

  // 处理接收到的消息
  handleMessage(messageData) {
    this.messageHandlers.forEach(handler => {
      handler(messageData)
    })
  }

  // 添加消息处理器
  addMessageHandler(handler) {
    this.messageHandlers.push(handler)
  }

  // 移除消息处理器
  removeMessageHandler(handler) {
    const index = this.messageHandlers.indexOf(handler)
    if (index > -1) {
      this.messageHandlers.splice(index, 1)
    }
  }
}

export default new ChatService()
```

### 3. 创建API服务类
```javascript
// src/services/ChatAPI.js
import axios from 'axios'

const API_BASE_URL = process.env.VUE_APP_API_BASE_URL

class ChatAPI {
  constructor() {
    this.http = axios.create({
      baseURL: API_BASE_URL
    })
    
    // 请求拦截器 - 自动添加Token
    this.http.interceptors.request.use(config => {
      const token = localStorage.getItem('token')
      if (token) {
        config.headers.Authorization = token
      }
      return config
    })
  }

  // 创建或获取聊天室
  async createChatRoom() {
    const response = await this.http.post('/api/chat/room')
    return response.data
  }

  // 获取聊天历史
  async getChatHistory(roomCode, page = 1, size = 20) {
    const response = await this.http.get(`/api/chat/history/${roomCode}`, {
      params: { page, size }
    })
    return response.data
  }

  // 获取用户聊天室列表
  async getUserChatRooms() {
    const response = await this.http.get('/api/chat/rooms')
    return response.data
  }

  // 自动分配客服
  async autoAssignCustomerService(roomCode) {
    const response = await this.http.post(`/api/chat/auto-assign/${roomCode}`)
    return response.data
  }

  // 结束聊天
  async endChatRoom(roomCode) {
    const response = await this.http.post(`/api/chat/end/${roomCode}`)
    return response.data
  }

  // 获取在线客服列表
  async getOnlineServices() {
    const response = await this.http.get('/api/chat/services/online')
    return response.data
  }
}

export default new ChatAPI()
```

### 4. 创建聊天组件
```vue
<!-- src/components/ChatWindow.vue -->
<template>
  <div class="chat-window">
    <!-- 聊天头部 -->
    <div class="chat-header">
      <h3>在线客服</h3>
      <div class="status-info">
        <span :class="['connection-status', connected ? 'connected' : 'disconnected']">
          {{ connected ? '已连接' : '未连接' }}
        </span>
        <button @click="requestCustomerService" :disabled="!connected" class="request-service-btn">
          请求客服
        </button>
      </div>
    </div>

    <!-- 消息列表 -->
    <div class="messages-container" ref="messagesContainer">
      <div v-for="message in messages" :key="message.id || message.timestamp" 
           :class="['message', getMessageClass(message)]">
        <div class="message-content">
          <div class="sender-info">
            <span class="sender-name">{{ message.senderName || '系统' }}</span>
            <span class="message-time">{{ formatTime(message.createTime || message.timestamp) }}</span>
          </div>
          <div class="content">{{ message.content }}</div>
        </div>
      </div>
    </div>

    <!-- 输入区域 -->
    <div class="input-area">
      <input 
        v-model="newMessage" 
        @keyup.enter="sendMessage" 
        placeholder="输入消息..."
        :disabled="!connected"
        class="message-input"
      >
      <button @click="sendMessage" :disabled="!connected || !newMessage.trim()" class="send-btn">
        发送
      </button>
    </div>
  </div>
</template>

<script>
import ChatService from '@/services/ChatService'
import ChatAPI from '@/services/ChatAPI'

export default {
  name: 'ChatWindow',
  data() {
    return {
      connected: false,
      messages: [],
      newMessage: '',
      roomCode: '',
      loading: false
    }
  },
  
  async mounted() {
    await this.initializeChat()
  },
  
  beforeDestroy() {
    this.cleanup()
  },
  
  methods: {
    async initializeChat() {
      try {
        this.loading = true
        
        // 1. 获取Token
        const token = localStorage.getItem('token')
        if (!token) {
          this.$message.error('请先登录')
          return
        }
        
        // 2. 创建聊天室
        await this.createChatRoom()
        
        // 3. 连接WebSocket
        await this.connectWebSocket(token)
        
        // 4. 加入聊天室
        await this.joinChatRoom()
        
        // 5. 加载历史消息
        await this.loadChatHistory()
        
      } catch (error) {
        console.error('初始化聊天失败:', error)
        this.$message.error('聊天初始化失败')
      } finally {
        this.loading = false
      }
    },
    
    async createChatRoom() {
      const result = await ChatAPI.createChatRoom()
      if (result.code === 200) {
        this.roomCode = result.data.roomCode
        console.log('聊天室创建成功:', this.roomCode)
      } else {
        throw new Error(result.message)
      }
    },
    
    async connectWebSocket(token) {
      // 添加消息处理器
      ChatService.addMessageHandler(this.handleMessage)
      
      // 连接WebSocket
      await ChatService.connect(token)
      this.connected = true
    },
    
    async joinChatRoom() {
      ChatService.joinRoom(this.roomCode)
      this.addSystemMessage(`已加入聊天室: ${this.roomCode}`)
    },
    
    async loadChatHistory() {
      try {
        const result = await ChatAPI.getChatHistory(this.roomCode)
        if (result.code === 200) {
          this.messages = result.data.messages.map(msg => ({
            ...msg,
            timestamp: new Date(msg.createTime).getTime()
          }))
          this.$nextTick(() => {
            this.scrollToBottom()
          })
        }
      } catch (error) {
        console.error('加载历史消息失败:', error)
      }
    },
    
    handleMessage(messageData) {
      if (messageData.type === 'USER_JOINED') {
        this.addSystemMessage(messageData.message)
      } else {
        this.messages.push({
          ...messageData,
          timestamp: new Date().getTime()
        })
        this.$nextTick(() => {
          this.scrollToBottom()
        })
      }
    },
    
    sendMessage() {
      if (!this.newMessage.trim()) return
      
      try {
        ChatService.sendMessage(this.newMessage)
        this.newMessage = ''
      } catch (error) {
        console.error('发送消息失败:', error)
        this.$message.error('发送消息失败')
      }
    },
    
    async requestCustomerService() {
      try {
        const result = await ChatAPI.autoAssignCustomerService(this.roomCode)
        if (result.code === 200) {
          this.$message.success('客服分配成功')
          this.addSystemMessage('正在为您分配客服...')
        } else {
          this.$message.warning(result.message)
        }
      } catch (error) {
        console.error('请求客服失败:', error)
        this.$message.error('请求客服失败')
      }
    },
    
    addSystemMessage(content) {
      this.messages.push({
        id: Date.now(),
        content: content,
        senderType: 'system',
        timestamp: new Date().getTime()
      })
      this.$nextTick(() => {
        this.scrollToBottom()
      })
    },
    
    getMessageClass(message) {
      if (message.senderType === 'system') return 'system'
      return message.senderType === 1 ? 'user' : 'service'
    },
    
    formatTime(time) {
      if (!time) return ''
      const date = new Date(time)
      return date.toLocaleTimeString()
    },
    
    scrollToBottom() {
      const container = this.$refs.messagesContainer
      if (container) {
        container.scrollTop = container.scrollHeight
      }
    },
    
    cleanup() {
      ChatService.removeMessageHandler(this.handleMessage)
      ChatService.disconnect()
    }
  }
}
</script>

<style scoped>
.chat-window {
  display: flex;
  flex-direction: column;
  height: 500px;
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
  background: white;
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: #f5f5f5;
  border-bottom: 1px solid #ddd;
}

.chat-header h3 {
  margin: 0;
  color: #333;
}

.status-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.connection-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.connection-status.connected {
  background: #d4edda;
  color: #155724;
}

.connection-status.disconnected {
  background: #f8d7da;
  color: #721c24;
}

.request-service-btn {
  padding: 6px 12px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.request-service-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 15px;
}

.message {
  margin-bottom: 15px;
}

.message.user .message-content {
  background: #007bff;
  color: white;
  margin-left: auto;
  margin-right: 0;
  max-width: 70%;
}

.message.service .message-content {
  background: #28a745;
  color: white;
  margin-left: 0;
  margin-right: auto;
  max-width: 70%;
}

.message.system .message-content {
  background: #6c757d;
  color: white;
  margin: 0 auto;
  text-align: center;
  max-width: 80%;
  font-style: italic;
}

.message-content {
  padding: 10px 15px;
  border-radius: 12px;
  word-wrap: break-word;
}

.sender-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
  font-size: 12px;
  opacity: 0.8;
}

.content {
  font-size: 14px;
  line-height: 1.4;
}

.input-area {
  display: flex;
  padding: 15px;
  border-top: 1px solid #ddd;
  background: #f9f9f9;
}

.message-input {
  flex: 1;
  padding: 10px 15px;
  border: 1px solid #ddd;
  border-radius: 20px;
  outline: none;
  font-size: 14px;
}

.send-btn {
  margin-left: 10px;
  padding: 10px 20px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 20px;
  cursor: pointer;
  font-size: 14px;
}

.send-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}
</style>
```

### 5. 在页面中使用聊天组件
```vue
<!-- src/views/CustomerService.vue -->
<template>
  <div class="customer-service-page">
    <div class="page-header">
      <h2>在线客服</h2>
    </div>

    <div class="chat-container">
      <ChatWindow />
    </div>
  </div>
</template>

<script>
import ChatWindow from '@/components/ChatWindow.vue'

export default {
  name: 'CustomerService',
  components: {
    ChatWindow
  }
}
</script>

<style scoped>
.customer-service-page {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 20px;
}

.chat-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}
</style>
```

## 🚀 简化使用方式

### 概述

为了提供更好的用户体验，我们新增了快速开始接口，让用户无需了解聊天室概念，点击"在线客服"按钮即可开始聊天。

### 快速开始接口

#### API接口
```http
POST /api/chat/quick-start
Authorization: {JWT_TOKEN}

Response:
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "roomCode": "ROOM_1735123456789",
    "roomId": 1,
    "status": 2,
    "customerServiceAssigned": true,
    "recentMessages": [...]
  }
}
```

**功能说明**：
- 自动创建或获取用户的聊天室
- 自动分配可用客服
- 返回最近的聊天记录
- 一次调用完成所有准备工作

### 使用优势

1. **用户体验简化** - 用户无需了解聊天室概念
2. **一键启动** - 单次API调用完成所有准备工作
3. **自动处理** - 自动分配客服、加载历史消息
4. **即插即用** - 组件可直接集成到任何页面

详细的前端实现示例请参考：[简化聊天组件示例](./simple-chat-widget-example.md)

## 📋 接口文档

### REST API接口

#### 1. 聊天室管理

##### 1.1 创建或获取聊天室
```http
POST /api/chat/room
Headers:
  Authorization:  {JWT_TOKEN}
  Content-Type: application/json

Response:
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1,
    "roomCode": "ROOM_1234567890",
    "userId": 1,
    "customerServiceId": null,
    "status": 2,
    "createTime": "2025-06-25T10:30:00"
  }
}
```

##### 1.2 获取聊天历史
```http
GET /api/chat/history/{roomCode}?page=1&size=20
Headers:
  Authorization:  {JWT_TOKEN}

Response:
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "messages": [
      {
        "id": 1,
        "roomId": 1,
        "senderId": 1,
        "senderType": 1,
        "messageType": 1,
        "content": "你好，我需要帮助",
        "createTime": "2025-06-25T10:31:00"
      }
    ],
    "roomInfo": {...},
    "page": 1,
    "size": 20
  }
}
```

##### 1.3 自动分配客服
```http
POST /api/chat/auto-assign/{roomCode}
Headers:
  Authorization:  {JWT_TOKEN}

Response:
{
  "code": 200,
  "message": "客服自动分配成功",
  "data": "客服自动分配成功"
}
```

##### 1.4 结束聊天室
```http
POST /api/chat/end/{roomCode}
Headers:
  Authorization:  {JWT_TOKEN}

Response:
{
  "code": 200,
  "message": "聊天室已结束",
  "data": "聊天室已结束"
}
```

#### 2. 客服管理

##### 2.1 获取客服信息
```http
GET /api/customer-service/info
Headers:
  Authorization:  {JWT_TOKEN}

Response:
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1,
    "userId": 2,
    "serviceName": "客服小王",
    "status": 1,
    "maxConcurrentChats": 10,
    "currentChatCount": 3
  }
}
```

##### 2.2 更新客服状态
```http
POST /api/customer-service/status?status=1
Headers:
  Authorization:  {JWT_TOKEN}

Response:
{
  "code": 200,
  "message": "状态更新成功",
  "data": "状态更新成功"
}
```

##### 2.3 获取在线客服列表
```http
GET /api/customer-service/online
Headers:
  Authorization:  {JWT_TOKEN}

Response:
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "id": 1,
      "serviceName": "客服小王",
      "status": 1,
      "currentChatCount": 3
    }
  ]
}
```

### WebSocket接口

#### 连接端点
```
ws://localhost:8080/ws/chat
```

#### 连接参数
```javascript
const headers = {
  'Authorization': ' ' + jwtToken
}
```

#### 订阅频道
```javascript
// 订阅聊天室消息
stompClient.subscribe('/topic/chat/{roomCode}', function(message) {
  const messageData = JSON.parse(message.body)
  // 处理消息
})
```

#### 发送消息
```javascript
// 发送聊天消息
stompClient.send('/app/chat.sendMessage/{roomCode}', {}, JSON.stringify({
  content: '消息内容'
}))

// 加入聊天室
stompClient.send('/app/chat.joinRoom/{roomCode}', {}, JSON.stringify({}))
```

#### 消息格式

##### 接收消息格式
```json
{
  "id": 123,
  "roomCode": "ROOM_1234567890",
  "senderId": 1,
  "senderName": "用户名",
  "senderAvatar": "头像URL",
  "senderType": 1,
  "messageType": 1,
  "content": "消息内容",
  "createTime": "2025-06-25T10:31:00"
}
```

##### 系统消息格式
```json
{
  "type": "USER_JOINED",
  "userId": 1,
  "userName": "用户名",
  "message": "用户名 加入了聊天"
}
```

## 🧪 测试流程

### 1. 后端测试

#### 1.1 启动应用
```bash
mvn spring-boot:run
```

#### 1.2 测试基础接口
```bash
# 测试应用是否正常启动
curl http://localhost:8080/test

# 测试聊天测试页面
curl http://localhost:8080/chat-test
```

#### 1.3 数据库检查
```sql
-- 检查表是否创建成功
SHOW TABLES LIKE 'chat_%';
SHOW TABLES LIKE 'customer_service';

-- 检查客服数据
SELECT * FROM customer_service;
```

### 2. 前端测试

#### 2.1 环境配置
```javascript
// .env.development
VUE_APP_API_BASE_URL=http://localhost:8080
```

#### 2.2 测试步骤
1. **登录获取Token**
2. **初始化聊天组件**
3. **建立WebSocket连接**
4. **创建聊天室**
5. **发送测试消息**
6. **请求客服分配**

### 3. 完整测试流程

#### 3.1 用户端测试
```javascript
// 1. 用户登录
const loginResponse = await axios.post('/api/auth/login', {
  username: 'testuser',
  password: 'password'
})
const token = loginResponse.data.data.token

// 2. 创建聊天室
const roomResponse = await axios.post('/api/chat/room', {}, {
  headers: { Authorization: ` ${token}` }
})
const roomCode = roomResponse.data.data.roomCode

// 3. 连接WebSocket
const socket = new SockJS('http://localhost:8080/ws/chat')
const stompClient = Stomp.over(socket)
await stompClient.connect({ Authorization: ` ${token}` })

// 4. 加入聊天室
stompClient.subscribe(`/topic/chat/${roomCode}`, handleMessage)
stompClient.send(`/app/chat.joinRoom/${roomCode}`, {}, JSON.stringify({}))

// 5. 发送消息
stompClient.send(`/app/chat.sendMessage/${roomCode}`, {}, JSON.stringify({
  content: '你好，我需要帮助'
}))
```

#### 3.2 客服端测试
```javascript
// 1. 客服登录
const serviceLoginResponse = await axios.post('/api/auth/login', {
  username: 'service',
  password: 'password'
})
const serviceToken = serviceLoginResponse.data.data.token

// 2. 更新客服状态为在线
await axios.post('/api/customer-service/status?status=1', {}, {
  headers: { Authorization: ` ${serviceToken}` }
})

// 3. 获取客服聊天室列表
const roomsResponse = await axios.get('/api/chat/service/rooms', {
  headers: { Authorization: ` ${serviceToken}` }
})

// 4. 连接WebSocket并加入聊天室
const serviceSocket = new SockJS('http://localhost:8080/ws/chat')
const serviceStompClient = Stomp.over(serviceSocket)
await serviceStompClient.connect({ Authorization: ` ${serviceToken}` })
serviceStompClient.subscribe(`/topic/chat/${roomCode}`, handleMessage)
serviceStompClient.send(`/app/chat.joinRoom/${roomCode}`, {}, JSON.stringify({}))

// 5. 客服回复消息
serviceStompClient.send(`/app/chat.sendMessage/${roomCode}`, {}, JSON.stringify({
  content: '您好！我是客服，请问有什么可以帮助您的？'
}))
```

## ❓ 常见问题

### 1. WebSocket连接失败
**问题**：前端无法连接WebSocket
**解决方案**：
- 检查后端是否正常启动
- 确认WebSocket端点配置正确
- 检查JWT Token是否有效
- 查看浏览器控制台错误信息

### 2. 消息发送失败
**问题**：消息发送后没有响应
**解决方案**：
- 确认已正确加入聊天室
- 检查消息格式是否正确
- 验证用户权限
- 查看后端日志

### 3. 历史消息加载失败
**问题**：无法加载聊天历史
**解决方案**：
- 检查REST API是否正常
- 确认聊天室存在
- 验证用户权限
- 检查数据库连接

### 4. 客服分配失败
**问题**：自动分配客服不成功
**解决方案**：
- 确认有在线客服
- 检查客服状态和并发数
- 查看客服表数据
- 检查分配逻辑

### 5. 前端组件渲染问题
**问题**：聊天组件显示异常
**解决方案**：
- 检查Vue组件语法
- 确认依赖包正确安装
- 验证CSS样式
- 查看浏览器开发者工具

### 6. 跨域问题
**问题**：前端请求被CORS阻止
**解决方案**：
```java
// 在WebSocketConfig中添加
@Override
public void registerStompEndpoints(StompEndpointRegistry registry) {
    registry.addEndpoint("/ws/chat")
            .setAllowedOriginPatterns("*")
            .withSockJS();
}
```

### 7. Token过期处理
**问题**：Token过期导致连接断开
**解决方案**：
```javascript
// 添加Token刷新逻辑
stompClient.connect(headers, onConnected, (error) => {
  if (error.includes('401') || error.includes('Unauthorized')) {
    // 刷新Token或重新登录
    refreshToken().then(newToken => {
      // 重新连接
    })
  }
})
```

## 🎯 开发建议

### 1. 开发顺序
1. 先确保后端基础功能正常
2. 使用测试页面验证WebSocket连接
3. 开发前端基础聊天功能
4. 逐步添加高级功能

### 2. 调试技巧
- 使用浏览器开发者工具查看WebSocket连接状态
- 在后端添加详细日志
- 使用Postman测试REST API
- 分步骤验证每个功能

### 3. 性能优化
- 限制历史消息加载数量
- 实现消息分页加载
- 添加连接重试机制
- 优化前端组件渲染

这份文档应该能帮助你从零开始理解和使用WebSocket聊天系统。如果在实际开发中遇到问题，可以参考相应的解决方案部分。
