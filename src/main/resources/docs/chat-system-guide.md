# 在线聊天系统使用指南

## 概述

本系统实现了一个基于WebSocket的用户-客服在线聊天功能，支持实时消息传递、历史消息查看等功能。

## 功能特性

- ✅ 实时聊天：基于WebSocket + STOMP协议
- ✅ 用户认证：集成JWT认证系统
- ✅ 历史消息：支持分页查询聊天记录
- ✅ 消息状态：支持已读/未读状态管理
- ✅ 聊天室管理：自动创建和管理聊天室
- ✅ 客服分配：支持手动和自动分配客服
- 🚀 **快速开始：一键启动聊天，用户无感知操作**

## 数据库表结构

### 1. 聊天室表 (chat_room)
```sql
CREATE TABLE `chat_room` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '聊天室ID',
  `room_code` varchar(50) NOT NULL COMMENT '聊天室编码',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `customer_service_id` bigint(20) DEFAULT NULL COMMENT '客服ID',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0-关闭，1-活跃，2-等待客服',
  `last_message_time` datetime DEFAULT NULL COMMENT '最后消息时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_room_code` (`room_code`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_customer_service_id` (`customer_service_id`)
);
```

### 2. 聊天消息表 (chat_message)
```sql
CREATE TABLE `chat_message` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '消息ID',
  `room_id` bigint(20) NOT NULL COMMENT '聊天室ID',
  `sender_id` bigint(20) NOT NULL COMMENT '发送者ID',
  `sender_type` tinyint(1) NOT NULL COMMENT '发送者类型：1-用户，2-客服',
  `message_type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '消息类型：1-文本，2-图片，3-文件',
  `content` text NOT NULL COMMENT '消息内容',
  `is_read` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已读：0-未读，1-已读',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_room_id` (`room_id`),
  KEY `idx_sender_id` (`sender_id`)
);
```

## API接口

### REST API

#### 🚀 快速开始聊天（推荐）
```
POST /api/chat/quick-start
Headers: Authorization: {JWT_TOKEN}
Response: {
  "code": 200,
  "data": {
    "roomCode": "ROOM_123456789",
    "roomId": 1,
    "status": 2,
    "customerServiceAssigned": true,
    "recentMessages": [...]
  }
}
```

#### 1. 创建或获取聊天室
```
POST /api/chat/room
Headers: Authorization: {JWT_TOKEN}
Response: {
  "code": 200,
  "data": {
    "id": 1,
    "roomCode": "ROOM_123456789",
    "userId": 1,
    "status": 2
  }
}
```

#### 2. 获取聊天历史
```
GET /api/chat/history/{roomCode}?page=1&size=20
Headers: Authorization: {JWT_TOKEN}
Response: {
  "code": 200,
  "data": {
    "messages": [...],
    "roomInfo": {...},
    "page": 1,
    "size": 20
  }
}
```

#### 3. 获取用户聊天室列表
```
GET /api/chat/rooms
Headers: Authorization: {JWT_TOKEN}
Response: {
  "code": 200,
  "data": [...]
}
```

### WebSocket API

#### 连接端点
```
/ws/chat
```

#### 认证
在连接时需要在headers中传递JWT Token：
```javascript
const headers = {
    'Authorization': token
};
stompClient.connect(headers, onConnected, onError);
```

#### 订阅聊天室
```
/topic/chat/{roomCode}
```

#### 发送消息
```
/app/chat.sendMessage/{roomCode}
Payload: {
  "content": "消息内容"
}
```

#### 加入聊天室
```
/app/chat.joinRoom/{roomCode}
Payload: {}
```

## 使用流程

### 🚀 简化版用户流程（推荐）

1. **用户登录**：获取JWT Token
2. **点击客服按钮**：用户点击"在线客服"按钮
3. **自动启动聊天**：调用 `POST /api/chat/quick-start` 一键完成准备工作
4. **开始聊天**：用户直接发送消息，等待客服回复

### 📋 标准版用户流程

1. **用户登录**：获取JWT Token
2. **创建聊天室**：调用 `POST /api/chat/room` 创建聊天室
3. **连接WebSocket**：使用JWT Token连接WebSocket
4. **加入聊天室**：订阅聊天室并发送加入消息
5. **发送消息**：通过WebSocket发送实时消息
6. **查看历史**：通过REST API获取历史消息

### 2. 客服端使用流程

1. **客服登录**：获取JWT Token
2. **获取待处理聊天室**：查询等待客服的聊天室
3. **分配客服**：调用分配客服接口
4. **连接WebSocket**：连接并加入聊天室
5. **处理用户咨询**：实时回复用户消息

## 前端集成示例

### 🚀 简化版JavaScript示例（推荐）
```javascript
// 一键启动聊天
async function startChat() {
  try {
    // 1. 快速开始聊天
    const token = localStorage.getItem('token')
    const response = await fetch('/api/chat/quick-start', {
      method: 'POST',
      headers: { 'Authorization': token }
    })
    const result = await response.json()
    const roomCode = result.data.roomCode

    // 2. 连接WebSocket
    const socket = new SockJS('/ws/chat')
    const stompClient = Stomp.over(socket)

    stompClient.connect({ 'Authorization': token }, () => {
      // 3. 自动订阅和加入
      stompClient.subscribe(`/topic/chat/${roomCode}`, handleMessage)
      stompClient.send(`/app/chat.joinRoom/${roomCode}`, {}, JSON.stringify({}))

      // 4. 显示聊天界面
      showChatWindow(roomCode, stompClient, result.data.recentMessages)
    })

  } catch (error) {
    console.error('启动聊天失败:', error)
  }
}

// 发送消息
function sendMessage(roomCode, stompClient, content) {
  stompClient.send(`/app/chat.sendMessage/${roomCode}`, {}, JSON.stringify({
    content: content
  }))
}
```

### 📋 标准版JavaScript示例
```javascript
// 1. 连接WebSocket
const socket = new SockJS('/ws/chat');
const stompClient = Stomp.over(socket);

const headers = {
    'Authorization': jwtToken
};

stompClient.connect(headers, function(frame) {
    console.log('Connected: ' + frame);
    
    // 2. 订阅聊天室
    stompClient.subscribe('/topic/chat/' + roomCode, function(message) {
        const messageData = JSON.parse(message.body);
        displayMessage(messageData);
    });
    
    // 3. 加入聊天室
    stompClient.send("/app/chat.joinRoom/" + roomCode, {}, JSON.stringify({}));
});

// 4. 发送消息
function sendMessage(content) {
    stompClient.send("/app/chat.sendMessage/" + roomCode, {}, JSON.stringify({
        'content': content
    }));
}
```

### Vue.js示例
```vue
<template>
  <div class="chat-container">
    <div class="messages" ref="messagesContainer">
      <div v-for="message in messages" :key="message.id" 
           :class="['message', message.senderType === 1 ? 'user' : 'service']">
        <span class="sender">{{ message.senderName }}:</span>
        <span class="content">{{ message.content }}</span>
        <span class="time">{{ formatTime(message.createTime) }}</span>
      </div>
    </div>
    <div class="input-area">
      <input v-model="newMessage" @keyup.enter="sendMessage" placeholder="输入消息...">
      <button @click="sendMessage">发送</button>
    </div>
  </div>
</template>

<script>
import SockJS from 'sockjs-client'
import Stomp from 'stompjs'

export default {
  data() {
    return {
      stompClient: null,
      messages: [],
      newMessage: '',
      roomCode: '',
      connected: false
    }
  },
  methods: {
    connect() {
      const socket = new SockJS('/ws/chat')
      this.stompClient = Stomp.over(socket)
      
      const headers = {
        'Authorization': this.$store.state.token
      }
      
      this.stompClient.connect(headers, this.onConnected, this.onError)
    },
    
    onConnected(frame) {
      this.connected = true
      this.stompClient.subscribe('/topic/chat/' + this.roomCode, this.onMessageReceived)
      this.stompClient.send("/app/chat.joinRoom/" + this.roomCode, {}, JSON.stringify({}))
    },
    
    onMessageReceived(message) {
      const messageData = JSON.parse(message.body)
      this.messages.push(messageData)
      this.$nextTick(() => {
        this.scrollToBottom()
      })
    },
    
    sendMessage() {
      if (this.newMessage.trim() && this.connected) {
        this.stompClient.send("/app/chat.sendMessage/" + this.roomCode, {}, JSON.stringify({
          'content': this.newMessage
        }))
        this.newMessage = ''
      }
    }
  }
}
</script>
```

## 测试

### 1. 使用测试页面
访问 `http://localhost:8080/chat-test.html` 进行功能测试

### 2. 测试步骤
1. 输入有效的JWT Token
2. 点击"连接"按钮连接WebSocket
3. 点击"创建/获取聊天室"创建聊天室
4. 点击"加入聊天室"加入聊天
5. 在输入框中输入消息并发送

## 注意事项

1. **认证**：所有操作都需要有效的JWT Token
2. **权限**：用户只能访问自己的聊天室
3. **连接管理**：WebSocket连接断开后需要重新连接
4. **消息持久化**：所有消息都会保存到数据库
5. **性能优化**：建议对历史消息进行分页加载

## 扩展功能

可以考虑添加以下功能：
- 文件上传和图片发送
- 消息撤回功能
- 聊天室成员管理
- 消息推送通知
- 聊天记录导出
- 敏感词过滤
