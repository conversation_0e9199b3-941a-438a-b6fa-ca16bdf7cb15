# WebSocket聊天系统快速开始指南

## 🚀 5分钟快速上手

### 🎯 两种使用方式

#### 方式一：简化版（推荐）- 用户无感知
用户点击"在线客服"按钮即可开始聊天，无需了解聊天室概念。

#### 方式二：标准版 - 手动管理
用户需要手动创建聊天室、加入聊天室等步骤。

---

### 第一步：后端准备（2分钟）

#### 1. 数据库初始化
```sql
-- 执行建表脚本
source src/main/resources/sql/chat_tables.sql;

-- 创建测试客服（重要！）
INSERT INTO customer_service (user_id, service_name, status, max_concurrent_chats, current_chat_count) 
VALUES (1, '客服小助手', 1, 10, 0);
```

#### 2. 启动应用
```bash
mvn spring-boot:run
```

#### 3. 验证后端
访问：`http://localhost:8080/chat-test`

### 第二步：前端开发（3分钟）

#### 1. 安装依赖
```bash
npm install sockjs-client @stomp/stompjs
```

#### 2. 创建最简聊天组件
```vue
<template>
  <div class="simple-chat">
    <div class="status">{{ status }}</div>
    <div class="messages" ref="messages">
      <div v-for="msg in messages" :key="msg.id">
        <strong>{{ msg.sender }}:</strong> {{ msg.content }}
      </div>
    </div>
    <div class="input">
      <input v-model="newMessage" @keyup.enter="sendMessage" placeholder="输入消息...">
      <button @click="sendMessage">发送</button>
    </div>
  </div>
</template>

<script>
import SockJS from 'sockjs-client'
import { Stomp } from '@stomp/stompjs'

export default {
  data() {
    return {
      stompClient: null,
      status: '未连接',
      messages: [],
      newMessage: '',
      roomCode: ''
    }
  },
  
  async mounted() {
    await this.init()
  },
  
  methods: {
    async init() {
      try {
        // 1. 获取Token（假设已登录）
        const token = localStorage.getItem('token')
        
        // 2. 创建聊天室
        const roomResponse = await fetch('/api/chat/room', {
          method: 'POST',
          headers: { 'Authorization': token }
        })
        const roomData = await roomResponse.json()
        this.roomCode = roomData.data.roomCode
        
        // 3. 连接WebSocket
        const socket = new SockJS('/ws/chat')
        this.stompClient = Stomp.over(socket)
        
        await new Promise((resolve, reject) => {
          this.stompClient.connect(
            { 'Authorization': `${token}` },
            () => {
              this.status = '已连接'
              
              // 订阅消息
              this.stompClient.subscribe(`/topic/chat/${this.roomCode}`, (message) => {
                const data = JSON.parse(message.body)
                this.messages.push({
                  id: Date.now(),
                  sender: data.senderName || '系统',
                  content: data.content || data.message
                })
              })
              
              // 加入聊天室
              this.stompClient.send(`/app/chat.joinRoom/${this.roomCode}`, {}, JSON.stringify({}))
              resolve()
            },
            reject
          )
        })
        
      } catch (error) {
        this.status = '连接失败: ' + error.message
      }
    },
    
    sendMessage() {
      if (!this.newMessage.trim()) return
      
      this.stompClient.send(`/app/chat.sendMessage/${this.roomCode}`, {}, JSON.stringify({
        content: this.newMessage
      }))
      
      this.newMessage = ''
    }
  }
}
</script>

<style scoped>
.simple-chat {
  border: 1px solid #ddd;
  border-radius: 8px;
  height: 400px;
  display: flex;
  flex-direction: column;
}

.status {
  padding: 10px;
  background: #f5f5f5;
  border-bottom: 1px solid #ddd;
  font-size: 12px;
}

.messages {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
}

.input {
  display: flex;
  padding: 10px;
  border-top: 1px solid #ddd;
}

.input input {
  flex: 1;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.input button {
  margin-left: 10px;
  padding: 8px 16px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
}
</style>
```

## 🔧 核心概念速览

### 🚀 简化版流程（推荐）
```
1. 用户点击"在线客服"按钮
2. 调用快速开始接口 → POST /api/chat/quick-start
3. 自动连接WebSocket并订阅消息
4. 用户直接发送消息，等待客服回复
```

### 📋 标准版流程
```
1. 前端连接 → /ws/chat (携带JWT Token)
2. 创建聊天室 → POST /api/chat/room
3. 订阅消息 → /topic/chat/{roomCode}
4. 发送消息 → /app/chat.sendMessage/{roomCode}
```

### 关键API
```javascript
// 🚀 简化版：一键启动聊天
POST /api/chat/quick-start

// 📋 标准版：分步操作
POST /api/chat/room                    // 创建聊天室
GET /api/chat/history/{roomCode}       // 获取历史消息
POST /api/chat/auto-assign/{roomCode}  // 自动分配客服
```

## 🧪 测试步骤

### 🚀 简化版测试（推荐）

#### 1. 测试快速开始接口
```bash
# 使用curl测试
curl -X POST http://localhost:8080/api/chat/quick-start \
  -H "Authorization: YOUR_JWT_TOKEN"

# 预期返回：roomCode、recentMessages等信息
```

#### 2. 前端集成测试
```javascript
// 简单测试代码
async function testQuickStart() {
  const token = localStorage.getItem('token')

  try {
    // 1. 快速启动
    const response = await fetch('/api/chat/quick-start', {
      method: 'POST',
      headers: { 'Authorization': token }
    })
    const result = await response.json()
    console.log('快速启动成功:', result.data)

    // 2. 连接WebSocket
    const socket = new SockJS('/ws/chat')
    const stompClient = Stomp.over(socket)

    stompClient.connect({ 'Authorization': token }, () => {
      console.log('WebSocket连接成功')

      // 3. 订阅和加入
      stompClient.subscribe(`/topic/chat/${result.data.roomCode}`, console.log)
      stompClient.send(`/app/chat.joinRoom/${result.data.roomCode}`, {}, JSON.stringify({}))
    })

  } catch (error) {
    console.error('测试失败:', error)
  }
}
```

### 📋 标准版测试

#### 1. 后端测试
```bash
# 访问测试页面
http://localhost:8080/chat-test

# 输入JWT Token
# 点击"连接" → "创建聊天室" → "加入聊天室"
# 发送测试消息
```

#### 2. 前端测试
```javascript
// 在Vue组件中使用
<template>
  <div>
    <SimpleChatComponent />
  </div>
</template>
```

## ❗ 常见问题快速解决

### 问题1：WebSocket连接失败
```javascript
// 检查Token格式
const token = localStorage.getItem('token')
console.log('Token:', token) // 应该是完整的JWT字符串

// 检查连接参数
const headers = { 'Authorization': `${token}` }
```

### 问题2：消息发送失败
```javascript
// 确保已加入聊天室
this.stompClient.send(`/app/chat.joinRoom/${this.roomCode}`, {}, JSON.stringify({}))

// 然后再发送消息
this.stompClient.send(`/app/chat.sendMessage/${this.roomCode}`, {}, JSON.stringify({
  content: this.newMessage
}))
```

### 问题3：没有客服响应
```sql
-- 检查客服数据
SELECT * FROM customer_service WHERE status = 1;

-- 如果没有在线客服，插入一个
INSERT INTO customer_service (user_id, service_name, status, max_concurrent_chats, current_chat_count) 
VALUES (1, '测试客服', 1, 10, 0);
```

## 📱 移动端适配

### 响应式CSS
```css
@media (max-width: 768px) {
  .simple-chat {
    height: 100vh;
    border-radius: 0;
  }
  
  .input {
    padding: 15px;
  }
  
  .input input {
    font-size: 16px; /* 防止iOS缩放 */
  }
}
```

## 🎯 下一步扩展

### 1. 添加文件上传
```javascript
// 在消息中支持图片
{
  messageType: 2, // 图片类型
  content: 'image_url'
}
```

### 2. 添加消息状态
```javascript
// 显示已读/未读状态
{
  isRead: 0, // 0-未读，1-已读
  readTime: null
}
```

### 3. 添加表情支持
```javascript
// 表情包数据
const emojis = ['😀', '😂', '😍', '🤔', '👍']
```

## 🔗 相关链接

- [完整开发指南](./websocket-chat-guide.md)
- [接口文档](./api-documentation.md)
- [故障排除指南](./troubleshooting.md)

---

**提示**：这个快速指南帮你在5分钟内搭建基础聊天功能。如需更多高级功能，请参考完整开发指南。
