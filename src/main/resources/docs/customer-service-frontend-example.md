# 客服端前端实现示例

## 客服端Vue组件

### 1. 客服工作台组件
```vue
<!-- CustomerServiceWorkbench.vue -->
<template>
  <div class="service-workbench">
    <!-- 客服状态栏 -->
    <div class="service-header">
      <div class="service-info">
        <h3>客服工作台</h3>
        <span class="service-name">{{ serviceInfo.serviceName }}</span>
      </div>
      <div class="service-status">
        <select v-model="currentStatus" @change="updateStatus">
          <option value="1">在线</option>
          <option value="2">忙碌</option>
          <option value="0">离线</option>
        </select>
        <span class="chat-count">当前聊天: {{ serviceInfo.currentChatCount }}/{{ serviceInfo.maxConcurrentChats }}</span>
      </div>
    </div>

    <!-- 聊天室列表 -->
    <div class="chat-rooms-panel">
      <h4>待处理聊天室</h4>
      <div class="room-list">
        <div v-for="room in chatRooms" :key="room.id" 
             :class="['room-item', { active: selectedRoom?.id === room.id }]"
             @click="selectRoom(room)">
          <div class="room-info">
            <span class="room-code">{{ room.roomCode }}</span>
            <span class="user-name">用户ID: {{ room.userId }}</span>
          </div>
          <div class="room-status">
            <span :class="['status', getStatusClass(room.status)]">
              {{ getStatusText(room.status) }}
            </span>
            <span class="last-message-time">
              {{ formatTime(room.lastMessageTime) }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- 聊天窗口 -->
    <div class="chat-window" v-if="selectedRoom">
      <div class="chat-header">
        <h4>{{ selectedRoom.roomCode }}</h4>
        <button @click="endChat" class="end-chat-btn">结束聊天</button>
      </div>
      
      <div class="messages-container" ref="messagesContainer">
        <div v-for="message in messages" :key="message.id" 
             :class="['message', message.senderType === 1 ? 'user' : 'service']">
          <div class="message-content">
            <div class="sender-info">
              <span class="sender">{{ message.senderType === 1 ? '用户' : '客服' }}</span>
              <span class="time">{{ formatTime(message.createTime) }}</span>
            </div>
            <div class="content">{{ message.content }}</div>
          </div>
        </div>
      </div>
      
      <div class="input-area">
        <textarea 
          v-model="newMessage" 
          @keyup.ctrl.enter="sendMessage"
          placeholder="输入回复内容... (Ctrl+Enter发送)"
          rows="3"
        ></textarea>
        <button @click="sendMessage" :disabled="!newMessage.trim()">发送</button>
      </div>
    </div>
  </div>
</template>

<script>
import ChatService from '@/services/ChatService'
import ChatAPI from '@/services/ChatAPI'

export default {
  name: 'CustomerServiceWorkbench',
  data() {
    return {
      serviceInfo: {},
      currentStatus: 1,
      chatRooms: [],
      selectedRoom: null,
      messages: [],
      newMessage: '',
      connected: false
    }
  },
  
  async mounted() {
    await this.initializeService()
  },
  
  beforeDestroy() {
    this.cleanup()
  },
  
  methods: {
    async initializeService() {
      try {
        // 1. 获取客服信息
        await this.loadServiceInfo()
        
        // 2. 连接WebSocket
        await this.connectWebSocket()
        
        // 3. 加载聊天室列表
        await this.loadChatRooms()
        
        // 4. 定时刷新聊天室列表
        this.startPolling()
        
      } catch (error) {
        console.error('初始化客服工作台失败:', error)
        this.$message.error('初始化失败')
      }
    },
    
    async loadServiceInfo() {
      const result = await ChatAPI.getCustomerServiceInfo()
      if (result.code === 200) {
        this.serviceInfo = result.data
        this.currentStatus = result.data.status
      }
    },
    
    async connectWebSocket() {
      const token = localStorage.getItem('token')
      ChatService.addMessageHandler(this.handleMessage)
      await ChatService.connect(token)
      this.connected = true
    },
    
    async loadChatRooms() {
      const result = await ChatAPI.getCustomerServiceChatRooms()
      if (result.code === 200) {
        this.chatRooms = result.data
      }
    },
    
    async updateStatus() {
      try {
        const result = await ChatAPI.updateCustomerServiceStatus(this.currentStatus)
        if (result.code === 200) {
          this.$message.success('状态更新成功')
          await this.loadServiceInfo()
        }
      } catch (error) {
        this.$message.error('状态更新失败')
      }
    },
    
    async selectRoom(room) {
      this.selectedRoom = room
      
      // 加载聊天历史
      await this.loadChatHistory(room.roomCode)
      
      // 加入聊天室
      if (this.connected) {
        ChatService.joinRoom(room.roomCode)
      }
    },
    
    async loadChatHistory(roomCode) {
      try {
        const result = await ChatAPI.getChatHistory(roomCode)
        if (result.code === 200) {
          this.messages = result.data.messages
          this.$nextTick(() => {
            this.scrollToBottom()
          })
        }
      } catch (error) {
        console.error('加载聊天历史失败:', error)
      }
    },
    
    handleMessage(messageData) {
      if (messageData.roomCode === this.selectedRoom?.roomCode) {
        this.messages.push(messageData)
        this.$nextTick(() => {
          this.scrollToBottom()
        })
      }
      
      // 更新聊天室列表中的最后消息时间
      const room = this.chatRooms.find(r => r.roomCode === messageData.roomCode)
      if (room) {
        room.lastMessageTime = new Date().toISOString()
      }
    },
    
    sendMessage() {
      if (!this.newMessage.trim() || !this.selectedRoom) return
      
      try {
        ChatService.sendMessage(this.newMessage)
        this.newMessage = ''
      } catch (error) {
        this.$message.error('发送消息失败')
      }
    },
    
    async endChat() {
      if (!this.selectedRoom) return
      
      try {
        const result = await ChatAPI.endChatRoom(this.selectedRoom.roomCode)
        if (result.code === 200) {
          this.$message.success('聊天已结束')
          this.selectedRoom = null
          this.messages = []
          await this.loadChatRooms()
        }
      } catch (error) {
        this.$message.error('结束聊天失败')
      }
    },
    
    startPolling() {
      // 每30秒刷新一次聊天室列表
      setInterval(() => {
        this.loadChatRooms()
      }, 30000)
    },
    
    getStatusClass(status) {
      const statusMap = {
        0: 'closed',
        1: 'active', 
        2: 'waiting'
      }
      return statusMap[status] || 'unknown'
    },
    
    getStatusText(status) {
      const statusMap = {
        0: '已关闭',
        1: '进行中',
        2: '等待客服'
      }
      return statusMap[status] || '未知'
    },
    
    formatTime(time) {
      if (!time) return ''
      return new Date(time).toLocaleString()
    },
    
    scrollToBottom() {
      const container = this.$refs.messagesContainer
      if (container) {
        container.scrollTop = container.scrollHeight
      }
    },
    
    cleanup() {
      ChatService.removeMessageHandler(this.handleMessage)
      ChatService.disconnect()
    }
  }
}
</script>

<style scoped>
.service-workbench {
  display: flex;
  height: 100vh;
  background: #f5f5f5;
}

.service-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 60px;
  background: white;
  border-bottom: 1px solid #ddd;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  z-index: 100;
}

.service-info h3 {
  margin: 0;
  color: #333;
}

.service-name {
  color: #666;
  font-size: 14px;
}

.service-status {
  display: flex;
  align-items: center;
  gap: 15px;
}

.chat-count {
  font-size: 14px;
  color: #666;
}

.chat-rooms-panel {
  width: 300px;
  background: white;
  border-right: 1px solid #ddd;
  margin-top: 60px;
  overflow-y: auto;
}

.chat-rooms-panel h4 {
  padding: 15px;
  margin: 0;
  background: #f8f9fa;
  border-bottom: 1px solid #ddd;
}

.room-item {
  padding: 15px;
  border-bottom: 1px solid #eee;
  cursor: pointer;
  transition: background-color 0.2s;
}

.room-item:hover {
  background: #f8f9fa;
}

.room-item.active {
  background: #e3f2fd;
  border-left: 3px solid #2196f3;
}

.room-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}

.room-code {
  font-weight: bold;
  color: #333;
}

.user-name {
  color: #666;
  font-size: 12px;
}

.room-status {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
}

.status.active {
  color: #4caf50;
}

.status.waiting {
  color: #ff9800;
}

.status.closed {
  color: #9e9e9e;
}

.chat-window {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin-top: 60px;
  background: white;
}

.chat-header {
  padding: 15px;
  background: #f8f9fa;
  border-bottom: 1px solid #ddd;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.end-chat-btn {
  padding: 6px 12px;
  background: #f44336;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 15px;
}

.message {
  margin-bottom: 15px;
}

.message.user .message-content {
  background: #e3f2fd;
  margin-left: 0;
  margin-right: auto;
  max-width: 70%;
}

.message.service .message-content {
  background: #e8f5e8;
  margin-left: auto;
  margin-right: 0;
  max-width: 70%;
}

.message-content {
  padding: 10px 15px;
  border-radius: 12px;
  word-wrap: break-word;
}

.sender-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
  font-size: 12px;
  color: #666;
}

.input-area {
  padding: 15px;
  border-top: 1px solid #ddd;
  display: flex;
  gap: 10px;
}

.input-area textarea {
  flex: 1;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 10px;
  resize: vertical;
  font-family: inherit;
}

.input-area button {
  padding: 10px 20px;
  background: #2196f3;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  align-self: flex-end;
}

.input-area button:disabled {
  background: #ccc;
  cursor: not-allowed;
}
</style>
```

### 2. 客服API扩展
```javascript
// 在ChatAPI.js中添加客服相关方法
class ChatAPI {
  // ... 其他方法

  // 获取客服信息
  async getCustomerServiceInfo() {
    const response = await this.http.get('/api/customer-service/info')
    return response.data
  }

  // 更新客服状态
  async updateCustomerServiceStatus(status) {
    const response = await this.http.post(`/api/customer-service/status?status=${status}`)
    return response.data
  }

  // 获取客服聊天室列表
  async getCustomerServiceChatRooms() {
    const response = await this.http.get('/api/chat/service/rooms')
    return response.data
  }
}
```

## 🔄 完整工作流程

### 1. 客服登录
```javascript
// 客服使用user_id=1对应的账号登录
const loginResponse = await axios.post('/api/auth/login', {
  username: 'service_user',  // 对应customer_service表中user_id=1的用户
  password: 'password123'
})
```

### 2. 系统识别客服身份
```java
// 后端自动检查该用户是否为客服
@Override
public boolean isCustomerService(Long userId) {
    CustomerService customerService = queryByUserId(userId);
    return customerService != null;  // user_id=1在customer_service表中有记录，返回true
}
```

### 3. 客服看到用户聊天室
```javascript
// 获取分配给该客服的聊天室
const roomsResponse = await axios.get('/api/chat/service/rooms', {
  headers: { 'Authorization': token }
})
// 返回该客服负责的所有聊天室列表
```

### 4. 客服回复用户消息
```javascript
// 客服发送的消息会被标记为senderType=2（客服消息）
stompClient.send(`/app/chat.sendMessage/${roomCode}`, {}, JSON.stringify({
  content: '您好！我是客服，请问有什么可以帮助您的？'
}))
```

## ✅ 总结

你的理解完全正确！整个流程就是：

1. **在customer_service表中插入记录** → 将普通用户变成客服
2. **客服使用对应的用户账号登录** → 获取Token
3. **系统自动识别客服身份** → 通过user_id查询customer_service表
4. **客服可以看到分配的聊天室** → 调用客服专用API
5. **客服可以回复用户消息** → 消息会被标记为客服消息

这种设计的好处是：
- 复用现有的用户认证系统
- 灵活的角色管理（一个用户可以既是普通用户又是客服）
- 清晰的权限控制
- 易于扩展和维护
