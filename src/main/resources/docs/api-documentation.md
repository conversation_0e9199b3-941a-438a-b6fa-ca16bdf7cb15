# WebSocket聊天系统API文档

## 📋 目录
1. [认证说明](#认证说明)
2. [REST API](#rest-api)
3. [WebSocket API](#websocket-api)
4. [数据模型](#数据模型)
5. [错误码说明](#错误码说明)

## 🔐 认证说明

### JWT Token格式
```
Authorization: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```
**注意**：Token不需要Bearer前缀，直接使用JWT字符串。

### 获取Token
```http
POST /api/user/login
Content-Type: application/json

{
  "userName": "用户名",
  "password": "密码"
}

Response:
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
}
```

## 🌐 REST API

### 1. 聊天室管理

#### 1.1 快速开始聊天（推荐）
```http
POST /api/chat/quick-start
Authorization: {token}
Content-Type: application/json

Response:
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "roomCode": "ROOM_1735123456789",
    "roomId": 1,
    "status": 2,
    "customerServiceAssigned": true,
    "recentMessages": [
      {
        "id": 1,
        "roomId": 1,
        "senderId": 1,
        "senderType": 1,
        "messageType": 1,
        "content": "你好，我需要帮助",
        "isRead": 1,
        "createTime": "2025-06-25T10:31:00"
      }
    ]
  }
}
```

**说明**：一键启动聊天，自动创建聊天室、分配客服并返回历史消息。适合简化的用户体验。

#### 1.2 创建或获取聊天室
```http
POST /api/chat/room
Authorization: {token}
Content-Type: application/json

Response:
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1,
    "roomCode": "ROOM_1735123456789",
    "userId": 1,
    "customerServiceId": null,
    "status": 2,
    "lastMessageTime": null,
    "createTime": "2025-06-25T10:30:00",
    "updateTime": "2025-06-25T10:30:00"
  }
}
```

**状态说明**：
- `0`: 关闭
- `1`: 活跃
- `2`: 等待客服

#### 1.3 获取聊天历史
```http
GET /api/chat/history/{roomCode}?page=1&size=20
Authorization:{token}

Response:
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "messages": [
      {
        "id": 1,
        "roomId": 1,
        "senderId": 1,
        "senderType": 1,
        "messageType": 1,
        "content": "你好，我需要帮助",
        "isRead": 1,
        "createTime": "2025-06-25T10:31:00"
      }
    ],
    "roomInfo": {
      "id": 1,
      "roomCode": "ROOM_1735123456789",
      "status": 1
    },
    "page": 1,
    "size": 20
  }
}
```

**消息类型说明**：
- `senderType`: 1-用户，2-客服
- `messageType`: 1-文本，2-图片，3-文件
- `isRead`: 0-未读，1-已读

#### 1.4 获取用户聊天室列表
```http
GET /api/chat/rooms
Authorization:{token}

Response:
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "id": 1,
      "roomCode": "ROOM_1735123456789",
      "userId": 1,
      "customerServiceId": 2,
      "status": 1,
      "lastMessageTime": "2025-06-25T10:35:00",
      "createTime": "2025-06-25T10:30:00"
    }
  ]
}
```

#### 1.5 获取未读消息数量
```http
GET /api/chat/unread/{roomCode}
Authorization:  {token}

Response:
{
  "code": 200,
  "message": "操作成功",
  "data": 5
}
```

#### 1.6 自动分配客服
```http
POST /api/chat/auto-assign/{roomCode}
Authorization:  {token}

Response:
{
  "code": 200,
  "message": "客服自动分配成功",
  "data": "客服自动分配成功"
}
```

#### 1.7 手动分配客服
```http
POST /api/chat/assign-service/{roomCode}?customerServiceId=2
Authorization:  {token}

Response:
{
  "code": 200,
  "message": "客服分配成功",
  "data": "客服分配成功"
}
```

#### 1.8 结束聊天室
```http
POST /api/chat/end/{roomCode}
Authorization:  {token}

Response:
{
  "code": 200,
  "message": "聊天室已结束",
  "data": "聊天室已结束"
}
```

### 2. 客服管理

#### 2.1 获取客服信息
```http
GET /api/customer-service/info
Authorization:  {token}

Response:
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1,
    "userId": 2,
    "serviceName": "客服小王",
    "avatar": "avatar_url",
    "status": 1,
    "maxConcurrentChats": 10,
    "currentChatCount": 3,
    "createTime": "2025-06-25T09:00:00"
  }
}
```

**客服状态说明**：
- `0`: 离线
- `1`: 在线
- `2`: 忙碌

#### 2.2 更新客服状态
```http
POST /api/customer-service/status?status=1
Authorization:  {token}

Response:
{
  "code": 200,
  "message": "状态更新成功",
  "data": "状态更新成功"
}
```

#### 2.3 获取在线客服列表
```http
GET /api/customer-service/online
Authorization:  {token}

Response:
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "id": 1,
      "serviceName": "客服小王",
      "status": 1,
      "currentChatCount": 3,
      "maxConcurrentChats": 10
    }
  ]
}
```

#### 2.4 获取可用客服列表
```http
GET /api/customer-service/available
Authorization:  {token}

Response:
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "id": 1,
      "serviceName": "客服小王",
      "status": 1,
      "currentChatCount": 2,
      "maxConcurrentChats": 10
    }
  ]
}
```

#### 2.5 获取客服聊天室列表
```http
GET /api/chat/service/rooms
Authorization:  {token}

Response:
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "id": 1,
      "roomCode": "ROOM_1735123456789",
      "userId": 1,
      "customerServiceId": 2,
      "status": 1,
      "lastMessageTime": "2025-06-25T10:35:00"
    }
  ]
}
```

## 🔌 WebSocket API

### 连接信息
```
端点: ws://localhost:8080/ws/chat
协议: STOMP over SockJS
```

### 🚀 简化使用方式（推荐）

结合快速开始接口，实现一键启动聊天：

```javascript
// 1. 快速开始聊天
const response = await fetch('/api/chat/quick-start', {
  method: 'POST',
  headers: { 'Authorization': token }
})
const chatData = await response.json()
const roomCode = chatData.data.roomCode

// 2. 连接WebSocket并自动处理
const socket = new SockJS('/ws/chat')
const stompClient = Stomp.over(socket)

stompClient.connect({ 'Authorization': token }, () => {
  // 自动订阅和加入
  stompClient.subscribe(`/topic/chat/${roomCode}`, handleMessage)
  stompClient.send(`/app/chat.joinRoom/${roomCode}`, {}, JSON.stringify({}))
})

// 3. 发送消息
function sendMessage(content) {
  stompClient.send(`/app/chat.sendMessage/${roomCode}`, {}, JSON.stringify({
    content: content
  }))
}
```

### 标准连接示例
```javascript
import SockJS from 'sockjs-client'
import { Stomp } from '@stomp/stompjs'

const socket = new SockJS('http://localhost:8080/ws/chat')
const stompClient = Stomp.over(socket)

const headers = {
  'Authorization': ' ' + jwtToken
}

stompClient.connect(headers, onConnected, onError)
```

### 订阅频道

#### 订阅聊天室消息
```javascript
stompClient.subscribe('/topic/chat/{roomCode}', function(message) {
  const messageData = JSON.parse(message.body)
  console.log('收到消息:', messageData)
})
```

### 发送消息

#### 发送聊天消息
```javascript
stompClient.send('/app/chat.sendMessage/{roomCode}', {}, JSON.stringify({
  content: '消息内容'
}))
```

#### 加入聊天室
```javascript
stompClient.send('/app/chat.joinRoom/{roomCode}', {}, JSON.stringify({}))
```

### 消息格式

#### 接收的聊天消息
```json
{
  "id": 123,
  "roomCode": "ROOM_1735123456789",
  "senderId": 1,
  "senderName": "用户名",
  "senderAvatar": "头像URL",
  "senderType": 1,
  "messageType": 1,
  "content": "消息内容",
  "createTime": "2025-06-25T10:31:00"
}
```

#### 系统通知消息
```json
{
  "type": "USER_JOINED",
  "userId": 1,
  "userName": "用户名",
  "message": "用户名 加入了聊天"
}
```

## 📊 数据模型

### ChatRoom（聊天室）
```json
{
  "id": "聊天室ID",
  "roomCode": "房间编码",
  "userId": "用户ID",
  "customerServiceId": "客服ID",
  "status": "状态",
  "lastMessageTime": "最后消息时间",
  "createTime": "创建时间",
  "updateTime": "更新时间"
}
```

### ChatMessage（聊天消息）
```json
{
  "id": "消息ID",
  "roomId": "聊天室ID",
  "senderId": "发送者ID",
  "senderType": "发送者类型",
  "messageType": "消息类型",
  "content": "消息内容",
  "isRead": "是否已读",
  "createTime": "创建时间"
}
```

### CustomerService（客服）
```json
{
  "id": "客服ID",
  "userId": "关联用户ID",
  "serviceName": "客服名称",
  "avatar": "头像",
  "status": "状态",
  "maxConcurrentChats": "最大并发聊天数",
  "currentChatCount": "当前聊天数",
  "createTime": "创建时间",
  "updateTime": "更新时间"
}
```

## ❌ 错误码说明

### 通用错误码
- `200`: 操作成功
- `400`: 请求参数错误
- `401`: 未登录或Token无效
- `403`: 无权限访问
- `404`: 资源不存在
- `500`: 服务器内部错误

### 业务错误码
- `CHAT_001`: 聊天室不存在
- `CHAT_002`: 无权限访问聊天室
- `CHAT_003`: 消息发送失败
- `CHAT_004`: 客服分配失败
- `SERVICE_001`: 非客服用户
- `SERVICE_002`: 客服状态无效
- `SERVICE_003`: 无可用客服

### 错误响应格式
```json
{
  "code": 404,
  "message": "聊天室不存在",
  "data": null
}
```

## 🔧 调试工具

### 使用curl测试REST API
```bash
# 创建聊天室
curl -X POST http://localhost:8080/api/chat/room \
  -H "Authorization:  YOUR_TOKEN" \
  -H "Content-Type: application/json"

# 获取聊天历史
curl -X GET "http://localhost:8080/api/chat/history/ROOM_CODE?page=1&size=20" \
  -H "Authorization:  YOUR_TOKEN"
```

### WebSocket调试
```javascript
// 在浏览器控制台中测试
const socket = new SockJS('http://localhost:8080/ws/chat')
const client = Stomp.over(socket)

client.debug = function(str) {
  console.log('STOMP: ' + str)
}

client.connect({'Authorization': ' YOUR_TOKEN'}, function(frame) {
  console.log('Connected: ' + frame)
})
```

---

**注意**：所有API都需要有效的JWT Token认证，请确保在请求头中正确携带Authorization字段。
