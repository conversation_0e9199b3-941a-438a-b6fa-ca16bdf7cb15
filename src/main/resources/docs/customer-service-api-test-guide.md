# 客服API测试指南

## 🎯 测试目的

验证新增的客服专用接口功能是否正常工作，确保客服能够：
1. 查看等待处理的用户消息
2. 接管用户聊天室
3. 与用户进行实时对话

## 🛠️ 测试准备

### 1. 数据准备

确保数据库中有以下测试数据：

#### 用户表（user）
```sql
-- 普通用户
INSERT INTO user (id, user_name, nick_name, password, avatar) VALUES 
(1, 'testuser1', '测试用户1', 'password123', 'avatar1.jpg'),
(2, 'testuser2', '测试用户2', 'password123', 'avatar2.jpg');

-- 客服用户
INSERT INTO user (id, user_name, nick_name, password, avatar) VALUES 
(10, 'service1', '客服小王', 'password123', 'service1.jpg'),
(11, 'service2', '客服小李', 'password123', 'service2.jpg');
```

#### 客服表（customer_service）
```sql
INSERT INTO customer_service (id, user_id, service_name, avatar, status, max_concurrent_chats, current_chat_count) VALUES 
(1, 10, '客服小王', 'service1.jpg', 1, 5, 0),
(2, 11, '客服小李', 'service2.jpg', 1, 5, 0);
```

#### 聊天室表（chat_room）
```sql
-- 等待客服的聊天室
INSERT INTO chat_room (id, room_code, user_id, customer_service_id, status, create_time, last_message_time) VALUES 
(1, 'ROOM_1735123456789', 1, NULL, 2, NOW(), NOW()),
(2, 'ROOM_1735123456790', 2, NULL, 2, NOW(), NOW());
```

#### 聊天消息表（chat_message）
```sql
INSERT INTO chat_message (id, room_id, sender_id, sender_type, message_type, content, is_read, create_time) VALUES 
(1, 1, 1, 1, 1, '你好，我需要帮助', 0, NOW()),
(2, 1, 1, 1, 1, '我的订单有问题', 0, NOW()),
(3, 2, 2, 1, 1, '请问如何退款？', 0, NOW());
```

### 2. 获取测试Token

#### 普通用户Token
```bash
curl -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"userName": "testuser1", "password": "password123"}'
```

#### 客服Token
```bash
curl -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"userName": "service1", "password": "password123"}'
```

## 🧪 API测试用例

### 测试用例1：获取等待客服的聊天室列表

#### 请求
```bash
curl -X GET http://localhost:8080/api/chat/service/pending-rooms \
  -H "Authorization: {客服JWT_TOKEN}"
```

#### 预期响应
```json
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "roomId": 1,
      "roomCode": "ROOM_1735123456789",
      "userId": 1,
      "userName": "测试用户1",
      "userAvatar": "avatar1.jpg",
      "status": 2,
      "createTime": "2025-06-25T10:30:00",
      "lastMessageTime": "2025-06-25T10:30:00",
      "latestMessage": "我的订单有问题",
      "latestMessageTime": "2025-06-25T10:31:00",
      "latestMessageType": 1,
      "unreadCount": 2
    },
    {
      "roomId": 2,
      "roomCode": "ROOM_1735123456790",
      "userId": 2,
      "userName": "测试用户2",
      "userAvatar": "avatar2.jpg",
      "status": 2,
      "createTime": "2025-06-25T10:32:00",
      "lastMessageTime": "2025-06-25T10:32:00",
      "latestMessage": "请问如何退款？",
      "latestMessageTime": "2025-06-25T10:32:00",
      "latestMessageType": 1,
      "unreadCount": 1
    }
  ]
}
```

#### 验证点
- [ ] 返回状态码200
- [ ] 只返回状态为2（等待客服）的聊天室
- [ ] 包含用户信息（姓名、头像）
- [ ] 包含最新消息内容和时间
- [ ] 包含未读消息数量
- [ ] 按创建时间升序排列

### 测试用例2：客服接管聊天室

#### 请求
```bash
curl -X POST http://localhost:8080/api/chat/service/take-over/ROOM_1735123456789 \
  -H "Authorization: {客服JWT_TOKEN}"
```

#### 预期响应
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "roomCode": "ROOM_1735123456789",
    "roomId": 1,
    "userId": 1,
    "messages": [
      {
        "id": 1,
        "roomId": 1,
        "senderId": 1,
        "senderType": 1,
        "messageType": 1,
        "content": "你好，我需要帮助",
        "isRead": 0,
        "createTime": "2025-06-25T10:30:00"
      },
      {
        "id": 2,
        "roomId": 1,
        "senderId": 1,
        "senderType": 1,
        "messageType": 1,
        "content": "我的订单有问题",
        "isRead": 0,
        "createTime": "2025-06-25T10:31:00"
      }
    ]
  }
}
```

#### 验证点
- [ ] 返回状态码200
- [ ] 聊天室状态更新为1（活跃）
- [ ] 客服ID被分配到聊天室
- [ ] 返回完整的历史消息
- [ ] 消息按时间升序排列

### 测试用例3：验证聊天室状态更新

#### 请求
```bash
# 再次查询等待客服的聊天室列表
curl -X GET http://localhost:8080/api/chat/service/pending-rooms \
  -H "Authorization: {客服JWT_TOKEN}"
```

#### 预期响应
```json
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "roomId": 2,
      "roomCode": "ROOM_1735123456790",
      "userId": 2,
      "userName": "测试用户2",
      "userAvatar": "avatar2.jpg",
      "status": 2,
      "createTime": "2025-06-25T10:32:00",
      "lastMessageTime": "2025-06-25T10:32:00",
      "latestMessage": "请问如何退款？",
      "latestMessageTime": "2025-06-25T10:32:00",
      "latestMessageType": 1,
      "unreadCount": 1
    }
  ]
}
```

#### 验证点
- [ ] 已接管的聊天室不再出现在待处理列表中
- [ ] 只返回仍在等待的聊天室

### 测试用例4：权限验证

#### 4.1 普通用户访问客服接口
```bash
curl -X GET http://localhost:8080/api/chat/service/pending-rooms \
  -H "Authorization: {普通用户JWT_TOKEN}"
```

#### 预期响应
```json
{
  "code": 403,
  "message": "非客服用户无权限访问"
}
```

#### 4.2 未登录用户访问
```bash
curl -X GET http://localhost:8080/api/chat/service/pending-rooms
```

#### 预期响应
```json
{
  "code": 401,
  "message": "用户未登录"
}
```

## 🔄 WebSocket集成测试

### 测试用例5：客服WebSocket连接和消息处理

#### 1. 客服连接WebSocket
```javascript
const socket = new SockJS('http://localhost:8080/ws/chat')
const stompClient = Stomp.over(socket)

stompClient.connect(
  { 'Authorization': customerServiceToken },
  () => {
    console.log('客服WebSocket连接成功')
    
    // 订阅接管的聊天室
    stompClient.subscribe('/topic/chat/ROOM_1735123456789', (message) => {
      console.log('收到消息:', JSON.parse(message.body))
    })
    
    // 加入聊天室
    stompClient.send('/app/chat.joinRoom/ROOM_1735123456789', {}, JSON.stringify({}))
  }
)
```

#### 2. 客服发送消息
```javascript
stompClient.send('/app/chat.sendMessage/ROOM_1735123456789', {}, JSON.stringify({
  content: '您好，我是客服小王，很高兴为您服务！'
}))
```

#### 3. 用户端验证
用户端应该能收到客服的消息，消息的senderType应该为2（客服）。

## 📊 测试结果记录

### 功能测试结果
- [ ] 获取等待客服的聊天室列表
- [ ] 客服接管聊天室
- [ ] 聊天室状态正确更新
- [ ] 权限验证正常
- [ ] WebSocket消息收发正常

### 性能测试结果
- [ ] 接口响应时间 < 500ms
- [ ] 并发10个客服同时查询待处理列表
- [ ] 数据库查询性能正常

### 异常情况测试
- [ ] 接管不存在的聊天室
- [ ] 重复接管同一个聊天室
- [ ] 客服离线时的处理
- [ ] 网络异常时的重连机制

## 🐛 常见问题排查

### 1. 获取待处理列表为空
**可能原因**：
- 数据库中没有状态为2的聊天室
- SQL查询条件错误
- 权限验证失败

**排查方法**：
```sql
-- 检查聊天室状态
SELECT * FROM chat_room WHERE status = 2;

-- 检查客服信息
SELECT * FROM customer_service WHERE user_id = {当前用户ID};
```

### 2. 接管聊天室失败
**可能原因**：
- 聊天室不存在
- 聊天室已被其他客服接管
- 数据库更新失败

**排查方法**：
```sql
-- 检查聊天室状态
SELECT * FROM chat_room WHERE room_code = 'ROOM_1735123456789';
```

### 3. WebSocket连接失败
**可能原因**：
- Token无效或过期
- WebSocket配置错误
- 网络连接问题

**排查方法**：
- 检查Token有效性
- 查看服务器日志
- 测试网络连接

## ✅ 测试完成检查清单

- [ ] 所有API接口测试通过
- [ ] 权限验证正常
- [ ] WebSocket功能正常
- [ ] 数据库状态更新正确
- [ ] 异常情况处理正常
- [ ] 性能指标达标
- [ ] 文档与实际功能一致

完成以上测试后，客服工作台功能即可正式投入使用。
