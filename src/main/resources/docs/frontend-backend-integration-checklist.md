# 前后端联调检查清单

## 📋 联调前准备

### 后端准备清单 ✅

#### 1. 数据库准备
- [ ] 执行建表脚本 `chat_tables.sql`
- [ ] 创建测试客服数据
- [ ] 验证表结构正确

```sql
-- 检查表是否存在
SHOW TABLES LIKE 'chat_%';
SHOW TABLES LIKE 'customer_service';

-- 检查客服数据
SELECT * FROM customer_service WHERE status = 1;
```

#### 2. 应用启动
- [ ] Spring Boot应用正常启动
- [ ] 端口8080可访问
- [ ] 无启动错误日志

```bash
# 验证应用启动
curl http://localhost:8080/test
# 应该返回: "测试接口正常工作！"
```

#### 3. WebSocket配置
- [ ] WebSocket端点配置正确
- [ ] CORS配置允许前端域名
- [ ] JWT认证拦截器工作正常

```bash
# 测试WebSocket页面
http://localhost:8080/chat-test
```

#### 4. API接口测试
- [ ] 聊天室创建接口正常
- [ ] 历史消息查询接口正常
- [ ] 客服管理接口正常

### 前端准备清单 ✅

#### 1. 依赖安装
- [ ] 安装 `sockjs-client`
- [ ] 安装 `@stomp/stompjs` 或 `stompjs`
- [ ] 安装 `axios`（用于HTTP请求）

```bash
npm install sockjs-client @stomp/stompjs axios
```

#### 2. 环境配置
- [ ] 配置API基础URL
- [ ] 配置WebSocket连接地址
- [ ] 配置Token存储方式

```javascript
// .env.development
VUE_APP_API_BASE_URL=http://localhost:8080
VUE_APP_WS_URL=ws://localhost:8080/ws/chat
```

#### 3. 基础服务类
- [ ] 创建ChatService类
- [ ] 创建ChatAPI类
- [ ] 配置HTTP拦截器

## 🔧 联调步骤

### 🚀 简化版联调（推荐）

#### 1. 测试快速开始接口
```javascript
// 测试快速开始聊天
const testQuickStart = async () => {
  const token = localStorage.getItem('token')
  try {
    const response = await axios.post('http://localhost:8080/api/chat/quick-start', {}, {
      headers: { 'Authorization': token }
    })
    console.log('快速开始成功:', response.data)
    return response.data.data
  } catch (error) {
    console.error('快速开始失败:', error)
  }
}
```

**检查点**：
- [ ] 快速开始接口正常返回
- [ ] 返回roomCode和recentMessages
- [ ] customerServiceAssigned状态正确

#### 2. 测试简化版WebSocket连接
```javascript
// 基于快速开始结果连接WebSocket
const testSimpleWebSocket = async () => {
  const chatData = await testQuickStart()
  if (!chatData) return

  const token = localStorage.getItem('token')
  const socket = new SockJS('http://localhost:8080/ws/chat')
  const stompClient = Stomp.over(socket)

  stompClient.connect({ 'Authorization': token }, () => {
    console.log('WebSocket连接成功')

    // 自动订阅和加入
    stompClient.subscribe(`/topic/chat/${chatData.roomCode}`, (message) => {
      console.log('收到消息:', JSON.parse(message.body))
    })

    stompClient.send(`/app/chat.joinRoom/${chatData.roomCode}`, {}, JSON.stringify({}))

    // 发送测试消息
    setTimeout(() => {
      stompClient.send(`/app/chat.sendMessage/${chatData.roomCode}`, {}, JSON.stringify({
        content: '这是一条测试消息'
      }))
    }, 1000)
  })
}
```

**检查点**：
- [ ] WebSocket连接成功
- [ ] 能够自动订阅聊天室
- [ ] 能够发送和接收消息
- [ ] 历史消息正确显示

### 📋 标准版联调

#### 第一阶段：基础连接测试

#### 1. 测试用户登录
```javascript
// 前端测试代码
const loginTest = async () => {
  try {
    const response = await axios.post('http://localhost:8080/api/auth/login', {
      username: 'testuser',
      password: 'password'
    })
    console.log('登录成功:', response.data)
    localStorage.setItem('token', response.data.data.token)
  } catch (error) {
    console.error('登录失败:', error)
  }
}
```

**检查点**：
- [ ] 能够成功登录
- [ ] 获取到有效Token
- [ ] Token格式正确（ xxx）

#### 2. 测试REST API
```javascript
// 测试创建聊天室
const testCreateRoom = async () => {
  const token = localStorage.getItem('token')
  try {
    const response = await axios.post('http://localhost:8080/api/chat/room', {}, {
      headers: { 'Authorization': token }
    })
    console.log('聊天室创建成功:', response.data)
    return response.data.data.roomCode
  } catch (error) {
    console.error('创建聊天室失败:', error)
  }
}
```

**检查点**：
- [ ] 能够创建聊天室
- [ ] 返回正确的roomCode
- [ ] 响应格式符合预期

### 第二阶段：WebSocket连接测试

#### 1. 测试WebSocket连接
```javascript
// 测试WebSocket连接
const testWebSocketConnection = () => {
  const token = localStorage.getItem('token')
  const socket = new SockJS('http://localhost:8080/ws/chat')
  const stompClient = Stomp.over(socket)
  
  const headers = {
    'Authorization': ` ${token}`
  }
  
  stompClient.connect(headers, 
    (frame) => {
      console.log('WebSocket连接成功:', frame)
      // 连接成功后的处理
    },
    (error) => {
      console.error('WebSocket连接失败:', error)
    }
  )
}
```

**检查点**：
- [ ] WebSocket连接成功
- [ ] 认证通过
- [ ] 无连接错误

#### 2. 测试消息订阅
```javascript
// 测试消息订阅
const testMessageSubscription = (stompClient, roomCode) => {
  stompClient.subscribe(`/topic/chat/${roomCode}`, (message) => {
    const messageData = JSON.parse(message.body)
    console.log('收到消息:', messageData)
  })
  
  // 加入聊天室
  stompClient.send(`/app/chat.joinRoom/${roomCode}`, {}, JSON.stringify({}))
}
```

**检查点**：
- [ ] 能够订阅聊天室
- [ ] 能够接收消息
- [ ] 消息格式正确

### 第三阶段：消息收发测试

#### 1. 测试发送消息
```javascript
// 测试发送消息
const testSendMessage = (stompClient, roomCode) => {
  stompClient.send(`/app/chat.sendMessage/${roomCode}`, {}, JSON.stringify({
    content: '这是一条测试消息'
  }))
}
```

**检查点**：
- [ ] 能够发送消息
- [ ] 消息能够被接收
- [ ] 消息内容正确

#### 2. 测试历史消息
```javascript
// 测试获取历史消息
const testChatHistory = async (roomCode) => {
  const token = localStorage.getItem('token')
  try {
    const response = await axios.get(`http://localhost:8080/api/chat/history/${roomCode}`, {
      headers: { 'Authorization': token }
    })
    console.log('历史消息:', response.data)
  } catch (error) {
    console.error('获取历史消息失败:', error)
  }
}
```

**检查点**：
- [ ] 能够获取历史消息
- [ ] 消息列表格式正确
- [ ] 分页功能正常

### 第四阶段：客服功能测试

#### 1. 测试客服分配
```javascript
// 测试自动分配客服
const testAutoAssignService = async (roomCode) => {
  const token = localStorage.getItem('token')
  try {
    const response = await axios.post(`http://localhost:8080/api/chat/auto-assign/${roomCode}`, {}, {
      headers: { 'Authorization': token }
    })
    console.log('客服分配结果:', response.data)
  } catch (error) {
    console.error('客服分配失败:', error)
  }
}
```

**检查点**：
- [ ] 能够分配客服
- [ ] 客服状态更新正确
- [ ] 聊天室状态变为活跃

## 🐛 常见问题排查

### 1. WebSocket连接问题

#### 问题：连接失败
```javascript
// 检查连接参数
console.log('Token:', localStorage.getItem('token'))
console.log('WebSocket URL:', 'ws://localhost:8080/ws/chat')

// 检查Token格式
const token = localStorage.getItem('token')

```

#### 问题：认证失败
```javascript
// 检查Token有效性
const checkTokenValidity = async () => {
  const token = localStorage.getItem('token')
  try {
    const response = await axios.get('http://localhost:8080/api/chat/rooms', {
      headers: { 'Authorization': token }
    })
    console.log('Token有效')
  } catch (error) {
    console.error('Token无效或已过期')
  }
}
```

### 2. 消息收发问题

#### 问题：消息发送失败
```javascript
// 检查连接状态
if (!stompClient.connected) {
  console.error('WebSocket未连接')
  return
}

// 检查房间编码
if (!roomCode) {
  console.error('房间编码为空')
  return
}

// 检查消息格式
const message = {
  content: messageContent
}
console.log('发送消息:', JSON.stringify(message))
```

#### 问题：消息接收异常
```javascript
// 添加详细日志
stompClient.subscribe(`/topic/chat/${roomCode}`, (message) => {
  console.log('原始消息:', message.body)
  try {
    const messageData = JSON.parse(message.body)
    console.log('解析后消息:', messageData)
  } catch (error) {
    console.error('消息解析失败:', error)
  }
})
```

### 3. API调用问题

#### 问题：CORS错误
```javascript
// 检查请求头
const config = {
  headers: {
    'Authorization': localStorage.getItem('token'),
    'Content-Type': 'application/json'
  }
}
console.log('请求配置:', config)
```

#### 问题：404错误
```javascript
// 检查API地址
const apiUrl = `${process.env.VUE_APP_API_BASE_URL}/api/chat/room`
console.log('API地址:', apiUrl)

// 检查后端路由
curl -X POST http://localhost:8080/api/chat/room \
  -H "Authorization:  YOUR_TOKEN"
```

## ✅ 联调完成检查

### 🚀 简化版功能验证清单
- [ ] 用户能够登录并获取Token
- [ ] 快速开始接口正常工作
- [ ] 一键启动聊天功能正常
- [ ] WebSocket自动连接成功
- [ ] 能够发送和接收消息
- [ ] 历史消息自动加载
- [ ] 客服自动分配功能正常
- [ ] 聊天窗口显示正常
- [ ] 用户体验流畅无卡顿

### 📋 标准版功能验证清单
- [ ] 用户能够登录并获取Token
- [ ] 能够创建聊天室
- [ ] WebSocket连接正常
- [ ] 能够发送和接收消息
- [ ] 历史消息加载正常
- [ ] 客服分配功能正常
- [ ] 消息状态更新正确
- [ ] 错误处理机制完善

### 性能检查
- [ ] 消息发送响应时间 < 500ms
- [ ] 历史消息加载时间 < 2s
- [ ] WebSocket连接稳定
- [ ] 内存使用正常

### 兼容性检查
- [ ] Chrome浏览器正常
- [ ] Firefox浏览器正常
- [ ] Safari浏览器正常
- [ ] 移动端浏览器正常

## 📝 联调报告模板

```markdown
# 前后端联调报告

## 测试环境
- 后端地址: http://localhost:8080
- 前端地址: http://localhost:3000
- 测试时间: 2025-06-25

## 测试结果
### 基础功能
- [x] 用户登录: 正常
- [x] 聊天室创建: 正常
- [x] WebSocket连接: 正常
- [x] 消息收发: 正常

### 高级功能
- [x] 历史消息: 正常
- [x] 客服分配: 正常
- [x] 状态管理: 正常

## 发现问题
1. 问题描述
2. 复现步骤
3. 解决方案

## 待优化项
1. 性能优化建议
2. 用户体验改进
3. 错误处理完善
```

---

**提示**：按照这个检查清单逐步进行联调，可以确保前后端功能正常对接。遇到问题时，参考排查步骤进行调试。
