# 客服工作台使用指南

## 🎯 客服工作流程说明

### 问题解答

您提出的问题非常重要！让我详细解释客服端的工作流程：

#### 1. 用户发送消息后，客服如何感知？

**答案**：客服通过专门的工作台界面实时查看等待处理的用户消息。

#### 2. 客服登录后如何知道哪些用户需要帮助？

**答案**：客服登录后会看到一个待处理消息列表，显示所有等待客服回复的用户。

#### 3. 客服点击按钮会不会创建新的聊天室？

**答案**：不会！客服使用的是完全不同的界面和接口，专门用于处理用户的聊天请求。

## 🔄 完整交互流程

### 用户端流程
1. 用户登录 → 点击"在线客服" → 发送消息
2. 系统创建聊天室，状态为"等待客服"
3. 用户消息进入客服待处理队列

### 客服端流程
1. 客服登录 → 打开客服工作台
2. 查看待处理消息列表（实时更新）
3. 点击某个用户的聊天 → 接管聊天室
4. 与用户实时对话

## 🛠️ 客服专用接口

### 1. 获取等待客服的聊天室列表
```http
GET /api/chat/service/pending-rooms
Authorization: {客服JWT_TOKEN}

Response:
{
  "code": 200,
  "data": [
    {
      "roomId": 1,
      "roomCode": "ROOM_123456789",
      "userId": 2,
      "userName": "张三",
      "userAvatar": "avatar_url",
      "latestMessage": "你好，我需要帮助",
      "latestMessageTime": "2025-06-25T10:30:00",
      "unreadCount": 3,
      "createTime": "2025-06-25T10:25:00"
    }
  ]
}
```

### 2. 客服接管聊天室
```http
POST /api/chat/service/take-over/{roomCode}
Authorization: {客服JWT_TOKEN}

Response:
{
  "code": 200,
  "data": {
    "roomCode": "ROOM_123456789",
    "roomId": 1,
    "userId": 2,
    "messages": [...] // 历史消息
  }
}
```

### 3. 获取客服已分配的聊天室
```http
GET /api/chat/service/rooms
Authorization: {客服JWT_TOKEN}

Response:
{
  "code": 200,
  "data": [...] // 客服负责的聊天室列表
}
```

## 💻 客服工作台前端示例

### Vue组件实现
```vue
<template>
  <div class="service-workbench">
    <!-- 客服状态栏 -->
    <div class="service-header">
      <h3>客服工作台</h3>
      <div class="service-status">
        <span class="status-indicator" :class="statusClass"></span>
        {{ statusText }}
      </div>
    </div>

    <!-- 待处理消息列表 -->
    <div class="pending-list">
      <h4>待处理消息 ({{ pendingRooms.length }})</h4>
      <div class="room-list">
        <div 
          v-for="room in pendingRooms" 
          :key="room.roomId"
          class="room-item"
          @click="takeOverRoom(room)"
        >
          <div class="user-info">
            <img :src="room.userAvatar || defaultAvatar" class="avatar" />
            <div class="user-details">
              <div class="user-name">{{ room.userName }}</div>
              <div class="latest-message">{{ room.latestMessage }}</div>
            </div>
          </div>
          <div class="room-meta">
            <div class="unread-count" v-if="room.unreadCount > 0">
              {{ room.unreadCount }}
            </div>
            <div class="message-time">
              {{ formatTime(room.latestMessageTime) }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 当前聊天窗口 -->
    <div v-if="currentRoom" class="chat-window">
      <div class="chat-header">
        <span>与 {{ currentRoom.userName }} 的对话</span>
        <button @click="closeChat" class="close-btn">×</button>
      </div>

      <div class="messages" ref="messagesContainer">
        <div 
          v-for="msg in currentMessages" 
          :key="msg.id"
          class="message"
          :class="{ 'service-message': msg.senderType === 2, 'user-message': msg.senderType === 1 }"
        >
          <div class="message-content">{{ msg.content }}</div>
          <div class="message-time">{{ formatTime(msg.createTime) }}</div>
        </div>
      </div>

      <div class="chat-input">
        <input 
          v-model="newMessage" 
          @keyup.enter="sendMessage"
          placeholder="输入回复消息..."
          :disabled="!connected"
        />
        <button @click="sendMessage" :disabled="!connected || !newMessage.trim()">
          发送
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import SockJS from 'sockjs-client'
import { Stomp } from '@stomp/stompjs'

export default {
  name: 'CustomerServiceWorkbench',
  data() {
    return {
      pendingRooms: [],
      currentRoom: null,
      currentMessages: [],
      newMessage: '',
      stompClient: null,
      connected: false,
      statusText: '在线',
      statusClass: 'online',
      defaultAvatar: '/default-avatar.png',
      refreshTimer: null
    }
  },

  async mounted() {
    await this.init()
  },

  beforeDestroy() {
    this.cleanup()
  },

  methods: {
    async init() {
      try {
        // 1. 获取待处理消息列表
        await this.loadPendingRooms()
        
        // 2. 连接WebSocket
        await this.connectWebSocket()
        
        // 3. 定时刷新待处理列表
        this.startRefreshTimer()
        
      } catch (error) {
        console.error('初始化客服工作台失败:', error)
      }
    },

    async loadPendingRooms() {
      const token = localStorage.getItem('token')
      const response = await fetch('/api/chat/service/pending-rooms', {
        headers: { 'Authorization': token }
      })
      const result = await response.json()
      
      if (result.code === 200) {
        this.pendingRooms = result.data
      }
    },

    async takeOverRoom(room) {
      try {
        const token = localStorage.getItem('token')
        const response = await fetch(`/api/chat/service/take-over/${room.roomCode}`, {
          method: 'POST',
          headers: { 'Authorization': token }
        })
        const result = await response.json()
        
        if (result.code === 200) {
          this.currentRoom = {
            ...room,
            roomCode: result.data.roomCode,
            roomId: result.data.roomId
          }
          this.currentMessages = result.data.messages || []
          
          // 订阅这个聊天室
          if (this.stompClient && this.connected) {
            this.stompClient.subscribe(`/topic/chat/${room.roomCode}`, (message) => {
              const data = JSON.parse(message.body)
              if (data.type !== 'USER_JOINED') {
                this.currentMessages.push(data)
                this.$nextTick(() => {
                  this.scrollToBottom()
                })
              }
            })
            
            // 加入聊天室
            this.stompClient.send(`/app/chat.joinRoom/${room.roomCode}`, {}, JSON.stringify({}))
          }
          
          // 从待处理列表中移除
          this.pendingRooms = this.pendingRooms.filter(r => r.roomId !== room.roomId)
          
          this.$nextTick(() => {
            this.scrollToBottom()
          })
        }
      } catch (error) {
        console.error('接管聊天室失败:', error)
      }
    },

    async connectWebSocket() {
      const token = localStorage.getItem('token')
      const socket = new SockJS('/ws/chat')
      this.stompClient = Stomp.over(socket)
      
      return new Promise((resolve, reject) => {
        this.stompClient.connect(
          { 'Authorization': token },
          () => {
            this.connected = true
            resolve()
          },
          (error) => {
            this.connected = false
            reject(error)
          }
        )
      })
    },

    sendMessage() {
      if (!this.newMessage.trim() || !this.connected || !this.currentRoom) return
      
      this.stompClient.send(`/app/chat.sendMessage/${this.currentRoom.roomCode}`, {}, JSON.stringify({
        content: this.newMessage
      }))
      
      this.newMessage = ''
    },

    closeChat() {
      this.currentRoom = null
      this.currentMessages = []
    },

    startRefreshTimer() {
      this.refreshTimer = setInterval(() => {
        this.loadPendingRooms()
      }, 10000) // 每10秒刷新一次
    },

    cleanup() {
      if (this.refreshTimer) {
        clearInterval(this.refreshTimer)
      }
      if (this.stompClient && this.connected) {
        this.stompClient.disconnect()
      }
    },

    scrollToBottom() {
      const container = this.$refs.messagesContainer
      if (container) {
        container.scrollTop = container.scrollHeight
      }
    },

    formatTime(time) {
      if (!time) return ''
      return new Date(time).toLocaleTimeString()
    }
  }
}
</script>

<style scoped>
.service-workbench {
  display: flex;
  height: 100vh;
  background: #f5f5f5;
}

.service-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 15px 20px;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 100;
}

.service-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.status-indicator.online {
  background: #4CAF50;
}

.pending-list {
  width: 350px;
  background: white;
  border-right: 1px solid #e0e0e0;
  margin-top: 70px;
  overflow-y: auto;
}

.pending-list h4 {
  padding: 15px 20px;
  margin: 0;
  background: #f8f9fa;
  border-bottom: 1px solid #e0e0e0;
}

.room-item {
  padding: 15px 20px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.room-item:hover {
  background: #f8f9fa;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.user-name {
  font-weight: 500;
  margin-bottom: 4px;
}

.latest-message {
  color: #666;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}

.room-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.unread-count {
  background: #ff4444;
  color: white;
  border-radius: 10px;
  padding: 2px 6px;
  font-size: 12px;
  min-width: 18px;
  text-align: center;
}

.message-time {
  color: #999;
  font-size: 12px;
}

.chat-window {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin-top: 70px;
}

.chat-header {
  background: white;
  padding: 15px 20px;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.close-btn {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #666;
}

.messages {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  background: white;
}

.message {
  margin-bottom: 15px;
}

.user-message {
  text-align: left;
}

.service-message {
  text-align: right;
}

.user-message .message-content {
  background: #f0f0f0;
  display: inline-block;
  padding: 10px 15px;
  border-radius: 18px 18px 18px 5px;
  max-width: 70%;
}

.service-message .message-content {
  background: #007bff;
  color: white;
  display: inline-block;
  padding: 10px 15px;
  border-radius: 18px 18px 5px 18px;
  max-width: 70%;
}

.message-time {
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}

.chat-input {
  padding: 20px;
  background: white;
  border-top: 1px solid #e0e0e0;
  display: flex;
  gap: 10px;
}

.chat-input input {
  flex: 1;
  padding: 10px 15px;
  border: 1px solid #ddd;
  border-radius: 25px;
  outline: none;
}

.chat-input button {
  background: #007bff;
  color: white;
  border: none;
  border-radius: 25px;
  padding: 10px 20px;
  cursor: pointer;
}

.chat-input button:disabled {
  background: #ccc;
  cursor: not-allowed;
}
</style>
```

## 🔄 工作流程总结

### 1. 用户端
- 用户点击"在线客服" → 系统创建聊天室（状态：等待客服）
- 用户发送消息 → 消息进入待处理队列

### 2. 客服端
- 客服登录工作台 → 看到待处理消息列表
- 客服点击某个用户 → 接管聊天室（状态：活跃）
- 客服与用户实时对话

### 3. 关键区别
- **用户使用**：`POST /api/chat/quick-start` - 创建自己的聊天室
- **客服使用**：`GET /api/chat/service/pending-rooms` - 查看待处理的用户聊天室
- **客服接管**：`POST /api/chat/service/take-over/{roomCode}` - 接管用户的聊天室

这样就完美解决了您提出的问题：客服不会创建新聊天室，而是处理用户创建的聊天室！
