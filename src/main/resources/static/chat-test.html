<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>在线聊天测试</title>
    <script src="https://cdn.jsdelivr.net/npm/sockjs-client@1.6.1/dist/sockjs.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/stompjs@2.3.3/lib/stomp.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .chat-container {
            border: 1px solid #ddd;
            border-radius: 8px;
            height: 400px;
            overflow-y: auto;
            padding: 10px;
            margin-bottom: 10px;
            background-color: #f9f9f9;
        }
        .message {
            margin-bottom: 10px;
            padding: 8px;
            border-radius: 4px;
        }
        .message.user {
            background-color: #007bff;
            color: white;
            text-align: right;
        }
        .message.service {
            background-color: #28a745;
            color: white;
            text-align: left;
        }
        .message.system {
            background-color: #6c757d;
            color: white;
            text-align: center;
            font-style: italic;
        }
        .input-container {
            display: flex;
            gap: 10px;
        }
        .input-container input {
            flex: 1;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .input-container button {
            padding: 8px 16px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .input-container button:hover {
            background-color: #0056b3;
        }
        .status {
            margin-bottom: 10px;
            padding: 8px;
            border-radius: 4px;
            background-color: #e9ecef;
        }
        .connected {
            background-color: #d4edda;
            color: #155724;
        }
        .disconnected {
            background-color: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <h1>在线聊天测试</h1>
    
    <div class="status" id="status">未连接</div>
    
    <div>
        <label>JWT Token:</label>
        <input type="text" id="tokenInput" placeholder="请输入JWT Token" style="width: 100%; margin-bottom: 10px;">
        <button onclick="connect()">连接</button>
        <button onclick="disconnect()">断开连接</button>
    </div>
    
    <div>
        <label>房间编码:</label>
        <input type="text" id="roomCodeInput" placeholder="请输入房间编码" style="width: 100%; margin-bottom: 10px;">
        <button onclick="createRoom()">创建/获取聊天室</button>
        <button onclick="joinRoom()">加入聊天室</button>
    </div>
    
    <div class="chat-container" id="chatContainer"></div>
    
    <div class="input-container">
        <input type="text" id="messageInput" placeholder="输入消息..." onkeypress="handleKeyPress(event)">
        <button onclick="sendMessage()">发送</button>
    </div>

    <script>
        let stompClient = null;
        let currentRoomCode = null;
        let currentToken = null;

        function connect() {
            const token = document.getElementById('tokenInput').value;
            if (!token) {
                alert('请输入JWT Token');
                return;
            }
            
            currentToken = token;
            const socket = new SockJS('/ws/chat');
            stompClient = Stomp.over(socket);
            
            // 设置认证头
            const headers = {
                'Authorization': token
            };
            
            stompClient.connect(headers, function (frame) {
                updateStatus('已连接', true);
                console.log('Connected: ' + frame);
            }, function (error) {
                updateStatus('连接失败: ' + error, false);
                console.log('Connection error: ' + error);
            });
        }

        function disconnect() {
            if (stompClient !== null) {
                stompClient.disconnect();
            }
            updateStatus('已断开连接', false);
            console.log("Disconnected");
        }

        function createRoom() {
            if (!currentToken) {
                alert('请先连接WebSocket');
                return;
            }
            
            fetch('/api/chat/room', {
                method: 'POST',
                headers: {
                    'Authorization': currentToken,
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    currentRoomCode = data.data.roomCode;
                    document.getElementById('roomCodeInput').value = currentRoomCode;
                    addMessage('系统', '聊天室创建成功: ' + currentRoomCode, 'system');
                } else {
                    alert('创建聊天室失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('创建聊天室失败');
            });
        }

        function joinRoom() {
            const roomCode = document.getElementById('roomCodeInput').value;
            if (!roomCode) {
                alert('请输入房间编码');
                return;
            }
            
            if (stompClient === null || !stompClient.connected) {
                alert('请先连接WebSocket');
                return;
            }
            
            currentRoomCode = roomCode;
            
            // 订阅聊天室消息
            stompClient.subscribe('/topic/chat/' + roomCode, function (message) {
                const messageData = JSON.parse(message.body);
                if (messageData.type === 'USER_JOINED') {
                    addMessage('系统', messageData.message, 'system');
                } else {
                    const senderType = messageData.senderType === 1 ? 'user' : 'service';
                    addMessage(messageData.senderName, messageData.content, senderType);
                }
            });
            
            // 发送加入房间消息
            stompClient.send("/app/chat.joinRoom/" + roomCode, {}, JSON.stringify({}));
            
            addMessage('系统', '已加入聊天室: ' + roomCode, 'system');
            
            // 加载聊天历史
            loadChatHistory(roomCode);
        }

        function sendMessage() {
            const messageInput = document.getElementById('messageInput');
            const message = messageInput.value.trim();
            
            if (!message) {
                return;
            }
            
            if (!currentRoomCode) {
                alert('请先加入聊天室');
                return;
            }
            
            if (stompClient === null || !stompClient.connected) {
                alert('WebSocket未连接');
                return;
            }
            
            stompClient.send("/app/chat.sendMessage/" + currentRoomCode, {}, JSON.stringify({
                'content': message
            }));
            
            messageInput.value = '';
        }

        function loadChatHistory(roomCode) {
            fetch('/api/chat/history/' + roomCode + '?page=1&size=50', {
                headers: {
                    'Authorization': currentToken
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    const messages = data.data.messages;
                    // 清空聊天容器
                    document.getElementById('chatContainer').innerHTML = '';
                    // 显示历史消息
                    messages.forEach(msg => {
                        const senderType = msg.senderType === 1 ? 'user' : 'service';
                        addMessage('历史消息', msg.content, senderType);
                    });
                }
            })
            .catch(error => {
                console.error('Error loading chat history:', error);
            });
        }

        function addMessage(sender, content, type) {
            const chatContainer = document.getElementById('chatContainer');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message ' + type;
            messageDiv.innerHTML = '<strong>' + sender + ':</strong> ' + content;
            chatContainer.appendChild(messageDiv);
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }

        function updateStatus(message, connected) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = 'status ' + (connected ? 'connected' : 'disconnected');
        }

        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }
    </script>
</body>
</html>
