-- 聊天室表
CREATE TABLE `chat_room` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '聊天室ID',
  `room_code` varchar(50) NOT NULL COMMENT '聊天室编码',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `customer_service_id` bigint(20) DEFAULT NULL COMMENT '客服ID',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0-关闭，1-活跃，2-等待客服',
  `last_message_time` datetime DEFAULT NULL COMMENT '最后消息时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_room_code` (`room_code`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_customer_service_id` (`customer_service_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='聊天室表';

-- 聊天消息表
CREATE TABLE `chat_message` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '消息ID',
  `room_id` bigint(20) NOT NULL COMMENT '聊天室ID',
  `sender_id` bigint(20) NOT NULL COMMENT '发送者ID',
  `sender_type` tinyint(1) NOT NULL COMMENT '发送者类型：1-用户，2-客服',
  `message_type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '消息类型：1-文本，2-图片，3-文件',
  `content` text NOT NULL COMMENT '消息内容',
  `is_read` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已读：0-未读，1-已读',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_room_id` (`room_id`),
  KEY `idx_sender_id` (`sender_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='聊天消息表';

-- 客服表（可选，如果需要专门的客服角色）
CREATE TABLE `customer_service` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '客服ID',
  `user_id` bigint(20) NOT NULL COMMENT '关联用户ID',
  `service_name` varchar(50) NOT NULL COMMENT '客服名称',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0-离线，1-在线，2-忙碌',
  `max_concurrent_chats` int(11) NOT NULL DEFAULT '10' COMMENT '最大并发聊天数',
  `current_chat_count` int(11) NOT NULL DEFAULT '0' COMMENT '当前聊天数',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_id` (`user_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客服表';

-- 插入一个默认客服（使用现有用户表中的用户）
-- INSERT INTO customer_service (user_id, service_name, status) VALUES (1, '客服小助手', 1);

-- 示例数据插入（可选）
-- 插入测试聊天室
-- INSERT INTO chat_room (room_code, user_id, customer_service_id, status, create_time, update_time)
-- VALUES ('ROOM_TEST_001', 1, 2, 1, NOW(), NOW());

-- 插入测试消息
-- INSERT INTO chat_message (room_id, sender_id, sender_type, message_type, content, is_read, create_time) VALUES
-- (1, 1, 1, 1, '你好，我需要帮助', 1, NOW()),
-- (1, 2, 2, 1, '您好！我是客服小助手，请问有什么可以帮助您的？', 1, NOW()),
-- (1, 1, 1, 1, '我想了解一下你们的产品', 0, NOW());
