package com.yqs.springbootminio.controller;

import com.yqs.springbootminio.service.StorageServiceContext;
import com.yqs.springbootminio.util.CommonResult;
import io.minio.MinioClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.InputStream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@SpringBootTest
public class MinioControllerTest {
    
    @InjectMocks
    private MinioController minioController;
    
    @Mock
    private MinioClient minioClient;
    
    @Mock
    private StorageServiceContext storageServiceContext;
    
    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }
    
    @Test
    void testUploadFile() throws Exception {
        // Arrange
        String originalFilename = "test.txt";
        String newFileName = "new_test.txt";
        InputStream inputStream = new ByteArrayInputStream("test content".getBytes());
        MultipartFile multipartFile = new MockMultipartFile("file", originalFilename, "text/plain", inputStream);
        
        when(storageServiceContext.getActiveService().uploadFile(originalFilename, inputStream)).thenReturn(newFileName);
        
        // Act
        CommonResult<String> result = minioController.uploadFile(multipartFile);
        
        // Assert
        assertNotNull(result);
        assertEquals("File uploaded successfully, new file name: " + newFileName, result.getMessage());
        assertEquals(newFileName, result.getData());
        
        verify(storageServiceContext.getActiveService(), times(1)).uploadFile(originalFilename, inputStream);
    }
    
    @Test
    void testUploadFileWithEmptyFile() throws Exception {
        // Arrange
        MultipartFile multipartFile = new MockMultipartFile("file", "", "", new byte[0]);
        
        // Act
        CommonResult<String> result = minioController.uploadFile(multipartFile);
        
        // Assert
        assertNotNull(result);
        assertEquals("File is empty", result.getMessage());
        assertNull(result.getData());
    }
    
    @Test
    void testUploadFileWithNullFile() throws Exception {
        // Act
        CommonResult<String> result = minioController.uploadFile(null);
        
        // Assert
        assertNotNull(result);
        assertEquals("File is null", result.getMessage());
        assertNull(result.getData());
    }
}