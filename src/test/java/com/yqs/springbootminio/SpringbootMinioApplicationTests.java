package com.yqs.springbootminio;


import cn.hutool.aop.proxy.ProxyFactory;
import cn.hutool.core.net.NetUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.yqs.springbootminio.model.Employee;
import com.yqs.springbootminio.service.EmployeeService;
import com.yqs.springbootminio.util.JwtTokenUtil;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.crypto.bcrypt.BCrypt;
import org.springframework.security.crypto.password.PasswordEncoder;

import javax.annotation.Resource;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.*;

@SpringBootTest
class SpringbootMinioApplicationTests {

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private EmployeeService employeeService;

    @Resource
    private RedisTemplate stringRedisTemplate;

    @Autowired
    private JwtTokenUtil jwtTokenUtil;

    @Test
    void contextLoads() throws UnknownHostException {

        String macAddress = NetUtil.getMacAddress(InetAddress.getLocalHost());
        System.out.println(macAddress);
        System.out.println(InetAddress.getLocalHost().getHostAddress());
    }

    @Test
    void test() {
        String relPassword = "123456";
        String gensalt = BCrypt.gensalt();
        System.out.println("盐值：" + gensalt);
        String hashpw = BCrypt.hashpw(relPassword, gensalt);
        System.out.println(BCrypt.hashpw(relPassword, "$2a$10$40DYvGCgYF2mRtPvQZv7fe"));
        System.out.println(BCrypt.hashpw(relPassword, "$2a$10$40DYvGCgYF2mRtPvQZv7fe").length());
        System.out.println(BCrypt.hashpw(relPassword, "$2a$10$40DYvGCgYF2mRtPvQZv7fe").equals(hashpw));
        System.out.println("哈希：" + hashpw);
    }

    @Test
    void test1() {
        String encode = passwordEncoder.encode("123456");
        System.out.println(encode);
        System.out.println(BCrypt.hashpw("123456", "$2a$10$uiuzI18fJyFx4pvc7zfffu")
                .equals("$2a$10$uiuzI18fJyFx4pvc7zfffuwTMo6dkd67b4crvtSt88Z.X5xTaQoVm"));
        System.out.println(encode.length());
    }

    @Test
    void test2() {
        Employee employee = employeeService.load(99);
        System.out.println(JSONUtil.toJsonStr(employee));
    }

    @Test
    void test3() {
//        PageView<Employee> pageView = employeeService.pageList(3, 15);
//        System.out.println(pageView.getRecords());
        ApplicationContext applicationContext = SpringUtil.getApplicationContext();
        System.out.println("444");
        System.out.println();
    }

    @Test
    void test4() {
        ServiceLoader<ProxyFactory> loader = ServiceLoader.load(ProxyFactory.class);
        for (ProxyFactory proxyFactory : loader) {
            try {
                System.out.println(proxyFactory.getClass().getName());
            } catch (ServiceConfigurationError e) {
                System.err.println("Failed to load: " + e.getMessage());
            }
        }
    }

    @Test
    void test5(){
        Set<String> set = new TreeSet<>();
        set.add("employee:list");
        set.add("employee:add");
        String token = jwtTokenUtil.generateToken(1L, set);
        System.out.println(token);
    }

}
