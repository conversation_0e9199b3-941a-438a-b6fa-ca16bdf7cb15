// Chrome 网络请求监控工具
// 用于在Chrome不显示代理请求时提供替代的调试方案

class NetworkMonitor {
  constructor() {
    this.requests = [];
    this.isMonitoring = false;
  }

  // 开始监控网络请求
  startMonitoring() {
    if (this.isMonitoring) return;
    
    this.isMonitoring = true;
    console.log('🔍 网络监控已启动 - Chrome代理请求调试模式');
    
    // 监控 fetch 请求
    this.interceptFetch();
    
    // 监控 XMLHttpRequest
    this.interceptXHR();
    
    // 创建监控面板
    this.createMonitorPanel();
  }

  // 拦截 fetch 请求
  interceptFetch() {
    const originalFetch = window.fetch;
    const self = this;
    
    window.fetch = function(...args) {
      const startTime = Date.now();
      const url = args[0];
      const options = args[1] || {};
      
      self.logRequest('FETCH', url, options, startTime);
      
      return originalFetch.apply(this, args)
        .then(response => {
          const endTime = Date.now();
          self.logResponse('FETCH', url, response, endTime - startTime);
          return response;
        })
        .catch(error => {
          const endTime = Date.now();
          self.logError('FETCH', url, error, endTime - startTime);
          throw error;
        });
    };
  }

  // 拦截 XMLHttpRequest
  interceptXHR() {
    const originalXHR = window.XMLHttpRequest;
    const self = this;
    
    window.XMLHttpRequest = function() {
      const xhr = new originalXHR();
      const originalOpen = xhr.open;
      const originalSend = xhr.send;
      
      let method, url, startTime;
      
      xhr.open = function(m, u, ...args) {
        method = m;
        url = u;
        return originalOpen.apply(this, [m, u, ...args]);
      };
      
      xhr.send = function(...args) {
        startTime = Date.now();
        self.logRequest('XHR', url, { method }, startTime);
        
        const originalOnReadyStateChange = xhr.onreadystatechange;
        xhr.onreadystatechange = function() {
          if (xhr.readyState === 4) {
            const endTime = Date.now();
            if (xhr.status >= 200 && xhr.status < 300) {
              self.logResponse('XHR', url, { status: xhr.status, statusText: xhr.statusText }, endTime - startTime);
            } else {
              self.logError('XHR', url, { status: xhr.status, statusText: xhr.statusText }, endTime - startTime);
            }
          }
          if (originalOnReadyStateChange) {
            originalOnReadyStateChange.apply(this, arguments);
          }
        };
        
        return originalSend.apply(this, args);
      };
      
      return xhr;
    };
  }

  // 记录请求
  logRequest(type, url, options, startTime) {
    const request = {
      id: Date.now() + Math.random(),
      type,
      url,
      method: options.method || 'GET',
      startTime,
      status: 'pending'
    };
    
    this.requests.push(request);
    
    console.group(`📤 [${type}] ${request.method} ${url}`);
    console.log('开始时间:', new Date(startTime).toLocaleTimeString());
    console.log('请求选项:', options);
    console.groupEnd();
    
    this.updateMonitorPanel();
  }

  // 记录响应
  logResponse(type, url, response, duration) {
    const request = this.requests.find(r => r.url === url && r.status === 'pending');
    if (request) {
      request.status = 'success';
      request.response = response;
      request.duration = duration;
    }
    
    console.group(`✅ [${type}] ${response.status || 'OK'} ${url}`);
    console.log('响应时间:', duration + 'ms');
    console.log('响应详情:', response);
    console.groupEnd();
    
    this.updateMonitorPanel();
  }

  // 记录错误
  logError(type, url, error, duration) {
    const request = this.requests.find(r => r.url === url && r.status === 'pending');
    if (request) {
      request.status = 'error';
      request.error = error;
      request.duration = duration;
    }
    
    console.group(`❌ [${type}] ERROR ${url}`);
    console.log('错误时间:', duration + 'ms');
    console.log('错误详情:', error);
    console.groupEnd();
    
    this.updateMonitorPanel();
  }

  // 创建监控面板
  createMonitorPanel() {
    if (document.getElementById('network-monitor')) return;
    
    const panel = document.createElement('div');
    panel.id = 'network-monitor';
    panel.style.cssText = `
      position: fixed;
      top: 10px;
      right: 10px;
      width: 400px;
      max-height: 300px;
      background: white;
      border: 2px solid #007bff;
      border-radius: 5px;
      box-shadow: 0 4px 8px rgba(0,0,0,0.1);
      z-index: 10000;
      font-family: monospace;
      font-size: 12px;
      overflow: hidden;
    `;
    
    panel.innerHTML = `
      <div style="background: #007bff; color: white; padding: 5px 10px; display: flex; justify-content: space-between; align-items: center;">
        <span>🔍 网络监控 (Chrome代理调试)</span>
        <button onclick="document.getElementById('network-monitor').remove()" style="background: none; border: none; color: white; cursor: pointer;">✕</button>
      </div>
      <div id="monitor-content" style="padding: 10px; max-height: 250px; overflow-y: auto;">
        <div style="color: #666;">等待网络请求...</div>
      </div>
    `;
    
    document.body.appendChild(panel);
  }

  // 更新监控面板
  updateMonitorPanel() {
    const content = document.getElementById('monitor-content');
    if (!content) return;
    
    const recentRequests = this.requests.slice(-10); // 只显示最近10个请求
    
    content.innerHTML = recentRequests.map(req => {
      const statusIcon = req.status === 'success' ? '✅' : req.status === 'error' ? '❌' : '⏳';
      const statusColor = req.status === 'success' ? 'green' : req.status === 'error' ? 'red' : 'orange';
      
      return `
        <div style="margin: 5px 0; padding: 5px; border-left: 3px solid ${statusColor};">
          <div>${statusIcon} ${req.method} ${req.url.split('?')[0]}</div>
          <div style="font-size: 10px; color: #666;">
            ${req.duration ? req.duration + 'ms' : '进行中'} | ${new Date(req.startTime).toLocaleTimeString()}
          </div>
        </div>
      `;
    }).join('');
  }

  // 停止监控
  stopMonitoring() {
    this.isMonitoring = false;
    const panel = document.getElementById('network-monitor');
    if (panel) panel.remove();
    console.log('🔍 网络监控已停止');
  }

  // 获取请求历史
  getRequestHistory() {
    return this.requests;
  }

  // 清空请求历史
  clearHistory() {
    this.requests = [];
    this.updateMonitorPanel();
  }
}

// 创建全局实例
window.networkMonitor = new NetworkMonitor();

// 导出
export default NetworkMonitor;
