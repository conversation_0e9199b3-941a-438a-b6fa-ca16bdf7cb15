import axios from 'axios'

/**
 * 用户权限管理工具
 */
class UserPermissionService {
  constructor() {
    this.userInfo = null
    this.cacheTime = null
    this.cacheExpiry = 5 * 60 * 1000 // 5分钟缓存
  }

  /**
   * 获取用户信息
   * @param {boolean} forceRefresh - 是否强制刷新
   * @returns {Promise<Object>} 用户信息
   */
  async getUserInfo(forceRefresh = false) {
    try {
      // 检查缓存
      if (!forceRefresh && this.userInfo && this.cacheTime) {
        const now = Date.now()
        if (now - this.cacheTime < this.cacheExpiry) {
          console.log('✅ 使用缓存的用户信息')
          return this.userInfo
        }
      }

      const token = localStorage.getItem('token')
      if (!token) {
        throw new Error('未找到用户token')
      }

      console.log('🔄 获取用户信息...')

      const response = await axios.get('/api/user/getInfo', {
        headers: { 'Authorization': token }
      })

      if (response.data.code === '200') {
        this.userInfo = response.data.data
        this.cacheTime = Date.now()
        
        // 更新localStorage缓存
        localStorage.setItem('userInfo', JSON.stringify(this.userInfo))
        localStorage.setItem('userInfoCacheTime', this.cacheTime.toString())
        
        console.log('✅ 用户信息获取成功:', this.userInfo)
        return this.userInfo
      } else {
        throw new Error(response.data.message || '获取用户信息失败')
      }
    } catch (error) {
      console.error('❌ 获取用户信息失败:', error)
      
      // 尝试从localStorage获取缓存
      const cachedUserInfo = localStorage.getItem('userInfo')
      if (cachedUserInfo) {
        console.log('⚠️ 使用localStorage缓存的用户信息')
        this.userInfo = JSON.parse(cachedUserInfo)
        return this.userInfo
      }
      
      throw error
    }
  }

  /**
   * 检查用户是否为客服
   * @param {Object} userInfo - 用户信息（可选，不传则自动获取）
   * @returns {Promise<boolean>} 是否为客服
   */
  async isCustomerService(userInfo = null) {
    try {
      if (!userInfo) {
        userInfo = await this.getUserInfo()
      }

      if (!userInfo || !userInfo.roleName) {
        return false
      }

      // 检查roleName数组中是否包含ROOT
      const isCS = Array.isArray(userInfo.roleName) && 
                   userInfo.roleName.includes('ROOT')
      
      console.log('🔍 用户权限检查:', {
        userId: userInfo.id,
        userName: userInfo.userName,
        roleName: userInfo.roleName,
        isCustomerService: isCS
      })

      return isCS
    } catch (error) {
      console.error('❌ 检查客服权限失败:', error)
      return false
    }
  }

  /**
   * 获取用户显示名称
   * @param {Object} userInfo - 用户信息（可选）
   * @returns {Promise<string>} 用户显示名称
   */
  async getUserDisplayName(userInfo = null) {
    try {
      if (!userInfo) {
        userInfo = await this.getUserInfo()
      }

      return userInfo?.nickName || userInfo?.userName || '用户'
    } catch (error) {
      console.error('❌ 获取用户显示名称失败:', error)
      return '用户'
    }
  }

  /**
   * 清除用户信息缓存
   */
  clearCache() {
    this.userInfo = null
    this.cacheTime = null
    localStorage.removeItem('userInfo')
    localStorage.removeItem('userInfoCacheTime')
    console.log('🗑️ 用户信息缓存已清除')
  }

  /**
   * 检查用户是否已登录
   * @returns {boolean} 是否已登录
   */
  isLoggedIn() {
    const token = localStorage.getItem('token')
    return !!token
  }

  /**
   * 退出登录
   */
  logout() {
    localStorage.removeItem('token')
    localStorage.removeItem('username')
    this.clearCache()
    console.log('👋 用户已退出登录')
  }

  /**
   * 获取当前用户ID
   * @returns {Promise<number|null>} 用户ID
   */
  async getCurrentUserId() {
    try {
      const userInfo = await this.getUserInfo()
      return userInfo?.id || null
    } catch (error) {
      console.error('❌ 获取用户ID失败:', error)
      return null
    }
  }

  /**
   * 检查用户权限并返回用户类型
   * @returns {Promise<Object>} 用户类型信息
   */
  async getUserType() {
    try {
      const userInfo = await this.getUserInfo()
      const isCS = await this.isCustomerService(userInfo)
      
      return {
        userInfo,
        isCustomerService: isCS,
        userType: isCS ? 'customer_service' : 'user',
        displayName: await this.getUserDisplayName(userInfo)
      }
    } catch (error) {
      console.error('❌ 获取用户类型失败:', error)
      return {
        userInfo: null,
        isCustomerService: false,
        userType: 'unknown',
        displayName: '用户'
      }
    }
  }
}

// 创建单例实例
const userPermissionService = new UserPermissionService()

export default userPermissionService
