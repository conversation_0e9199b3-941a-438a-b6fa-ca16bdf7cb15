/**
 * 聊天功能修复助手
 * 提供一键修复常见问题的功能
 */
class ChatFixHelper {
  constructor() {
    this.fixResults = []
  }

  /**
   * 一键修复聊天功能问题
   */
  async autoFix() {
    console.log('🔧 开始自动修复聊天功能问题...')
    this.fixResults = []

    try {
      // 1. 清理缓存数据
      await this.clearCache()
      
      // 2. 重置连接状态
      await this.resetConnection()
      
      // 3. 验证Token
      await this.validateToken()
      
      // 4. 测试连接
      await this.testConnection()
      
      const report = this.generateFixReport()
      console.log('🎉 自动修复完成:', report)
      return report
      
    } catch (error) {
      console.error('❌ 自动修复失败:', error)
      this.addFixResult('自动修复', '修复过程中发生错误: ' + error.message, 'error')
      return this.generateFixReport()
    }
  }

  /**
   * 清理缓存数据
   */
  async clearCache() {
    try {
      console.log('🧹 清理缓存数据...')
      
      // 清理聊天相关的缓存
      const keysToRemove = [
        'currentRoomCode',
        'chatMessages',
        'chatConnectionStatus'
      ]
      
      keysToRemove.forEach(key => {
        if (localStorage.getItem(key)) {
          localStorage.removeItem(key)
        }
      })
      
      this.addFixResult('清理缓存', '成功清理聊天相关缓存数据', 'success')
    } catch (error) {
      this.addFixResult('清理缓存', '清理缓存失败: ' + error.message, 'error')
      throw error
    }
  }

  /**
   * 重置连接状态
   */
  async resetConnection() {
    try {
      console.log('🔄 重置连接状态...')
      
      // 尝试断开现有连接
      if (window.ChatService) {
        try {
          window.ChatService.disconnect()
        } catch (e) {
          console.log('断开连接时出现错误（可能连接已断开）:', e.message)
        }
      }
      
      this.addFixResult('重置连接', '成功重置WebSocket连接状态', 'success')
    } catch (error) {
      this.addFixResult('重置连接', '重置连接失败: ' + error.message, 'error')
      throw error
    }
  }

  /**
   * 验证Token
   */
  async validateToken() {
    try {
      console.log('🔐 验证Token...')
      
      const token = localStorage.getItem('token')
      if (!token) {
        this.addFixResult('Token验证', 'Token不存在，需要重新登录', 'warning')
        return
      }

      // 检查Token格式
      if (token.length < 10) {
        this.addFixResult('Token验证', 'Token格式异常，需要重新登录', 'warning')
        return
      }

      this.addFixResult('Token验证', 'Token存在且格式正常', 'success')
    } catch (error) {
      this.addFixResult('Token验证', 'Token验证失败: ' + error.message, 'error')
      throw error
    }
  }

  /**
   * 测试连接
   */
  async testConnection() {
    try {
      console.log('🔍 测试连接...')
      
      // 导入SystemDiagnosis进行测试
      const SystemDiagnosis = (await import('./systemDiagnosis.js')).default
      const diagnosis = await SystemDiagnosis.quickDiagnose()
      
      if (diagnosis.success) {
        this.addFixResult('连接测试', '所有连接测试通过', 'success')
      } else {
        this.addFixResult('连接测试', diagnosis.message, 'error', diagnosis)
      }
    } catch (error) {
      this.addFixResult('连接测试', '连接测试失败: ' + error.message, 'error')
      throw error
    }
  }

  /**
   * 添加修复结果
   */
  addFixResult(step, message, status, data = null) {
    this.fixResults.push({
      step,
      message,
      status,
      data,
      timestamp: new Date().toISOString()
    })
  }

  /**
   * 生成修复报告
   */
  generateFixReport() {
    const summary = {
      total: this.fixResults.length,
      success: this.fixResults.filter(r => r.status === 'success').length,
      warning: this.fixResults.filter(r => r.status === 'warning').length,
      error: this.fixResults.filter(r => r.status === 'error').length
    }

    const nextSteps = []
    
    // 根据结果生成下一步建议
    this.fixResults.forEach(result => {
      if (result.status === 'warning' || result.status === 'error') {
        switch (result.step) {
          case 'Token验证':
            nextSteps.push('请重新登录系统获取新的Token')
            break
          case '连接测试':
            nextSteps.push('请检查后端服务状态和网络连接')
            break
        }
      }
    })

    // 去重建议
    const uniqueNextSteps = [...new Set(nextSteps)]

    return {
      summary,
      results: this.fixResults,
      nextSteps: uniqueNextSteps,
      status: summary.error === 0 ? (summary.warning === 0 ? 'success' : 'warning') : 'failed',
      timestamp: new Date().toISOString()
    }
  }

  /**
   * 手动修复步骤指南
   */
  getManualFixSteps() {
    return [
      {
        step: 1,
        title: '检查后端服务',
        description: '确认Spring Boot应用已启动',
        command: 'curl http://localhost:8080/api/test',
        expected: '返回成功响应'
      },
      {
        step: 2,
        title: '检查登录状态',
        description: '确认用户已正确登录',
        action: '查看localStorage中的token和userInfo',
        expected: 'token存在且有效'
      },
      {
        step: 3,
        title: '清理浏览器缓存',
        description: '清理可能冲突的缓存数据',
        action: '按F12打开开发者工具，清理Application->Storage',
        expected: '缓存数据被清理'
      },
      {
        step: 4,
        title: '重新登录',
        description: '获取新的认证Token',
        action: '退出登录后重新登录',
        expected: '获得新的有效Token'
      },
      {
        step: 5,
        title: '测试聊天功能',
        description: '验证聊天功能是否正常',
        action: '点击在线客服按钮',
        expected: '聊天窗口正常打开'
      }
    ]
  }

  /**
   * 获取常见问题解决方案
   */
  getCommonSolutions() {
    return {
      'ECONNREFUSED': {
        problem: '连接被拒绝',
        solution: '启动后端Spring Boot服务',
        command: 'mvn spring-boot:run 或 java -jar target/your-app.jar'
      },
      'TOKEN_INVALID': {
        problem: 'Token无效或过期',
        solution: '重新登录获取新Token',
        action: '退出登录后重新登录系统'
      },
      'WEBSOCKET_FAILED': {
        problem: 'WebSocket连接失败',
        solution: '检查WebSocket服务配置',
        check: '确认ws://localhost:8080/ws/chat可访问'
      },
      'API_NOT_FOUND': {
        problem: 'API接口不存在',
        solution: '检查后端API配置',
        check: '确认/api/chat/quick-start接口已实现'
      }
    }
  }
}

// 导出单例实例
export default new ChatFixHelper()
