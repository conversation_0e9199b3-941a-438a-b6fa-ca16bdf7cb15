import SockJS from 'sockjs-client'
import Stomp from 'stompjs'
import axios from 'axios'

/**
 * 聊天服务管理器
 * 统一管理 WebSocket 连接、消息发送、聊天室管理等功能
 */
class ChatService {
  constructor() {
    this.stompClient = null
    this.isConnected = false
    this.subscriptions = new Map() // 存储订阅信息
    this.messageHandlers = new Map() // 存储消息处理器
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = 5
    this.reconnectInterval = 5000
    this.connectionPromise = null
    this.token = null
  }

  /**
   * 连接WebSocket
   * @param {string} token - JWT Token
   * @returns {Promise<boolean>}
   */
  async connect(token) {
    if (this.isConnected && this.stompClient?.connected) {
      console.log('✅ WebSocket已连接，跳过重复连接')
      return true
    }

    if (this.connectionPromise) {
      console.log('🔄 WebSocket连接中，等待连接完成...')
      return this.connectionPromise
    }

    this.token = token
    console.log('🔗 开始连接WebSocket...')

    this.connectionPromise = new Promise((resolve, reject) => {
      try {
        const socket = new SockJS('/ws/chat')
        this.stompClient = Stomp.over(socket)

        // 禁用调试日志
        this.stompClient.debug = null

        const connectHeaders = {
          'Authorization': token
        }

        this.stompClient.connect(
          connectHeaders,
          (frame) => {
            console.log('✅ WebSocket连接成功:', frame)
            this.isConnected = true
            this.reconnectAttempts = 0
            this.connectionPromise = null
            resolve(true)
          },
          (error) => {
            console.error('❌ WebSocket连接失败:', error)
            this.isConnected = false
            this.connectionPromise = null
            this.handleConnectionError(error)
            reject(error)
          }
        )

        // 设置连接超时
        setTimeout(() => {
          if (!this.isConnected) {
            console.error('❌ WebSocket连接超时')
            this.connectionPromise = null
            reject(new Error('WebSocket连接超时'))
          }
        }, 10000)

      } catch (error) {
        console.error('❌ WebSocket连接异常:', error)
        this.connectionPromise = null
        reject(error)
      }
    })

    return this.connectionPromise
  }

  /**
   * 处理连接错误
   */
  handleConnectionError(error) {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++
      console.log(`🔄 尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`)
      
      setTimeout(() => {
        if (this.token) {
          this.connect(this.token)
        }
      }, this.reconnectInterval)
    } else {
      console.error('❌ WebSocket重连失败，已达到最大重试次数')
    }
  }

  /**
   * 断开连接
   */
  disconnect() {
    if (this.stompClient && this.isConnected) {
      console.log('🔌 断开WebSocket连接...')
      
      // 清除所有订阅
      this.subscriptions.clear()
      this.messageHandlers.clear()
      
      this.stompClient.disconnect(() => {
        console.log('✅ WebSocket已断开')
      })
      
      this.isConnected = false
      this.stompClient = null
    }
  }

  /**
   * 订阅聊天室消息
   * @param {string} roomCode - 聊天室代码
   * @param {Function} messageHandler - 消息处理函数
   * @returns {Promise<boolean>}
   */
  async subscribeToRoom(roomCode, messageHandler) {
    if (!this.isConnected || !this.stompClient?.connected) {
      throw new Error('WebSocket未连接')
    }

    const destination = `/topic/chat/${roomCode}`
    
    // 如果已经订阅了，先取消订阅
    if (this.subscriptions.has(roomCode)) {
      console.log(`📤 取消之前的订阅: ${roomCode}`)
      const oldSubscription = this.subscriptions.get(roomCode)
      oldSubscription.unsubscribe()
    }

    console.log(`📥 订阅聊天室: ${roomCode}`)
    
    try {
      const subscription = this.stompClient.subscribe(destination, (message) => {
        try {
          const data = JSON.parse(message.body)
          console.log('📨 收到消息:', data)
          
          if (messageHandler && typeof messageHandler === 'function') {
            messageHandler(data)
          }
        } catch (error) {
          console.error('❌ 处理消息失败:', error)
        }
      })

      this.subscriptions.set(roomCode, subscription)
      this.messageHandlers.set(roomCode, messageHandler)
      
      return true
    } catch (error) {
      console.error('❌ 订阅聊天室失败:', error)
      throw error
    }
  }

  /**
   * 取消订阅聊天室
   * @param {string} roomCode - 聊天室代码
   */
  unsubscribeFromRoom(roomCode) {
    if (this.subscriptions.has(roomCode)) {
      console.log(`📤 取消订阅聊天室: ${roomCode}`)
      const subscription = this.subscriptions.get(roomCode)
      subscription.unsubscribe()
      this.subscriptions.delete(roomCode)
      this.messageHandlers.delete(roomCode)
    }
  }

  /**
   * 加入聊天室
   * @param {string} roomCode - 聊天室代码
   * @returns {Promise<boolean>}
   */
  async joinRoom(roomCode) {
    if (!this.isConnected || !this.stompClient?.connected) {
      throw new Error('WebSocket未连接')
    }

    try {
      const destination = `/app/chat.joinRoom/${roomCode}`
      console.log('🚪 加入聊天室:', roomCode)
      
      this.stompClient.send(destination, {}, JSON.stringify({}))
      return true
    } catch (error) {
      console.error('❌ 加入聊天室失败:', error)
      throw error
    }
  }

  /**
   * 发送消息
   * @param {string} roomCode - 聊天室代码
   * @param {string} content - 消息内容
   * @returns {Promise<boolean>}
   */
  async sendMessage(roomCode, content) {
    if (!this.isConnected || !this.stompClient?.connected) {
      throw new Error('WebSocket未连接')
    }

    if (!content || !content.trim()) {
      throw new Error('消息内容不能为空')
    }

    try {
      const destination = `/app/chat.sendMessage/${roomCode}`
      const messageData = { content: content.trim() }
      
      console.log('📤 发送消息:', { roomCode, content })
      
      this.stompClient.send(destination, {}, JSON.stringify(messageData))
      return true
    } catch (error) {
      console.error('❌ 发送消息失败:', error)
      throw error
    }
  }

  /**
   * 检查连接状态
   * @returns {boolean}
   */
  isWebSocketConnected() {
    return this.isConnected && this.stompClient?.connected
  }

  /**
   * 获取连接状态信息
   * @returns {Object}
   */
  getConnectionStatus() {
    return {
      isConnected: this.isConnected,
      reconnectAttempts: this.reconnectAttempts,
      subscriptionsCount: this.subscriptions.size,
      stompClientConnected: this.stompClient?.connected || false
    }
  }

  // ==================== API 调用方法 ====================

  /**
   * 快速开始聊天
   * @returns {Promise<Object>} 聊天室信息
   */
  async quickStartChat() {
    try {
      const token = localStorage.getItem('token')
      if (!token) {
        throw new Error('未找到用户token')
      }

      console.log('🚀 调用快速开始聊天接口...')

      const response = await axios.post('/api/chat/quick-start', {}, {
        headers: { 'Authorization': token }
      })

      if (response.data.code === 200) {
        const data = response.data.data
        console.log('✅ 快速开始聊天成功:', data)
        return data
      } else {
        throw new Error(response.data.message || '快速开始聊天失败')
      }
    } catch (error) {
      console.error('❌ 快速开始聊天失败:', error)
      throw error
    }
  }

  /**
   * 获取聊天历史
   * @param {string} roomCode - 聊天室代码
   * @param {number} page - 页码
   * @param {number} size - 每页大小
   * @returns {Promise<Object>} 聊天历史
   */
  async getChatHistory(roomCode, page = 1, size = 20) {
    try {
      const token = localStorage.getItem('token')
      if (!token) {
        throw new Error('未找到用户token')
      }

      const response = await axios.get(`/api/chat/history/${roomCode}`, {
        params: { page, size },
        headers: { 'Authorization': token }
      })

      if (response.data.code === 200) {
        return response.data.data
      } else {
        throw new Error(response.data.message || '获取聊天历史失败')
      }
    } catch (error) {
      console.error('❌ 获取聊天历史失败:', error)
      throw error
    }
  }

  /**
   * 获取待处理消息列表（客服专用）
   * @returns {Promise<Array>} 待处理消息列表
   */
  async getPendingRooms() {
    try {
      const token = localStorage.getItem('token')
      if (!token) {
        throw new Error('未找到用户token')
      }

      const response = await axios.get('/api/chat/service/pending-rooms', {
        headers: { 'Authorization': token }
      })

      if (response.data.code === 200) {
        return response.data.data
      } else {
        throw new Error(response.data.message || '获取待处理消息失败')
      }
    } catch (error) {
      console.error('❌ 获取待处理消息失败:', error)
      throw error
    }
  }

  /**
   * 客服接管聊天室
   * @param {string} roomCode - 聊天室代码
   * @returns {Promise<Object>} 接管结果
   */
  async takeOverRoom(roomCode) {
    try {
      const token = localStorage.getItem('token')
      if (!token) {
        throw new Error('未找到用户token')
      }

      const response = await axios.post(`/api/chat/service/take-over/${roomCode}`, {}, {
        headers: { 'Authorization': token }
      })

      if (response.data.code === 200) {
        return response.data.data
      } else {
        throw new Error(response.data.message || '接管聊天室失败')
      }
    } catch (error) {
      console.error('❌ 接管聊天室失败:', error)
      throw error
    }
  }
}

// 创建单例实例
const chatService = new ChatService()

export default chatService
