import axios from 'axios'

/**
 * 系统诊断工具 - 专门用于排查聊天功能问题
 */
class SystemDiagnosis {
  constructor() {
    this.results = []
    this.baseURL = 'http://localhost:8080'
  }

  /**
   * 运行完整的系统诊断
   */
  async runFullDiagnosis() {
    console.log('🚀 开始系统诊断...')
    this.results = []

    try {
      // 1. 检查基础连接
      await this.testBasicConnection()
      
      // 2. 检查API测试接口
      await this.testAPIEndpoint()
      
      // 3. 检查用户认证
      await this.testUserAuthentication()
      
      // 4. 检查聊天API
      await this.testChatAPI()
      
      // 5. 检查WebSocket连接
      await this.testWebSocketConnection()
      
      // 6. 生成诊断报告
      const report = this.generateReport()
      
      console.log('📊 诊断完成，结果：', report)
      return report
      
    } catch (error) {
      console.error('❌ 诊断过程出错:', error)
      this.addResult('诊断异常', '诊断过程中发生错误: ' + error.message, 'error')
      return this.generateReport()
    }
  }

  /**
   * 测试基础连接
   */
  async testBasicConnection() {
    try {
      console.log('🔍 测试基础连接...')
      const response = await axios.get('/test', { timeout: 5000 })
      this.addResult('基础连接', '后端服务连接正常', 'success', response.data)
    } catch (error) {
      this.addResult('基础连接', this.getErrorMessage(error), 'error', error)
      throw error
    }
  }

  /**
   * 测试API接口
   */
  async testAPIEndpoint() {
    try {
      console.log('🔍 测试API接口...')
      const response = await axios.get('/api/test', { timeout: 5000 })
      this.addResult('API接口', 'API测试接口正常', 'success', response.data)
    } catch (error) {
      this.addResult('API接口', this.getErrorMessage(error), 'error', error)
      throw error
    }
  }

  /**
   * 测试用户认证
   */
  async testUserAuthentication() {
    try {
      const token = localStorage.getItem('token')
      if (!token) {
        this.addResult('用户认证', '未找到登录Token', 'warning')
        return
      }

      console.log('🔍 测试用户认证...')
      const response = await axios.get('/api/user/info', {
        headers: { 'Authorization': token },
        timeout: 5000
      })
      
      if (response.data.code === 200) {
        this.addResult('用户认证', '用户认证正常', 'success', response.data.data)
      } else {
        this.addResult('用户认证', response.data.message || '认证失败', 'error', response.data)
      }
    } catch (error) {
      this.addResult('用户认证', this.getErrorMessage(error), 'error', error)
      throw error
    }
  }

  /**
   * 测试聊天API
   */
  async testChatAPI() {
    try {
      const token = localStorage.getItem('token')
      if (!token) {
        this.addResult('聊天API', '需要登录Token', 'warning')
        return
      }

      console.log('🔍 测试聊天API...')
      const response = await axios.post('/api/chat/quick-start', {}, {
        headers: { 'Authorization': token },
        timeout: 10000
      })
      
      if (response.data.code === 200) {
        this.addResult('聊天API', '聊天API调用成功', 'success', response.data.data)
      } else {
        this.addResult('聊天API', response.data.message || 'API调用失败', 'error', response.data)
      }
    } catch (error) {
      this.addResult('聊天API', this.getErrorMessage(error), 'error', error)
      throw error
    }
  }

  /**
   * 测试WebSocket连接
   */
  async testWebSocketConnection() {
    return new Promise((resolve) => {
      try {
        console.log('🔍 测试WebSocket连接...')
        const socket = new WebSocket('ws://localhost:8080/ws/chat')
        
        const timeout = setTimeout(() => {
          socket.close()
          this.addResult('WebSocket', '连接超时', 'error')
          resolve()
        }, 5000)

        socket.onopen = () => {
          clearTimeout(timeout)
          socket.close()
          this.addResult('WebSocket', 'WebSocket连接正常', 'success')
          resolve()
        }

        socket.onerror = (error) => {
          clearTimeout(timeout)
          this.addResult('WebSocket', 'WebSocket连接失败', 'error', error)
          resolve()
        }
      } catch (error) {
        this.addResult('WebSocket', 'WebSocket测试异常: ' + error.message, 'error', error)
        resolve()
      }
    })
  }

  /**
   * 添加测试结果
   */
  addResult(test, message, status, data = null) {
    this.results.push({
      test,
      message,
      status,
      data,
      timestamp: new Date().toISOString()
    })
  }

  /**
   * 生成诊断报告
   */
  generateReport() {
    const summary = {
      total: this.results.length,
      success: this.results.filter(r => r.status === 'success').length,
      warning: this.results.filter(r => r.status === 'warning').length,
      error: this.results.filter(r => r.status === 'error').length
    }

    const recommendations = []
    
    // 分析结果并生成建议
    this.results.forEach(result => {
      if (result.status === 'error') {
        switch (result.test) {
          case '基础连接':
            recommendations.push('请确认后端Spring Boot服务已启动并运行在 http://localhost:8080')
            break
          case 'API接口':
            recommendations.push('请检查后端API接口配置，确保 /api/test 接口可访问')
            break
          case '用户认证':
            recommendations.push('请重新登录获取有效的Token')
            break
          case '聊天API':
            recommendations.push('请检查聊天API权限配置和用户权限')
            break
          case 'WebSocket':
            recommendations.push('请检查WebSocket服务配置和防火墙设置')
            break
        }
      }
    })

    // 去重建议
    const uniqueRecommendations = [...new Set(recommendations)]

    return {
      summary,
      results: this.results,
      recommendations: uniqueRecommendations,
      status: summary.error === 0 ? 'success' : 'failed',
      timestamp: new Date().toISOString()
    }
  }

  /**
   * 获取友好的错误消息
   */
  getErrorMessage(error) {
    if (error.code === 'ECONNREFUSED') {
      return '连接被拒绝，请检查后端服务是否启动'
    } else if (error.code === 'ENOTFOUND') {
      return '无法找到服务器，请检查服务器地址'
    } else if (error.code === 'ETIMEDOUT') {
      return '连接超时，请检查网络连接'
    } else if (error.response) {
      const status = error.response.status
      const message = error.response.data?.message || error.response.statusText
      return `服务器错误 (${status}): ${message}`
    } else if (error.request) {
      return '网络请求失败，请检查网络连接'
    } else {
      return error.message || '未知错误'
    }
  }

  /**
   * 快速问题诊断
   */
  async quickDiagnose() {
    console.log('🔍 快速诊断聊天问题...')
    
    // 检查Token
    const token = localStorage.getItem('token')
    if (!token) {
      return {
        success: false,
        issue: 'NO_TOKEN',
        message: '未找到登录Token，请先登录',
        solution: '请重新登录系统'
      }
    }

    // 测试基础连接
    try {
      await axios.get('/api/test', { timeout: 5000 })
    } catch (error) {
      return {
        success: false,
        issue: 'CONNECTION_FAILED',
        message: '无法连接到后端服务',
        solution: '请确认后端服务已启动并运行在 http://localhost:8080',
        details: this.getErrorMessage(error)
      }
    }

    // 测试聊天API
    try {
      const response = await axios.post('/api/chat/quick-start', {}, {
        headers: { 'Authorization': token },
        timeout: 10000
      })
      
      if (response.data.code !== 200) {
        return {
          success: false,
          issue: 'CHAT_API_FAILED',
          message: '聊天API调用失败: ' + (response.data.message || '未知错误'),
          solution: '请检查用户权限和API配置',
          details: response.data
        }
      }
    } catch (error) {
      return {
        success: false,
        issue: 'CHAT_API_ERROR',
        message: '聊天API调用异常',
        solution: '请检查网络连接和API配置',
        details: this.getErrorMessage(error)
      }
    }

    return {
      success: true,
      message: '所有测试通过，聊天功能应该正常工作'
    }
  }
}

// 导出单例实例
export default new SystemDiagnosis()
