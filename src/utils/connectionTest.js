import axios from 'axios'

/**
 * 后端连接测试工具
 */
class ConnectionTest {
  constructor() {
    this.baseURL = 'http://localhost:8080'
  }

  /**
   * 测试基础连接
   */
  async testBasicConnection() {
    try {
      console.log('🔍 测试基础连接...')
      const response = await axios.get('/api/test', { timeout: 5000 })
      console.log('✅ 基础连接测试成功:', response.data)
      return { success: true, message: '基础连接正常', data: response.data }
    } catch (error) {
      console.error('❌ 基础连接测试失败:', error)
      return { 
        success: false, 
        message: this.getErrorMessage(error),
        error: error
      }
    }
  }

  /**
   * 测试用户认证
   */
  async testAuthentication() {
    try {
      const token = localStorage.getItem('token')
      if (!token) {
        return { success: false, message: '未找到登录Token' }
      }

      console.log('🔍 测试用户认证...')
      const response = await axios.get('/api/user/info', {
        headers: { 'Authorization': token },
        timeout: 5000
      })
      console.log('✅ 用户认证测试成功:', response.data)
      return { success: true, message: '用户认证正常', data: response.data }
    } catch (error) {
      console.error('❌ 用户认证测试失败:', error)
      return { 
        success: false, 
        message: this.getErrorMessage(error),
        error: error
      }
    }
  }

  /**
   * 测试聊天API
   */
  async testChatAPI() {
    try {
      const token = localStorage.getItem('token')
      if (!token) {
        return { success: false, message: '未找到登录Token' }
      }

      console.log('🔍 测试聊天API...')
      const response = await axios.post('/api/chat/quick-start', {}, {
        headers: { 'Authorization': token },
        timeout: 10000
      })
      console.log('✅ 聊天API测试成功:', response.data)
      return { success: true, message: '聊天API正常', data: response.data }
    } catch (error) {
      console.error('❌ 聊天API测试失败:', error)
      return { 
        success: false, 
        message: this.getErrorMessage(error),
        error: error
      }
    }
  }

  /**
   * 测试WebSocket连接
   */
  async testWebSocketConnection() {
    return new Promise((resolve) => {
      try {
        console.log('🔍 测试WebSocket连接...')
        const socket = new WebSocket('ws://localhost:8080/ws/chat')
        
        const timeout = setTimeout(() => {
          socket.close()
          resolve({ 
            success: false, 
            message: 'WebSocket连接超时' 
          })
        }, 5000)

        socket.onopen = () => {
          clearTimeout(timeout)
          socket.close()
          console.log('✅ WebSocket连接测试成功')
          resolve({ 
            success: true, 
            message: 'WebSocket连接正常' 
          })
        }

        socket.onerror = (error) => {
          clearTimeout(timeout)
          console.error('❌ WebSocket连接测试失败:', error)
          resolve({ 
            success: false, 
            message: 'WebSocket连接失败',
            error: error
          })
        }
      } catch (error) {
        console.error('❌ WebSocket测试异常:', error)
        resolve({ 
          success: false, 
          message: 'WebSocket测试异常: ' + error.message,
          error: error
        })
      }
    })
  }

  /**
   * 运行完整的连接测试
   */
  async runFullTest() {
    console.log('🚀 开始完整的连接测试...')
    
    const results = {
      basicConnection: await this.testBasicConnection(),
      authentication: await this.testAuthentication(),
      chatAPI: await this.testChatAPI(),
      webSocket: await this.testWebSocketConnection()
    }

    console.log('📊 连接测试结果:', results)
    
    // 生成测试报告
    const report = this.generateReport(results)
    console.log('📋 测试报告:', report)
    
    return { results, report }
  }

  /**
   * 生成测试报告
   */
  generateReport(results) {
    const report = {
      summary: {
        total: 4,
        passed: 0,
        failed: 0
      },
      details: [],
      recommendations: []
    }

    // 统计结果
    Object.entries(results).forEach(([test, result]) => {
      if (result.success) {
        report.summary.passed++
      } else {
        report.summary.failed++
      }
      
      report.details.push({
        test: test,
        status: result.success ? 'PASS' : 'FAIL',
        message: result.message
      })
    })

    // 生成建议
    if (!results.basicConnection.success) {
      report.recommendations.push('请确认后端服务已启动并运行在 http://localhost:8080')
    }
    
    if (!results.authentication.success) {
      report.recommendations.push('请检查用户登录状态和Token有效性')
    }
    
    if (!results.chatAPI.success) {
      report.recommendations.push('请检查聊天API接口是否正确配置')
    }
    
    if (!results.webSocket.success) {
      report.recommendations.push('请检查WebSocket服务是否正确配置')
    }

    return report
  }

  /**
   * 获取友好的错误消息
   */
  getErrorMessage(error) {
    if (error.code === 'ECONNREFUSED') {
      return '连接被拒绝，请检查后端服务是否启动'
    } else if (error.code === 'ENOTFOUND') {
      return '无法找到服务器，请检查服务器地址'
    } else if (error.code === 'ETIMEDOUT') {
      return '连接超时，请检查网络连接'
    } else if (error.response) {
      const status = error.response.status
      const message = error.response.data?.message || error.response.statusText
      return `服务器错误 (${status}): ${message}`
    } else if (error.request) {
      return '网络请求失败，请检查网络连接'
    } else {
      return error.message || '未知错误'
    }
  }

  /**
   * 快速诊断聊天问题
   */
  async quickDiagnose() {
    console.log('🔍 快速诊断聊天问题...')
    
    // 检查Token
    const token = localStorage.getItem('token')
    if (!token) {
      return {
        success: false,
        issue: 'NO_TOKEN',
        message: '未找到登录Token，请先登录',
        solution: '请重新登录系统'
      }
    }

    // 测试基础连接
    const basicTest = await this.testBasicConnection()
    if (!basicTest.success) {
      return {
        success: false,
        issue: 'CONNECTION_FAILED',
        message: '无法连接到后端服务',
        solution: '请确认后端服务已启动并运行在 http://localhost:8080',
        details: basicTest
      }
    }

    // 测试聊天API
    const chatTest = await this.testChatAPI()
    if (!chatTest.success) {
      return {
        success: false,
        issue: 'CHAT_API_FAILED',
        message: '聊天API调用失败',
        solution: '请检查用户权限和API配置',
        details: chatTest
      }
    }

    return {
      success: true,
      message: '所有测试通过，聊天功能应该正常工作'
    }
  }
}

// 导出单例实例
export default new ConnectionTest()
