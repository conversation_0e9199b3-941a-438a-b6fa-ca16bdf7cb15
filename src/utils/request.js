import axios from 'axios'

// 创建axios实例
const request = axios.create({
  timeout: 30000, // 请求超时时间
  headers: {
    'Content-Type': 'application/json',
    'X-Requested-With': 'XMLHttpRequest'
  }
})

// 请求拦截器
request.interceptors.request.use(
  config => {
    // 在发送请求之前做些什么
    const requestInfo = {
      method: config.method?.toUpperCase(),
      url: config.url,
      baseURL: config.baseURL,
      headers: config.headers,
      data: config.data,
      params: config.params,
      timestamp: new Date().toISOString()
    }

    console.group(`🌐 发送请求: ${requestInfo.method} ${requestInfo.url}`)
    console.log('请求详情:', requestInfo)
    console.log('完整URL:', (config.baseURL || '') + config.url)
    console.log('🔍 Chrome用户提示: 如果在Network面板看不到此请求，请尝试:')
    console.log('   1. 确保选择了"All"过滤器')
    console.log('   2. 勾选"Preserve log"')
    console.log('   3. 或使用豆包浏览器进行调试')
    console.groupEnd()

    // 添加token到请求头（除了登录请求）
    const token = localStorage.getItem('token')
    if (token && config.url !== '/api/user/login') {
      config.headers['Authorization'] = token
      console.log('🔐 已添加Authorization头部:', token.substring(0, 20) + '...')
    }

    // 确保请求在开发工具中可见
    config.headers['Cache-Control'] = 'no-cache'
    config.headers['Pragma'] = 'no-cache'
    config.headers['X-Request-ID'] = Date.now().toString() // 添加唯一请求ID

    return config
  },
  error => {
    // 对请求错误做些什么
    console.error('❌ 请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  response => {
    // 对响应数据做点什么
    const responseInfo = {
      status: response.status,
      statusText: response.statusText,
      url: response.config.url,
      method: response.config.method?.toUpperCase(),
      data: response.data,
      headers: response.headers,
      requestId: response.config.headers['X-Request-ID'],
      timestamp: new Date().toISOString()
    }

    console.group(`✅ 收到响应: ${responseInfo.method} ${responseInfo.url} (${responseInfo.status})`)
    console.log('响应详情:', responseInfo)
    console.log('响应数据:', response.data)
    console.groupEnd()

    return response
  },
  error => {
    // 对响应错误做点什么
    const errorInfo = {
      message: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText,
      url: error.config?.url,
      method: error.config?.method?.toUpperCase(),
      data: error.response?.data,
      requestId: error.config?.headers?.['X-Request-ID'],
      timestamp: new Date().toISOString()
    }

    console.group(`❌ 响应错误: ${errorInfo.method} ${errorInfo.url} (${errorInfo.status})`)
    console.error('错误详情:', errorInfo)
    console.error('完整错误:', error)

    // 提供错误处理建议
    if (errorInfo.status === 401) {
      console.warn('💡 建议: 检查token是否有效，可能需要重新登录')
    } else if (errorInfo.status === 403) {
      console.warn('💡 建议: 当前用户权限不足，请联系管理员')
    } else if (errorInfo.status === 404) {
      console.warn('💡 建议: 接口路径不存在，请检查API地址')
    } else if (errorInfo.status >= 500) {
      console.warn('💡 建议: 服务器错误，请稍后重试或联系技术支持')
    } else if (!error.response) {
      console.warn('💡 建议: 网络连接问题，请检查网络设置或代理配置')
    }

    console.groupEnd()

    // 增强错误对象，提供更多上下文信息
    if (error.response) {
      error.friendlyMessage = getFriendlyErrorMessage(error.response.status, error.response.data)
    } else if (error.code === 'ECONNABORTED') {
      error.friendlyMessage = '请求超时，请检查网络连接'
    } else if (!error.response) {
      error.friendlyMessage = '网络连接失败，请检查网络设置'
    }

    return Promise.reject(error)
  }
)

// 获取友好的错误提示信息
function getFriendlyErrorMessage(status, data) {
  const message = data?.message || data?.msg || '';

  switch (status) {
    case 400:
      return message || '请求参数错误';
    case 401:
      return '登录已过期，请重新登录';
    case 403:
      return '权限不足，无法访问该资源';
    case 404:
      return '请求的资源不存在';
    case 408:
      return '请求超时，请稍后重试';
    case 429:
      return '请求过于频繁，请稍后重试';
    case 500:
      return '服务器内部错误，请稍后重试';
    case 502:
      return '网关错误，请稍后重试';
    case 503:
      return '服务暂时不可用，请稍后重试';
    case 504:
      return '网关超时，请稍后重试';
    default:
      return message || `请求失败 (${status})`;
  }
}

export default request
