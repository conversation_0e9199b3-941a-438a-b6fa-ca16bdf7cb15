<template>
  <div class="agent-workspace">
    <!-- 顶部工具栏 -->
    <div class="workspace-header">
      <div class="agent-info">
        <el-avatar :src="agentInfo.avatar" size="small">{{ agentInfo.name }}</el-avatar>
        <div class="agent-details">
          <span class="agent-name">{{ agentInfo.name }}</span>
          <el-tag :type="statusType" size="mini">{{ statusText }}</el-tag>
        </div>
      </div>
      
      <div class="workspace-controls">
        <el-select v-model="agentStatus" @change="updateStatus" size="small">
          <el-option label="在线" value="online"></el-option>
          <el-option label="忙碌" value="busy"></el-option>
          <el-option label="休息" value="break"></el-option>
          <el-option label="离线" value="offline"></el-option>
        </el-select>
        
        <el-badge :value="waitingCount" class="waiting-badge">
          <el-button type="primary" size="small" @click="showWaitingQueue">
            等待队列
          </el-button>
        </el-badge>
        
        <el-button type="info" size="small" @click="showStatistics">
          今日统计
        </el-button>
      </div>
    </div>

    <!-- 主工作区 -->
    <div class="workspace-main">
      <!-- 左侧会话列表 -->
      <div class="session-list">
        <div class="session-list-header">
          <h3>待处理消息 ({{ pendingRooms.length }})</h3>
          <el-button type="text" @click="refreshPendingRooms" icon="el-icon-refresh"></el-button>
        </div>

        <div class="session-items">
          <div
            v-for="room in pendingRooms"
            :key="room.roomId"
            class="session-item"
            @click="takeOverRoom(room)"
          >
            <div class="session-user">
              <el-avatar :src="room.userAvatar" size="small">
                {{ room.userName.charAt(0) }}
              </el-avatar>
              <div class="user-info">
                <div class="user-name">{{ room.userName }}</div>
                <div class="session-subject">{{ room.latestMessage }}</div>
              </div>
            </div>

            <div class="session-meta">
              <div class="session-time">{{ formatTime(room.latestMessageTime) }}</div>
              <el-badge :value="room.unreadCount" v-if="room.unreadCount > 0"></el-badge>
            </div>
          </div>

          <div v-if="pendingRooms.length === 0" class="no-pending">
            <el-empty description="暂无待处理消息" :image-size="80"></el-empty>
          </div>
        </div>
      </div>

      <!-- 中间聊天区域 -->
      <div class="chat-area" v-if="currentRoom">
        <!-- 聊天头部 -->
        <div class="chat-header">
          <div class="chat-user-info">
            <el-avatar :src="currentRoom.userAvatar">
              {{ currentRoom.userName.charAt(0) }}
            </el-avatar>
            <div class="user-details">
              <div class="user-name">{{ currentRoom.userName }}</div>
              <div class="session-info">
                <span>聊天室: {{ currentRoom.roomCode }}</span>
                <span>用户ID: {{ currentRoom.userId }}</span>
              </div>
            </div>
          </div>
          
          <div class="chat-actions">
            <el-button type="text" icon="el-icon-user">用户信息</el-button>
            <el-button type="text" icon="el-icon-time">历史记录</el-button>
            <el-button type="text" icon="el-icon-close" @click="closeCurrentRoom">结束会话</el-button>
          </div>
        </div>

        <!-- 消息区域 -->
        <div class="messages-container" ref="messagesContainer">
          <div 
            v-for="message in currentMessages" 
            :key="message.messageId"
            class="message-item"
            :class="message.senderType"
          >
            <div class="message-avatar">
              <el-avatar :src="getMessageAvatar(message)" size="small">
                {{ getMessageSender(message).charAt(0) }}
              </el-avatar>
            </div>
            
            <div class="message-content">
              <div class="message-header">
                <span class="sender-name">{{ getMessageSender(message) }}</span>
                <span class="message-time">{{ formatMessageTime(message.sendTime) }}</span>
              </div>
              
              <div class="message-body">
                <div v-if="message.messageType === 'text'" class="text-message">
                  {{ message.content }}
                </div>
                <div v-else-if="message.messageType === 'image'" class="image-message">
                  <el-image :src="message.fileUrl" :preview-src-list="[message.fileUrl]"></el-image>
                </div>
                <div v-else-if="message.messageType === 'file'" class="file-message">
                  <i class="el-icon-document"></i>
                  <span>{{ message.fileName }}</span>
                  <el-button type="text" @click="downloadFile(message)">下载</el-button>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 正在输入提示 -->
          <div v-if="userTyping" class="typing-indicator">
            <span>{{ currentSession.userInfo.userName }} 正在输入...</span>
          </div>
        </div>

        <!-- 输入区域 -->
        <div class="input-area">
          <div class="input-toolbar">
            <el-button type="text" @click="showQuickReplies" icon="el-icon-chat-line-square">快捷回复</el-button>
            <el-button type="text" @click="selectImage" icon="el-icon-picture">图片</el-button>
            <el-button type="text" @click="selectFile" icon="el-icon-paperclip">文件</el-button>
            <el-button type="text" @click="showEmoji" icon="el-icon-sunny">表情</el-button>
          </div>
          
          <div class="input-box">
            <el-input
              v-model="inputMessage"
              type="textarea"
              :rows="3"
              placeholder="输入回复消息..."
              @keydown.enter.prevent="handleEnterKey"
              @input="handleTyping"
              resize="none"
            ></el-input>
            
            <div class="input-actions">
              <el-button 
                type="primary" 
                @click="sendMessage"
                :disabled="!inputMessage.trim()"
                size="small"
              >
                发送 (Ctrl+Enter)
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else class="empty-chat">
        <el-empty description="请选择一个会话开始聊天"></el-empty>
      </div>

      <!-- 右侧信息面板 -->
      <div class="info-panel" v-if="showInfoPanel">
        <el-tabs v-model="activeInfoTab">
          <el-tab-pane label="用户信息" name="user">
            <div class="user-profile" v-if="currentRoom">
              <div class="profile-header">
                <el-avatar :src="currentRoom.userAvatar" size="large">
                  {{ currentRoom.userName.charAt(0) }}
                </el-avatar>
                <h4>{{ currentRoom.userName }}</h4>
              </div>

              <div class="profile-details">
                <div class="detail-item">
                  <label>用户ID:</label>
                  <span>{{ currentRoom.userId }}</span>
                </div>
                <div class="detail-item">
                  <label>聊天室ID:</label>
                  <span>{{ currentRoom.roomId }}</span>
                </div>
                <div class="detail-item">
                  <label>聊天室代码:</label>
                  <span>{{ currentRoom.roomCode }}</span>
                </div>
                <div class="detail-item">
                  <label>最新消息时间:</label>
                  <span>{{ formatDate(currentRoom.latestMessageTime) }}</span>
                </div>
              </div>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="快捷回复" name="quick">
            <div class="quick-replies">
              <div class="reply-categories">
                <el-button 
                  v-for="category in quickReplyCategories" 
                  :key="category.name"
                  :type="selectedCategory === category.name ? 'primary' : 'text'"
                  size="mini"
                  @click="selectCategory(category.name)"
                >
                  {{ category.label }}
                </el-button>
              </div>
              
              <div class="reply-templates">
                <div 
                  v-for="template in filteredTemplates" 
                  :key="template.id"
                  class="template-item"
                  @click="useTemplate(template)"
                >
                  <div class="template-title">{{ template.title }}</div>
                  <div class="template-content">{{ template.content }}</div>
                </div>
              </div>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="会话记录" name="history">
            <div class="session-history">
              <div class="history-filters">
                <el-date-picker
                  v-model="historyDateRange"
                  type="daterange"
                  size="mini"
                  placeholder="选择日期范围"
                ></el-date-picker>
              </div>
              
              <div class="history-list">
                <div 
                  v-for="session in userHistorySessions" 
                  :key="session.sessionId"
                  class="history-item"
                  @click="viewHistorySession(session)"
                >
                  <div class="history-subject">{{ session.subject }}</div>
                  <div class="history-time">{{ formatDate(session.startTime) }}</div>
                  <div class="history-status">{{ session.status }}</div>
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>

    <!-- 等待队列弹窗 -->
    <el-dialog title="等待队列" :visible.sync="showWaitingDialog" width="600px">
      <div class="waiting-queue">
        <div class="queue-header">
          <span>当前等待: {{ waitingQueue.length }} 人</span>
          <span>平均等待时间: {{ avgWaitTime }} 分钟</span>
        </div>
        
        <el-table :data="waitingQueue" style="width: 100%">
          <el-table-column prop="userInfo.userName" label="用户" width="120"></el-table-column>
          <el-table-column prop="subject" label="咨询主题" width="150"></el-table-column>
          <el-table-column prop="priority" label="优先级" width="80">
            <template slot-scope="scope">
              <el-tag :type="getPriorityType(scope.row.priority)" size="mini">
                {{ scope.row.priority }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="waitTime" label="等待时间" width="100"></el-table-column>
          <el-table-column label="操作" width="100">
            <template slot-scope="scope">
              <el-button type="primary" size="mini" @click="acceptWaitingSession(scope.row)">
                接受
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>

    <!-- 隐藏的文件输入 -->
    <input ref="fileInput" type="file" style="display: none" @change="handleFileSelect">
  </div>
</template>

<script>
import axios from 'axios'
import ChatService from '@/utils/chatService'

export default {
  name: 'AgentWorkspace',
  data() {
    return {
      // 客服信息
      agentInfo: {
        agentId: null,
        name: '',
        avatar: '',
        status: 'offline'
      },
      agentStatus: 'offline',
      maxConcurrentChats: 5,

      // 待处理消息列表
      pendingRooms: [],

      // 当前聊天室
      currentRoom: null,
      currentMessages: [],

      // 消息输入
      inputMessage: '',
      userTyping: false,

      // 界面状态
      showInfoPanel: true,
      activeInfoTab: 'user',

      // 快捷回复
      quickReplyCategories: [
        { name: 'greeting', label: '问候语' },
        { name: 'common', label: '常用回复' },
        { name: 'technical', label: '技术支持' },
        { name: 'closing', label: '结束语' }
      ],
      selectedCategory: 'greeting',
      quickReplyTemplates: [],

      // 历史记录
      userHistorySessions: [],
      historyDateRange: null,

      // WebSocket连接
      websocket: null,
      isConnected: false,

      // 定时器
      typingTimer: null,
      refreshTimer: null
    }
  },
  computed: {
    statusType() {
      const typeMap = {
        online: 'success',
        busy: 'warning',
        break: 'info',
        offline: 'danger'
      }
      return typeMap[this.agentStatus] || 'info'
    },
    
    statusText() {
      const textMap = {
        online: '在线',
        busy: '忙碌',
        break: '休息中',
        offline: '离线'
      }
      return textMap[this.agentStatus] || '未知'
    },
    
    sessionDuration() {
      if (!this.currentSession) return '0分钟'
      const start = new Date(this.currentSession.startTime)
      const now = new Date()
      const minutes = Math.floor((now - start) / 60000)
      return `${minutes}分钟`
    },
    
    filteredTemplates() {
      return this.quickReplyTemplates.filter(t => t.category === this.selectedCategory)
    }
  },
  
  async mounted() {
    // 初始化客服工作台
    await this.init()
  },

  beforeDestroy() {
    this.cleanup()
  },
  
  methods: {
    // 初始化客服工作台
    async init() {
      try {
        // 1. 获取客服信息
        await this.loadAgentInfo()

        // 2. 加载待处理消息列表
        await this.loadPendingRooms()

        // 3. 连接WebSocket
        await this.connectWebSocket()

        // 4. 定时刷新待处理列表
        this.startRefreshTimer()

        console.log('✅ 客服工作台初始化完成')
      } catch (error) {
        console.error('❌ 客服工作台初始化失败:', error)
        this.$message.error('工作台初始化失败')
      }
    },

    // 获取客服信息
    async loadAgentInfo() {
      try {
        const token = localStorage.getItem('token')
        const response = await axios.get('/api/agent/dashboard', {
          headers: { 'Authorization': token }
        })

        if (response.data.code === 200) {
          this.agentInfo = response.data.data.agentInfo
          this.agentStatus = this.agentInfo.status
          this.maxConcurrentChats = this.agentInfo.maxConcurrentChats
        }
      } catch (error) {
        console.error('获取客服信息失败:', error)
      }
    },

    // 加载待处理消息列表
    async loadPendingRooms() {
      try {
        this.pendingRooms = await ChatService.getPendingRooms()
        console.log('📋 待处理消息列表:', this.pendingRooms)
      } catch (error) {
        console.error('❌ 获取待处理消息失败:', error)
        if (error.message.includes('403') || error.message.includes('权限')) {
          this.$message.warning('您没有客服权限')
        } else {
          this.$message.error('获取待处理消息失败：' + (error.message || '未知错误'))
        }
      }
    },
    
    // WebSocket连接管理
    async connectWebSocket() {
      try {
        const token = localStorage.getItem('token')
        if (!token) {
          this.$message.warning('请先登录')
          return
        }

        console.log('🔗 客服端连接WebSocket...')
        await ChatService.connect(token)
        this.isConnected = true
        console.log('✅ 客服端WebSocket连接已建立')
      } catch (error) {
        console.error('❌ 客服端WebSocket连接失败:', error)
        this.isConnected = false
        this.$message.error('WebSocket连接失败')
      }
    },

    // 接管聊天室
    async takeOverRoom(room) {
      try {
        const data = await ChatService.takeOverRoom(room.roomCode)

        this.currentRoom = {
          ...room,
          roomCode: data.roomCode,
          roomId: data.roomId,
          userId: data.userId
        }

        // 显示历史消息
        this.currentMessages = (data.messages || []).map(msg => this.formatMessage(msg))

        // 订阅聊天室消息
        await ChatService.subscribeToRoom(room.roomCode, this.handleWebSocketMessage)

        // 加入聊天室
        await ChatService.joinRoom(room.roomCode)

        // 从待处理列表移除
        this.pendingRooms = this.pendingRooms.filter(r => r.roomId !== room.roomId)

        this.$message.success(`已接管用户 ${room.userName} 的聊天`)

        this.$nextTick(() => {
          this.scrollToBottom()
        })
      } catch (error) {
        console.error('❌ 接管聊天室失败:', error)
        this.$message.error('接管失败：' + (error.message || '未知错误'))
      }
    },

    // 处理WebSocket消息
    handleWebSocketMessage(data) {
      console.log('📥 客服端收到WebSocket消息:', data)

      // 过滤掉用户加入通知
      if (data.type === 'USER_JOINED') {
        return
      }

      // 添加消息到当前聊天记录
      if (this.currentRoom && data.roomCode === this.currentRoom.roomCode) {
        const formattedMessage = this.formatMessage(data)
        this.currentMessages.push(formattedMessage)

        this.$nextTick(() => {
          this.scrollToBottom()
        })
      }
    },

    // 处理聊天消息
    handleChatMessage(message) {
      if (this.currentRoom && message.roomCode === this.currentRoom.roomCode) {
        const formattedMessage = this.formatMessage(message)
        this.currentMessages.push(formattedMessage)

        this.$nextTick(() => {
          this.scrollToBottom()
        })
      }
    },

    // 格式化消息
    formatMessage(message) {
      return {
        id: message.id,
        type: message.senderType === 1 ? 'user' : 'agent', // 1=用户, 2=客服
        text: message.content,
        time: new Date(message.createTime),
        messageId: message.id,
        senderType: message.senderType,
        senderName: message.senderName,
        messageType: message.messageType, // 1=文本, 2=图片, 3=文件
        fileUrl: message.fileUrl,
        fileName: message.fileName
      }
    },

    // 发送消息
    async sendMessage() {
      if (!this.inputMessage.trim()) return
      if (!this.currentRoom) {
        this.$message.warning('请先选择一个聊天室')
        return
      }
      if (!this.isConnected) {
        this.$message.warning('连接已断开，请稍后重试')
        return
      }

      const content = this.inputMessage
      this.inputMessage = ''

      // 立即显示客服消息（乐观更新）
      this.currentMessages.push({
        type: 'agent',
        text: content,
        time: new Date(),
        senderType: 2, // 2=客服
        senderName: this.agentInfo.name,
        messageId: this.generateMessageId()
      })

      // 发送消息到服务器
      try {
        await ChatService.sendMessage(this.currentRoom.roomCode, content)
        console.log('✅ 客服消息已发送到服务器:', content)
      } catch (error) {
        console.error('❌ 客服发送消息失败:', error)
        this.$message.error('发送消息失败：' + (error.message || '未知错误'))
      }

      this.stopTyping()

      this.$nextTick(() => {
        this.scrollToBottom()
      })
    },

    generateMessageId() {
      return 'msg_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11)
    },

    handleEnterKey(event) {
      if (event.ctrlKey) {
        this.sendMessage()
      }
    },

    handleTyping() {
      // 简化的输入状态处理
      if (!this.typingTimer) {
        this.typingTimer = setTimeout(() => {
          this.stopTyping()
        }, 3000)
      }
    },

    stopTyping() {
      if (this.typingTimer) {
        clearTimeout(this.typingTimer)
        this.typingTimer = null
      }
    },

    // 定时刷新待处理列表
    startRefreshTimer() {
      this.refreshTimer = setInterval(() => {
        this.loadPendingRooms()
      }, 10000) // 每10秒刷新一次
    },

    // 刷新待处理列表
    async refreshPendingRooms() {
      await this.loadPendingRooms()
    },

    // 状态管理方法
    async updateStatus() {
      try {
        const token = localStorage.getItem('token')
        await axios.post('/api/agent/status', {
          status: this.agentStatus,
          maxConcurrentChats: this.maxConcurrentChats
        }, {
          headers: { 'Authorization': token }
        })
        this.$message.success('状态更新成功')
      } catch (error) {
        console.error('状态更新失败:', error)
        this.$message.error('状态更新失败')
      }
    },

    // 工具方法
    formatTime(time) {
      return new Date(time).toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit'
      })
    },

    formatMessageTime(time) {
      const now = new Date()
      const messageTime = new Date(time)
      const diffMinutes = Math.floor((now - messageTime) / 60000)

      if (diffMinutes < 1) return '刚刚'
      if (diffMinutes < 60) return `${diffMinutes}分钟前`
      if (diffMinutes < 1440) return `${Math.floor(diffMinutes / 60)}小时前`
      return messageTime.toLocaleDateString('zh-CN')
    },

    formatDate(date) {
      return new Date(date).toLocaleDateString('zh-CN')
    },

    getPriorityType(priority) {
      const typeMap = {
        low: 'info',
        normal: '',
        high: 'warning',
        urgent: 'danger'
      }
      return typeMap[priority] || ''
    },

    getMessageAvatar(message) {
      if (message.senderType === 'agent') {
        return this.agentInfo.avatar
      } else {
        return this.currentSession?.userInfo?.avatar
      }
    },

    getMessageSender(message) {
      if (message.senderType === 'agent') {
        return this.agentInfo.name
      } else {
        return this.currentSession?.userInfo?.userName || '用户'
      }
    },

    scrollToBottom() {
      if (this.$refs.messagesContainer) {
        this.$refs.messagesContainer.scrollTop = this.$refs.messagesContainer.scrollHeight
      }
    },

    // 关闭当前聊天室
    closeCurrentRoom() {
      this.$confirm('确定要结束当前会话吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.currentRoom = null
        this.currentMessages = []
        this.$message.success('会话已结束')
      }).catch(() => {
        // 用户取消
      })
    },

    // 资源清理
    cleanup() {
      // 清理WebSocket连接
      if (this.websocket) {
        this.websocket.close()
        this.websocket = null
      }

      // 清理定时器
      if (this.refreshTimer) {
        clearInterval(this.refreshTimer)
        this.refreshTimer = null
      }

      if (this.typingTimer) {
        clearTimeout(this.typingTimer)
        this.typingTimer = null
      }
    }
  }
}
</script>

<style scoped>
.agent-workspace {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
}

/* 顶部工具栏 */
.workspace-header {
  background: white;
  padding: 15px 20px;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.agent-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.agent-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.agent-name {
  font-weight: 600;
  color: #303133;
}

.workspace-controls {
  display: flex;
  align-items: center;
  gap: 15px;
}

.waiting-badge {
  margin-right: 10px;
}

/* 主工作区 */
.workspace-main {
  flex: 1;
  display: flex;
  overflow: hidden;
}

/* 左侧会话列表 */
.session-list {
  width: 320px;
  background: white;
  border-right: 1px solid #e4e7ed;
  display: flex;
  flex-direction: column;
}

.session-list-header {
  padding: 15px 20px;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.session-list-header h3 {
  margin: 0;
  font-size: 16px;
  color: #303133;
}

.session-items {
  flex: 1;
  overflow-y: auto;
}

.session-item {
  padding: 15px 20px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.session-item:hover {
  background: #f8f9fa;
}

.session-item.active {
  background: #ecf5ff;
  border-left: 3px solid #409eff;
}

.session-user {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.user-info {
  flex: 1;
  min-width: 0;
}

.user-name {
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.session-subject {
  font-size: 12px;
  color: #909399;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.session-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.session-time {
  font-size: 11px;
  color: #c0c4cc;
}

.no-pending {
  padding: 40px 20px;
  text-align: center;
}

/* 聊天区域 */
.chat-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: white;
}

.chat-header {
  padding: 15px 20px;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #fafbfc;
}

.chat-user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.user-name {
  font-weight: 600;
  color: #303133;
}

.session-info {
  font-size: 12px;
  color: #909399;
  display: flex;
  gap: 15px;
}

.chat-actions {
  display: flex;
  gap: 10px;
}

/* 消息区域 */
.messages-container {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  background: #f8f9fa;
}

.message-item {
  display: flex;
  margin-bottom: 20px;
  animation: fadeInUp 0.3s ease-out;
}

.message-item.user {
  flex-direction: row-reverse;
}

.message-avatar {
  margin: 0 12px;
  flex-shrink: 0;
}

.message-content {
  max-width: 60%;
  min-width: 100px;
}

.message-item.user .message-content {
  text-align: right;
}

.message-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 6px;
}

.message-item.user .message-header {
  flex-direction: row-reverse;
}

.sender-name {
  font-size: 12px;
  font-weight: 600;
  color: #606266;
}

.message-time {
  font-size: 11px;
  color: #c0c4cc;
}

.message-body {
  background: white;
  padding: 12px 16px;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  word-wrap: break-word;
  line-height: 1.5;
}

.message-item.agent .message-body {
  background: #409eff;
  color: white;
}

.text-message {
  white-space: pre-wrap;
}

.image-message .el-image {
  max-width: 200px;
  border-radius: 8px;
}

.file-message {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #f0f0f0;
  border-radius: 8px;
}

.typing-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  font-style: italic;
  color: #909399;
  margin-bottom: 15px;
}

/* 输入区域 */
.input-area {
  border-top: 1px solid #e4e7ed;
  background: white;
}

.input-toolbar {
  padding: 10px 20px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  gap: 15px;
}

.input-box {
  padding: 15px 20px;
  display: flex;
  gap: 15px;
  align-items: flex-end;
}

.input-box .el-textarea {
  flex: 1;
}

.input-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* 空状态 */
.empty-chat {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
}

/* 信息面板 */
.info-panel {
  width: 300px;
  background: white;
  border-left: 1px solid #e4e7ed;
  display: flex;
  flex-direction: column;
}

.user-profile {
  padding: 20px;
}

.profile-header {
  text-align: center;
  margin-bottom: 20px;
}

.profile-header h4 {
  margin: 10px 0 0 0;
  color: #303133;
}

.profile-details {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.detail-item label {
  font-weight: 600;
  color: #606266;
}

.detail-item span {
  color: #303133;
}

/* 快捷回复 */
.quick-replies {
  padding: 15px;
}

.reply-categories {
  margin-bottom: 15px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.reply-templates {
  max-height: 400px;
  overflow-y: auto;
}

.template-item {
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  margin-bottom: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.template-item:hover {
  border-color: #409eff;
  background: #ecf5ff;
}

.template-title {
  font-weight: 600;
  color: #303133;
  margin-bottom: 6px;
}

.template-content {
  font-size: 12px;
  color: #606266;
  line-height: 1.4;
}

/* 等待队列弹窗 */
.waiting-queue {
  padding: 10px 0;
}

.queue-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
  padding: 0 10px;
  font-size: 14px;
  color: #606266;
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 滚动条样式 */
.session-items::-webkit-scrollbar,
.messages-container::-webkit-scrollbar,
.reply-templates::-webkit-scrollbar {
  width: 6px;
}

.session-items::-webkit-scrollbar-track,
.messages-container::-webkit-scrollbar-track,
.reply-templates::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.session-items::-webkit-scrollbar-thumb,
.messages-container::-webkit-scrollbar-thumb,
.reply-templates::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.session-items::-webkit-scrollbar-thumb:hover,
.messages-container::-webkit-scrollbar-thumb:hover,
.reply-templates::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .info-panel {
    width: 250px;
  }

  .session-list {
    width: 280px;
  }
}

@media (max-width: 768px) {
  .workspace-main {
    flex-direction: column;
  }

  .session-list {
    width: 100%;
    height: 200px;
  }

  .info-panel {
    display: none;
  }
}
</style>
