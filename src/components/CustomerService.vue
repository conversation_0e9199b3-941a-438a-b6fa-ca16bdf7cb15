<template>
  <div class="customer-service-container">
    <!-- 客服图标按钮 -->
    <div v-if="!isOpen" class="service-icon" @click="toggleChat" :style="iconPosition">
      <i class="el-icon-service"></i>
      <div class="service-badge" v-if="unreadCount > 0">{{ unreadCount }}</div>
    </div>

    <!-- 聊天窗口 -->
    <div v-if="isOpen" class="chat-window" :style="windowPosition" ref="chatWindow">
      <!-- 聊天窗口头部 -->
      <div class="chat-header" @mousedown="startDrag">
        <div class="header-left">
          <div class="service-avatar">
            <i class="el-icon-service"></i>
          </div>
          <div class="service-info">
            <div class="service-name">在线客服</div>
            <div class="service-status" :class="serviceStatus">
              {{ serviceStatusText }}
            </div>
          </div>
        </div>
        <div class="header-actions">
          <el-button type="text" icon="el-icon-warning" @click="showDiagnosis" size="mini" title="诊断问题"></el-button>
          <el-button type="text" icon="el-icon-minus" @click="minimizeChat" size="mini"></el-button>
          <el-button type="text" icon="el-icon-close" @click="closeChat" size="mini"></el-button>
        </div>
      </div>

      <!-- 聊天消息区域 -->
      <div class="chat-messages" ref="messagesContainer">
        <div v-for="(message, index) in messages" :key="index" class="message-item" :class="message.type">
          <div class="message-avatar">
            <i :class="message.type === 'user' ? 'el-icon-user' : 'el-icon-service'"></i>
          </div>
          <div class="message-content">
            <div class="message-text">{{ message.text }}</div>
            <div class="message-time">{{ formatTime(message.time) }}</div>
          </div>
        </div>

        <!-- 客服正在输入提示 -->
        <div v-if="isTyping" class="typing-indicator">
          <div class="message-avatar">
            <i class="el-icon-service"></i>
          </div>
          <div class="typing-dots">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </div>
      </div>

      <!-- 输入区域 -->
      <div class="chat-input">
        <div class="input-toolbar">
          <el-button type="text" icon="el-icon-picture" size="mini" @click="selectImage"></el-button>
          <el-button type="text" icon="el-icon-paperclip" size="mini" @click="selectFile"></el-button>
        </div>
        <div class="input-area">
          <el-input v-model="inputMessage" type="textarea" :rows="2" placeholder="请输入您的问题..."
            @keydown.enter.prevent="handleEnterKey" @input="handleTyping" resize="none"></el-input>
          <el-button type="primary" size="small" @click="sendMessage" :disabled="!inputMessage.trim()" class="send-btn">
            发送
          </el-button>
        </div>
      </div>
    </div>

    <!-- 隐藏的文件输入 -->
    <input ref="fileInput" type="file" style="display: none" @change="handleFileSelect"
      accept="image/*,.pdf,.doc,.docx,.txt">

    <!-- 快速诊断组件 -->
    <QuickDiagnosis :visible.sync="showDiagnosisDialog" />
  </div>
</template>

<script>
import axios from 'axios'
import ChatService from '@/utils/chatService'
import ConnectionTest from '@/utils/connectionTest'
import SystemDiagnosis from '@/utils/systemDiagnosis'
import QuickDiagnosis from './QuickDiagnosis.vue'

export default {
  name: 'CustomerService',
  components: {
    QuickDiagnosis
  },
  data() {
    return {
      isOpen: false,
      inputMessage: '',
      messages: [],
      unreadCount: 0,
      serviceStatus: 'offline', // online, busy, offline
      isTyping: false,
      isDragging: false,
      dragOffset: { x: 0, y: 0 },
      windowPosition: {
        right: '20px',
        bottom: '20px'
      },
      iconPosition: {
        right: '20px',
        bottom: '20px'
      },
      // WebSocket连接状态
      isConnected: false,
      // 聊天室相关
      roomCode: null,
      roomId: null,
      roomStatus: 0, // 0=关闭, 1=活跃, 2=等待客服
      customerServiceAssigned: false,
      // 输入状态
      typingTimer: null,
      isUserTyping: false,
      // 诊断对话框
      showDiagnosisDialog: false
    }
  },
  computed: {
    serviceStatusText() {
      const statusMap = {
        online: '在线',
        busy: '忙碌',
        offline: '离线'
      }
      return statusMap[this.serviceStatus] || '未知'
    }
  },
  mounted() {
    // 添加全局鼠标事件监听
    document.addEventListener('mousemove', this.handleMouseMove)
    document.addEventListener('mouseup', this.handleMouseUp)

    // 页面关闭时清理资源
    window.addEventListener('beforeunload', this.cleanup)
  },
  beforeDestroy() {
    this.cleanup()
  },
  methods: {
    // 显示诊断对话框
    showDiagnosis() {
      this.showDiagnosisDialog = true
    },

    // STOMP WebSocket连接管理
    async initWebSocketConnection() {
      const token = localStorage.getItem('token')
      if (!token) {
        console.warn('未找到用户token，无法建立WebSocket连接')
        this.$message.warning('请先登录')
        return false
      }

      try {
        // 首先测试后端是否可达
        console.log('🔗 开始建立WebSocket连接...')
        console.log('🔍 测试后端连接状态...')

        try {
          // 测试HTTP连接
          const testResponse = await axios.get('/api/chat/test', {
            headers: { 'Authorization': token },
            timeout: 3000
          })
          console.log('✅ 后端HTTP连接正常:', testResponse.status)
        } catch (httpError) {
          console.warn('⚠️ 后端HTTP连接测试失败:', httpError.message)
          // 继续尝试WebSocket连接
        }

        // 创建SockJS连接 - 关键修复：使用正确的URL格式
        const wsUrl = '/ws/chat'
        console.log('🔗 创建SockJS连接:', wsUrl)
        console.log('🔗 当前页面URL:', window.location.href)
        console.log('🔗 WebSocket将连接到:', window.location.protocol + '//' + window.location.host + wsUrl)

        const socket = new SockJS(wsUrl)
        this.stompClient = Stomp.over(socket)

        // 启用调试信息以便排查问题
        this.stompClient.debug = (str) => {
          console.log('🔍 STOMP Debug:', str)
        }

        // 设置连接头部（包含认证token）- 关键修复：确保token格式正确
        const connectHeaders = {
          'Authorization': token
        }

        console.log('🔑 WebSocket连接头部:', connectHeaders)
        console.log('🔑 Token内容:', token.substring(0, 20) + '...')

        // 连接到WebSocket
        await new Promise((resolve, reject) => {
          const connectTimeout = setTimeout(() => {
            console.error('❌ WebSocket连接超时')
            reject(new Error('WebSocket连接超时'))
          }, 15000) // 15秒超时

          this.stompClient.connect(connectHeaders,
            (frame) => {
              clearTimeout(connectTimeout)
              console.log('✅ STOMP连接已建立:', frame)
              console.log('✅ 连接状态:', this.stompClient.connected)
              console.log('✅ 会话ID:', frame.headers['session'])
              this.isConnected = true
              this.serviceStatus = 'online'
              this.reconnectAttempts = 0
              resolve(frame)
            },
            (error) => {
              clearTimeout(connectTimeout)
              console.error('❌ STOMP连接失败:', error)
              console.error('❌ 错误类型:', typeof error)
              console.error('❌ 错误详情:', error)

              // 更详细的错误分析
              if (typeof error === 'string') {
                console.error('❌ 错误字符串:', error)
              } else if (error && error.headers) {
                console.error('❌ STOMP错误头:', error.headers)
                console.error('❌ STOMP错误消息:', error.headers.message)
              }

              this.isConnected = false
              this.serviceStatus = 'offline'
              reject(error)
            }
          )
        })

        return true
      } catch (error) {
        console.error('❌ 初始化WebSocket连接失败:', error)
        console.error('❌ 错误堆栈:', error.stack)
        this.isConnected = false
        this.serviceStatus = 'offline'

        // 提供更详细的错误信息
        if (error.message.includes('timeout')) {
          this.$message.error('WebSocket连接超时，请检查网络连接')
        } else if (error.message.includes('refused')) {
          this.$message.error('无法连接到服务器，请确认后端服务已启动')
        } else {
          this.$message.error('WebSocket连接失败: ' + error.message)
        }

        return false
      }
    },

    // 订阅聊天室消息
    subscribeToRoom() {
      if (!this.stompClient || !this.roomCode || !this.isConnected) {
        console.warn('⚠️ 无法订阅聊天室，连接未建立或房间代码为空')
        return
      }

      try {
        // 订阅聊天室主题
        this.stompClient.subscribe(`/topic/chat/${this.roomCode}`, (message) => {
          try {
            const data = JSON.parse(message.body)
            console.log('📨 收到聊天室消息:', data)

            if (data.type === 'USER_JOINED') {
              // 用户加入通知，不显示在聊天中
              console.log('👤 用户加入通知:', data.message)
            } else {
              // 普通聊天消息
              this.handleWebSocketMessage(data)
            }
          } catch (error) {
            console.error('❌ 解析聊天室消息失败:', error)
          }
        })

        console.log('✅ 已订阅聊天室:', this.roomCode)
      } catch (error) {
        console.error('❌ 订阅聊天室失败:', error)
      }
    },

    // 加入聊天室
    joinRoom() {
      if (!this.stompClient || !this.roomCode || !this.isConnected) {
        console.warn('⚠️ 无法加入聊天室，连接未建立或房间代码为空')
        return
      }

      try {
        // 发送加入聊天室消息
        this.stompClient.send(`/app/chat.joinRoom/${this.roomCode}`, {}, JSON.stringify({}))
        console.log('✅ 已加入聊天室:', this.roomCode)
      } catch (error) {
        console.error('❌ 加入聊天室失败:', error)
      }
    },

    // 聊天界面控制
    async toggleChat() {
      console.log('🎯 toggleChat called, isOpen:', this.isOpen, 'roomCode:', this.roomCode)
      this.isOpen = !this.isOpen
      if (this.isOpen) {
        this.unreadCount = 0

        // 如果没有聊天室，快速开始聊天
        if (!this.roomCode) {
          console.log('🚀 开始快速聊天...')
          try {
            await this.quickStartChat()
          } catch (error) {
            console.error('❌ 打开聊天窗口时快速开始聊天失败:', error)
            // 如果快速开始失败，关闭聊天窗口
            this.isOpen = false
            return
          }
        }

        this.$nextTick(() => {
          this.scrollToBottom()
        })
      }
    },

    closeChat() {
      this.isOpen = false
      // 注意：这里不主动结束聊天室，让用户可以重新打开继续聊天
    },

    minimizeChat() {
      this.isOpen = false
    },

    // 快速开始聊天（核心方法）
    async quickStartChat() {
      try {
        const token = localStorage.getItem('token')
        if (!token) {
          this.$message.warning('请先登录')
          throw new Error('未找到用户token')
        }

        console.log('🚀 开始快速聊天...')
        this.serviceStatus = 'connecting'

        // 先进行快速诊断
        console.log('🔍 进行连接诊断...')
        const diagnosis = await SystemDiagnosis.quickDiagnose()

        if (!diagnosis.success) {
          console.error('❌ 连接诊断失败:', diagnosis)
          this.$message.error(diagnosis.message)
          if (diagnosis.solution) {
            console.log('💡 解决方案:', diagnosis.solution)
            // 显示详细的解决方案
            this.$message({
              type: 'warning',
              message: `解决方案: ${diagnosis.solution}`,
              duration: 8000
            })
          }
          throw new Error(diagnosis.message)
        }

        // 调用快速开始聊天接口
        console.log('🔍 检查后端服务连接...')
        const data = await ChatService.quickStartChat()

        this.roomCode = data.roomCode
        this.roomId = data.roomId
        this.roomStatus = data.status
        this.customerServiceAssigned = data.customerServiceAssigned

        // 加载最近消息
        if (data.recentMessages && data.recentMessages.length > 0) {
          this.messages = data.recentMessages.map(msg => ({
            type: msg.senderType === 1 ? 'user' : 'service',
            text: msg.content,
            time: new Date(msg.createTime),
            id: msg.id
          }))
        }

        console.log('✅ 快速开始聊天成功:', {
          roomCode: this.roomCode,
          roomId: this.roomId,
          status: this.roomStatus,
          customerServiceAssigned: this.customerServiceAssigned,
          messagesCount: this.messages.length
        })

        // 连接WebSocket并加入聊天室
        try {
          await this.connectWebSocket()
        } catch (wsError) {
          console.warn('⚠️ WebSocket连接失败，但聊天室已创建:', wsError.message)
          // WebSocket连接失败不影响聊天室创建，继续显示聊天界面
        }

        // 添加系统消息
        if (!this.customerServiceAssigned) {
          this.addMessage({
            type: 'system',
            text: '您好！欢迎使用在线客服，正在为您分配客服人员，请稍候...',
            time: new Date()
          })
        }

        this.serviceStatus = 'online'
        return true

      } catch (error) {
        console.error('❌ 快速开始聊天失败:', error)
        this.serviceStatus = 'offline'

        // 详细的错误分析和处理
        if (error.response) {
          // 服务器返回了错误响应
          const status = error.response.status
          const message = error.response.data?.message || error.message

          console.error('❌ 服务器错误:', { status, message, data: error.response.data })

          if (status === 401 || status === 403) {
            this.$message.error('认证失败，请重新登录')
            this.$router.push('/login')
          } else if (status >= 500) {
            this.$message.error('服务器内部错误，请稍后重试')
          } else {
            this.$message.error(`请求失败: ${message}`)
          }
        } else if (error.request) {
          // 请求已发出但没有收到响应
          console.error('❌ 网络连接错误:', error.request)
          this.$message.error('无法连接到服务器，请检查网络连接或确认后端服务已启动')
        } else {
          // 其他错误
          console.error('❌ 其他错误:', error.message)
          if (error.message.includes('认证失败') || error.message.includes('401') || error.message.includes('403')) {
            this.$message.error('认证失败，请重新登录')
            this.$router.push('/login')
          } else {
            this.$message.error('启动聊天失败：' + (error.message || '未知错误'))
          }
        }
        throw error
      }
    },

    // 重置聊天状态
    resetChatState() {
      this.roomCode = null
      this.roomId = null
      this.roomStatus = 0
      this.customerServiceAssigned = false
      this.messages = []
    },

    // 连接WebSocket
    async connectWebSocket() {
      try {
        const token = localStorage.getItem('token')
        if (!token) {
          throw new Error('未找到用户token')
        }

        console.log('🔗 连接WebSocket...')

        // 使用ChatService连接WebSocket
        await ChatService.connect(token)

        // 订阅聊天室消息
        await ChatService.subscribeToRoom(this.roomCode, this.handleWebSocketMessage)

        // 加入聊天室
        await ChatService.joinRoom(this.roomCode)

        this.isConnected = true
        console.log('✅ WebSocket连接成功')

        return true
      } catch (error) {
        console.error('❌ WebSocket连接失败:', error)
        this.isConnected = false
        throw error
      }
    },

    // 处理WebSocket消息
    handleWebSocketMessage(data) {
      console.log('📥 收到WebSocket消息:', data)

      // 过滤掉用户加入通知
      if (data.type === 'USER_JOINED') {
        return
      }

      // 添加消息到聊天记录
      this.addMessage({
        type: data.senderType === 1 ? 'user' : 'service',
        text: data.content,
        time: new Date(data.createTime),
        id: data.id,
        senderName: data.senderName
      })

      // 如果是客服消息，停止输入状态
      if (data.senderType === 2) {
        this.isTyping = false
      }

      this.$nextTick(() => {
        this.scrollToBottom()
      })
    },

    // 消息发送
    async sendMessage() {
      if (!this.inputMessage.trim()) return

      // 调试信息
      console.log('🔍 发送消息调试信息:', {
        roomCode: this.roomCode,
        isConnected: this.isConnected,
        stompClient: !!this.stompClient,
        inputMessage: this.inputMessage
      })

      // 如果没有聊天室，先尝试建立
      if (!this.roomCode) {
        console.log('🔄 聊天室未建立，尝试快速开始聊天...')
        try {
          await this.quickStartChat()
          // 如果快速开始失败，不继续发送消息
          if (!this.roomCode) {
            console.error('❌ 快速开始聊天失败，无法发送消息')
            this.$message.error('聊天室创建失败，请重试')
            return
          }
        } catch (error) {
          console.error('❌ 快速开始聊天异常:', error)
          this.$message.error('聊天室创建失败，请重试')
          return
        }
      }

      const content = this.inputMessage
      // 先清空输入框，避免重复发送
      this.inputMessage = ''

      // 立即显示用户消息
      this.addMessage({
        type: 'user',
        text: content,
        time: new Date(),
        senderType: 1, // 1=用户
        messageId: this.generateMessageId()
      })

      // 发送到服务器
      console.log('🔍 发送消息前的状态检查:', {
        stompClient: !!this.stompClient,
        isConnected: this.isConnected,
        stompConnected: this.stompClient?.connected,
        roomCode: this.roomCode
      })

      // 发送消息到服务器
      try {
        await ChatService.sendMessage(this.roomCode, content)
        console.log('✅ 消息已发送到服务器:', content)
      } catch (error) {
        console.error('❌ 发送消息失败:', error)
        this.$message.error('发送消息失败：' + (error.message || '未知错误'))
      }

      this.stopUserTyping()

      this.$nextTick(() => {
        this.scrollToBottom()
      })
    },

    // 消息处理方法（保持兼容性）
    handleChatMessage(message) {
      this.handleWebSocketMessage(message)
    },

    // 格式化消息
    formatMessage(message) {
      return {
        id: message.id,
        type: message.senderType === 1 ? 'user' : 'service', // 1=用户, 2=客服
        text: message.content,
        time: new Date(message.createTime),
        messageId: message.id,
        senderType: message.senderType,
        senderName: message.senderName,
        messageType: message.messageType, // 1=文本, 2=图片, 3=文件
        fileUrl: message.fileUrl,
        fileName: message.fileName
      }
    },

    // 消息管理
    addMessage(message) {
      this.messages.push(message)

      // 限制消息数量，避免内存泄漏
      if (this.messages.length > 100) {
        this.messages = this.messages.slice(-50)
      }
    },

    generateMessageId() {
      return 'msg_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11)
    },

    handleEnterKey(event) {
      if (!event.shiftKey) {
        this.sendMessage()
      }
    },

    // 输入状态管理（简化版，STOMP会自动处理心跳）
    handleTyping() {
      // 这里可以添加输入状态的处理逻辑
      // 由于当前后端接口文档中没有明确的输入状态处理，暂时简化
      if (!this.isUserTyping) {
        this.isUserTyping = true

        // 清除之前的定时器
        if (this.typingTimer) {
          clearTimeout(this.typingTimer)
        }

        // 3秒后自动停止输入状态
        this.typingTimer = setTimeout(() => {
          this.stopUserTyping()
        }, 3000)
      }
    },

    stopUserTyping() {
      this.isUserTyping = false

      if (this.typingTimer) {
        clearTimeout(this.typingTimer)
        this.typingTimer = null
      }
    },

    // 工具方法
    scrollToBottom() {
      if (this.$refs.messagesContainer) {
        this.$refs.messagesContainer.scrollTop = this.$refs.messagesContainer.scrollHeight
      }
    },

    formatTime(time) {
      return time.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit'
      })
    },

    // 文件选择相关方法
    selectImage() {
      this.$refs.fileInput.accept = 'image/*'
      this.$refs.fileInput.click()
    },

    selectFile() {
      this.$refs.fileInput.accept = '.pdf,.doc,.docx,.txt'
      this.$refs.fileInput.click()
    },

    handleFileSelect(event) {
      const file = event.target.files[0]
      if (file) {
        // 显示文件消息
        this.addMessage({
          type: 'user',
          text: `[文件] ${file.name}`,
          time: new Date(),
          messageId: this.generateMessageId()
        })

        this.$nextTick(() => {
          this.scrollToBottom()
        })

        // TODO: 实现文件上传到服务器的逻辑
        this.$message.info('文件上传功能待实现')
      }

      // 清空文件输入
      event.target.value = ''
    },

    // 拖拽相关方法
    startDrag(event) {
      this.isDragging = true
      const rect = this.$refs.chatWindow.getBoundingClientRect()
      this.dragOffset = {
        x: event.clientX - rect.left,
        y: event.clientY - rect.top
      }
    },

    handleMouseMove(event) {
      if (!this.isDragging) return

      const x = event.clientX - this.dragOffset.x
      const y = event.clientY - this.dragOffset.y

      // 限制拖拽范围
      const maxX = window.innerWidth - 350 // 窗口宽度
      const maxY = window.innerHeight - 500 // 窗口高度

      const constrainedX = Math.max(0, Math.min(x, maxX))
      const constrainedY = Math.max(0, Math.min(y, maxY))

      this.windowPosition = {
        left: constrainedX + 'px',
        top: constrainedY + 'px',
        right: 'auto',
        bottom: 'auto'
      }
    },

    handleMouseUp() {
      this.isDragging = false
    },

    // 资源清理
    cleanup() {
      // 清理ChatService连接
      try {
        if (this.roomCode) {
          ChatService.unsubscribeFromRoom(this.roomCode)
        }
        // 注意：不要断开ChatService连接，因为其他组件可能还在使用
      } catch (error) {
        console.error('❌ 清理聊天服务失败:', error)
      }

      // 清理定时器
      this.stopUserTyping()

      // 清理事件监听
      document.removeEventListener('mousemove', this.handleMouseMove)
      document.removeEventListener('mouseup', this.handleMouseUp)
      window.removeEventListener('beforeunload', this.cleanup)
    },

    selectImage() {
      this.$refs.fileInput.accept = 'image/*'
      this.$refs.fileInput.click()
    },

    selectFile() {
      this.$refs.fileInput.accept = '.pdf,.doc,.docx,.txt'
      this.$refs.fileInput.click()
    },

    handleFileSelect(event) {
      const file = event.target.files[0]
      if (file) {
        this.messages.push({
          type: 'user',
          text: `[文件] ${file.name}`,
          time: new Date()
        })

        this.$nextTick(() => {
          this.scrollToBottom()
        })

        // 模拟客服回复
        setTimeout(() => {
          this.messages.push({
            type: 'service',
            text: '已收到您的文件，我们会尽快查看并回复您。',
            time: new Date()
          })

          this.$nextTick(() => {
            this.scrollToBottom()
          })
        }, 1500)
      }

      // 清空文件输入
      event.target.value = ''
    },

    // 拖拽相关方法
    startDrag(event) {
      this.isDragging = true
      const rect = this.$refs.chatWindow.getBoundingClientRect()
      this.dragOffset = {
        x: event.clientX - rect.left,
        y: event.clientY - rect.top
      }
    },

    handleMouseMove(event) {
      if (!this.isDragging) return

      const x = event.clientX - this.dragOffset.x
      const y = event.clientY - this.dragOffset.y

      // 限制拖拽范围
      const maxX = window.innerWidth - 350 // 窗口宽度
      const maxY = window.innerHeight - 500 // 窗口高度

      const constrainedX = Math.max(0, Math.min(x, maxX))
      const constrainedY = Math.max(0, Math.min(y, maxY))

      this.windowPosition = {
        left: constrainedX + 'px',
        top: constrainedY + 'px',
        right: 'auto',
        bottom: 'auto'
      }
    },

    handleMouseUp() {
      this.isDragging = false
    }
  }
}
</script>

<style scoped>
.customer-service-container {
  position: fixed;
  z-index: 9999;
}

/* 客服图标样式 */
.service-icon {
  position: fixed;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #409EFF, #67C23A);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
  transition: all 0.3s ease;
  animation: pulse 2s infinite;
}

.service-icon:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(64, 158, 255, 0.6);
}

.service-icon i {
  font-size: 24px;
  color: white;
}

.service-badge {
  position: absolute;
  top: -5px;
  right: -5px;
  background: #F56C6C;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
}

@keyframes pulse {
  0% {
    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
  }

  50% {
    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4), 0 0 0 10px rgba(64, 158, 255, 0.1);
  }

  100% {
    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
  }
}

/* 聊天窗口样式 */
.chat-window {
  position: fixed;
  width: 350px;
  height: 500px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 聊天头部样式 */
.chat-header {
  background: linear-gradient(135deg, #409EFF, #67C23A);
  color: white;
  padding: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: move;
  user-select: none;
}

.header-left {
  display: flex;
  align-items: center;
}

.service-avatar {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.service-avatar i {
  font-size: 20px;
  color: white;
}

.service-info {
  display: flex;
  flex-direction: column;
}

.service-name {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 2px;
}

.service-status {
  font-size: 12px;
  opacity: 0.9;
}

.service-status.online {
  color: #67C23A;
}

.service-status.busy {
  color: #E6A23C;
}

.service-status.offline {
  color: #F56C6C;
}

.header-actions {
  display: flex;
  gap: 5px;
}

.header-actions .el-button {
  color: white;
  padding: 5px;
}

.header-actions .el-button:hover {
  background: rgba(255, 255, 255, 0.1);
}

/* 消息区域样式 */
.chat-messages {
  flex: 1;
  padding: 15px;
  overflow-y: auto;
  background: #f8f9fa;
}

.message-item {
  display: flex;
  margin-bottom: 15px;
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.message-item.user {
  flex-direction: row-reverse;
}

.message-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 8px;
  flex-shrink: 0;
}

.message-item.user .message-avatar {
  background: #409EFF;
  color: white;
}

.message-item.service .message-avatar {
  background: #67C23A;
  color: white;
}

.message-content {
  max-width: 70%;
}

.message-item.user .message-content {
  text-align: right;
}

.message-text {
  background: white;
  padding: 10px 12px;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  word-wrap: break-word;
  line-height: 1.4;
}

.message-item.user .message-text {
  background: #409EFF;
  color: white;
}

.message-time {
  font-size: 11px;
  color: #999;
  margin-top: 5px;
  padding: 0 5px;
}

/* 正在输入指示器 */
.typing-indicator {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.typing-dots {
  background: white;
  padding: 10px 15px;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  display: flex;
  gap: 4px;
}

.typing-dots span {
  width: 6px;
  height: 6px;
  background: #999;
  border-radius: 50%;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-dots span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typing {

  0%,
  80%,
  100% {
    transform: scale(0.8);
    opacity: 0.5;
  }

  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 输入区域样式 */
.chat-input {
  border-top: 1px solid #eee;
  background: white;
}

.input-toolbar {
  padding: 8px 15px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  gap: 5px;
}

.input-area {
  padding: 15px;
  display: flex;
  gap: 10px;
  align-items: flex-end;
}

.input-area .el-textarea {
  flex: 1;
}

.send-btn {
  height: 32px;
  align-self: flex-end;
}

/* 滚动条样式 */
.chat-messages::-webkit-scrollbar {
  width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chat-window {
    width: 300px;
    height: 450px;
  }

  .service-icon {
    width: 50px;
    height: 50px;
  }

  .service-icon i {
    font-size: 20px;
  }
}
</style>
