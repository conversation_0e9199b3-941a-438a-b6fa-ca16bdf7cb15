<template>
  <div class="quick-diagnosis">
    <el-dialog
      title="聊天功能快速诊断"
      :visible.sync="visible"
      width="600px"
      :close-on-click-modal="false">
      
      <div class="diagnosis-content">
        <!-- 诊断步骤 -->
        <el-steps :active="currentStep" finish-status="success" align-center>
          <el-step title="检查登录状态"></el-step>
          <el-step title="测试后端连接"></el-step>
          <el-step title="验证聊天API"></el-step>
          <el-step title="诊断完成"></el-step>
        </el-steps>

        <!-- 诊断结果 -->
        <div class="diagnosis-results" v-if="results.length > 0">
          <h4>诊断结果：</h4>
          <el-alert
            v-for="(result, index) in results"
            :key="index"
            :title="result.title"
            :description="result.message"
            :type="result.type"
            :closable="false"
            style="margin-bottom: 10px;">
          </el-alert>
        </div>

        <!-- 解决方案 -->
        <div class="solutions" v-if="solutions.length > 0">
          <h4>建议解决方案：</h4>
          <ol>
            <li v-for="(solution, index) in solutions" :key="index">
              {{ solution }}
            </li>
          </ol>
        </div>

        <!-- 详细信息 -->
        <div class="details" v-if="showDetails && diagnosticData">
          <h4>详细信息：</h4>
          <pre class="diagnostic-data">{{ JSON.stringify(diagnosticData, null, 2) }}</pre>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="visible = false">关闭</el-button>
        <el-button type="primary" @click="runDiagnosis" :loading="diagnosing">
          {{ diagnosing ? '诊断中...' : '开始诊断' }}
        </el-button>
        <el-button type="info" @click="showDetails = !showDetails" v-if="diagnosticData">
          {{ showDetails ? '隐藏详情' : '显示详情' }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import axios from 'axios'

export default {
  name: 'QuickDiagnosis',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      currentStep: 0,
      diagnosing: false,
      results: [],
      solutions: [],
      showDetails: false,
      diagnosticData: null
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.resetDiagnosis()
      }
    }
  },
  methods: {
    resetDiagnosis() {
      this.currentStep = 0
      this.results = []
      this.solutions = []
      this.showDetails = false
      this.diagnosticData = null
    },

    async runDiagnosis() {
      this.diagnosing = true
      this.resetDiagnosis()

      try {
        // 步骤1: 检查登录状态
        this.currentStep = 1
        await this.checkLoginStatus()

        // 步骤2: 测试后端连接
        this.currentStep = 2
        await this.testBackendConnection()

        // 步骤3: 验证聊天API
        this.currentStep = 3
        await this.testChatAPI()

        // 步骤4: 完成
        this.currentStep = 4
        
        if (this.results.every(r => r.type === 'success')) {
          this.results.push({
            title: '诊断完成',
            message: '所有测试通过！聊天功能应该可以正常使用。',
            type: 'success'
          })
        }

      } catch (error) {
        console.error('诊断过程出错:', error)
        this.results.push({
          title: '诊断异常',
          message: '诊断过程中发生错误: ' + error.message,
          type: 'error'
        })
      } finally {
        this.diagnosing = false
      }
    },

    async checkLoginStatus() {
      const token = localStorage.getItem('token')
      const userInfo = localStorage.getItem('userInfo')

      if (!token) {
        this.results.push({
          title: '登录状态检查',
          message: '未找到登录Token，请先登录系统',
          type: 'error'
        })
        this.solutions.push('请重新登录系统')
        throw new Error('未登录')
      }

      if (!userInfo) {
        this.results.push({
          title: '用户信息检查',
          message: '未找到用户信息，可能需要重新登录',
          type: 'warning'
        })
        this.solutions.push('请刷新页面或重新登录')
      } else {
        this.results.push({
          title: '登录状态检查',
          message: '用户已登录，Token和用户信息正常',
          type: 'success'
        })
      }
    },

    async testBackendConnection() {
      try {
        console.log('测试后端连接...')
        
        // 测试基础连接
        const response = await axios.get('/api/test', { 
          timeout: 5000,
          headers: {
            'Authorization': localStorage.getItem('token')
          }
        })

        this.results.push({
          title: '后端连接测试',
          message: '后端服务连接正常',
          type: 'success'
        })

        this.diagnosticData = {
          ...this.diagnosticData,
          backendConnection: {
            status: 'success',
            response: response.data
          }
        }

      } catch (error) {
        console.error('后端连接测试失败:', error)
        
        let message = '后端服务连接失败'
        if (error.code === 'ECONNREFUSED') {
          message = '无法连接到后端服务，请确认服务已启动'
          this.solutions.push('启动后端Spring Boot应用 (通常在 http://localhost:8080)')
        } else if (error.response?.status === 404) {
          message = 'API接口不存在，请检查后端配置'
          this.solutions.push('检查后端API接口配置')
        } else {
          message = `连接错误: ${error.message}`
          this.solutions.push('检查网络连接和后端服务状态')
        }

        this.results.push({
          title: '后端连接测试',
          message: message,
          type: 'error'
        })

        this.diagnosticData = {
          ...this.diagnosticData,
          backendConnection: {
            status: 'failed',
            error: error.message,
            code: error.code
          }
        }

        throw error
      }
    },

    async testChatAPI() {
      try {
        console.log('测试聊天API...')
        
        const token = localStorage.getItem('token')
        const response = await axios.post('/api/chat/quick-start', {}, {
          headers: { 'Authorization': token },
          timeout: 10000
        })

        if (response.data.code === 200) {
          this.results.push({
            title: '聊天API测试',
            message: '聊天API调用成功，聊天功能正常',
            type: 'success'
          })

          this.diagnosticData = {
            ...this.diagnosticData,
            chatAPI: {
              status: 'success',
              roomCode: response.data.data?.roomCode,
              response: response.data
            }
          }
        } else {
          throw new Error(response.data.message || '聊天API返回错误')
        }

      } catch (error) {
        console.error('聊天API测试失败:', error)
        
        let message = '聊天API调用失败'
        if (error.response?.status === 401) {
          message = 'Token认证失败，请重新登录'
          this.solutions.push('重新登录系统获取新的Token')
        } else if (error.response?.status === 403) {
          message = '权限不足，无法访问聊天功能'
          this.solutions.push('检查用户权限设置')
        } else if (error.response?.status === 500) {
          message = '服务器内部错误，请联系管理员'
          this.solutions.push('查看后端服务日志，联系技术支持')
        } else {
          message = `API调用错误: ${error.message}`
          this.solutions.push('检查API接口配置和参数')
        }

        this.results.push({
          title: '聊天API测试',
          message: message,
          type: 'error'
        })

        this.diagnosticData = {
          ...this.diagnosticData,
          chatAPI: {
            status: 'failed',
            error: error.message,
            response: error.response?.data
          }
        }

        throw error
      }
    }
  }
}
</script>

<style scoped>
.quick-diagnosis {
  /* 样式由父组件控制 */
}

.diagnosis-content {
  padding: 20px 0;
}

.diagnosis-results {
  margin: 20px 0;
}

.solutions {
  margin: 20px 0;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.solutions ol {
  margin: 10px 0;
  padding-left: 20px;
}

.details {
  margin: 20px 0;
}

.diagnostic-data {
  background-color: #2d3748;
  color: #e2e8f0;
  padding: 15px;
  border-radius: 4px;
  font-size: 12px;
  max-height: 300px;
  overflow-y: auto;
}

.el-steps {
  margin-bottom: 30px;
}

h4 {
  color: #409eff;
  margin-bottom: 10px;
}
</style>
