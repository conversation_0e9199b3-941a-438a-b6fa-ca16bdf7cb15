<template>
  <div class="chat-integration-test">
    <el-card class="test-card">
      <div slot="header" class="clearfix">
        <span>聊天系统集成测试</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="refreshStatus">刷新状态</el-button>
      </div>
      
      <!-- 连接状态 -->
      <div class="status-section">
        <h3>连接状态</h3>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-card shadow="hover">
              <div class="status-item">
                <i :class="connectionStatus.isConnected ? 'el-icon-success' : 'el-icon-error'" 
                   :style="{ color: connectionStatus.isConnected ? '#67C23A' : '#F56C6C' }"></i>
                <span>WebSocket连接</span>
                <div class="status-value">{{ connectionStatus.isConnected ? '已连接' : '未连接' }}</div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="8">
            <el-card shadow="hover">
              <div class="status-item">
                <i class="el-icon-message"></i>
                <span>订阅数量</span>
                <div class="status-value">{{ connectionStatus.subscriptionsCount }}</div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="8">
            <el-card shadow="hover">
              <div class="status-item">
                <i class="el-icon-user"></i>
                <span>用户类型</span>
                <div class="status-value">{{ userType }}</div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 功能测试 -->
      <div class="test-section">
        <h3>功能测试</h3>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-card shadow="hover">
              <div slot="header">用户端测试</div>
              <div class="test-buttons">
                <el-button type="warning" @click="runConnectionTest" :loading="testing.connection">
                  🔍 连接诊断
                </el-button>
                <el-button type="danger" @click="runAutoFix" :loading="testing.autoFix">
                  🔧 一键修复
                </el-button>
                <el-button type="primary" @click="testQuickStart" :loading="testing.quickStart">
                  测试快速开始聊天
                </el-button>
                <el-button type="success" @click="testSendMessage" :loading="testing.sendMessage">
                  测试发送消息
                </el-button>
                <el-button type="info" @click="openUserChat">
                  打开用户聊天窗口
                </el-button>
              </div>
            </el-card>
          </el-col>
          <el-col :span="12">
            <el-card shadow="hover">
              <div slot="header">客服端测试</div>
              <div class="test-buttons">
                <el-button type="warning" @click="testGetPendingRooms" :loading="testing.pendingRooms">
                  获取待处理消息
                </el-button>
                <el-button type="danger" @click="testTakeOverRoom" :loading="testing.takeOver">
                  测试接管聊天室
                </el-button>
                <el-button type="info" @click="openServiceWorkbench">
                  打开客服工作台
                </el-button>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 测试结果 -->
      <div class="results-section" v-if="testResults.length > 0">
        <h3>测试结果</h3>
        <el-timeline>
          <el-timeline-item
            v-for="(result, index) in testResults"
            :key="index"
            :type="result.type"
            :timestamp="result.timestamp">
            <div class="result-content">
              <strong>{{ result.title }}</strong>
              <p>{{ result.message }}</p>
              <pre v-if="result.data" class="result-data">{{ JSON.stringify(result.data, null, 2) }}</pre>
            </div>
          </el-timeline-item>
        </el-timeline>
      </div>
    </el-card>

    <!-- 用户聊天组件 -->
    <CustomerService ref="customerService" />
  </div>
</template>

<script>
import ChatService from '@/utils/chatService'
import ConnectionTest from '@/utils/connectionTest'
import SystemDiagnosis from '@/utils/systemDiagnosis'
import ChatFixHelper from '@/utils/chatFixHelper'
import CustomerService from './CustomerService.vue'

export default {
  name: 'ChatIntegrationTest',
  components: {
    CustomerService
  },
  data() {
    return {
      connectionStatus: {
        isConnected: false,
        stompConnected: false,
        subscriptionsCount: 0
      },
      userType: '普通用户',
      testing: {
        connection: false,
        autoFix: false,
        quickStart: false,
        sendMessage: false,
        pendingRooms: false,
        takeOver: false
      },
      testResults: [],
      currentRoomCode: null
    }
  },
  
  mounted() {
    this.refreshStatus()
    this.checkUserType()
  },
  
  methods: {
    // 刷新连接状态
    refreshStatus() {
      this.connectionStatus = ChatService.getConnectionStatus()
    },

    // 运行连接测试
    async runConnectionTest() {
      this.testing.connection = true
      try {
        const report = await SystemDiagnosis.runFullDiagnosis()

        this.addTestResult(
          '系统诊断',
          `诊断完成: ${report.summary.success}/${report.summary.total} 通过`,
          report.status === 'success' ? 'success' : 'warning',
          report
        )

        // 显示详细建议
        if (report.recommendations.length > 0) {
          this.$message({
            type: 'warning',
            message: '发现问题: ' + report.recommendations.join('; '),
            duration: 8000
          })
        } else {
          this.$message.success('所有系统诊断通过！')
        }

      } catch (error) {
        this.addTestResult(
          '系统诊断',
          `诊断失败: ${error.message}`,
          'danger',
          { error: error.message }
        )
      } finally {
        this.testing.connection = false
      }
    },

    // 运行一键修复
    async runAutoFix() {
      this.testing.autoFix = true
      try {
        const report = await ChatFixHelper.autoFix()

        this.addTestResult(
          '一键修复',
          `修复完成: ${report.summary.success}/${report.summary.total} 成功`,
          report.status === 'success' ? 'success' : (report.status === 'warning' ? 'warning' : 'danger'),
          report
        )

        // 显示修复结果
        if (report.status === 'success') {
          this.$message.success('修复完成！请重新测试聊天功能')
        } else if (report.nextSteps.length > 0) {
          this.$message({
            type: 'warning',
            message: '部分修复完成，下一步: ' + report.nextSteps.join('; '),
            duration: 10000
          })
        } else {
          this.$message.error('修复失败，请查看详细信息')
        }

      } catch (error) {
        this.addTestResult(
          '一键修复',
          `修复失败: ${error.message}`,
          'danger',
          { error: error.message }
        )
      } finally {
        this.testing.autoFix = false
      }
    },
    
    // 检查用户类型
    checkUserType() {
      const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')
      this.userType = userInfo.isCustomerService ? '客服用户' : '普通用户'
    },
    
    // 添加测试结果
    addTestResult(title, message, type = 'primary', data = null) {
      this.testResults.unshift({
        title,
        message,
        type,
        data,
        timestamp: new Date().toLocaleTimeString()
      })
      
      // 限制结果数量
      if (this.testResults.length > 10) {
        this.testResults = this.testResults.slice(0, 10)
      }
    },
    
    // 测试快速开始聊天
    async testQuickStart() {
      this.testing.quickStart = true
      try {
        const result = await ChatService.quickStartChat()
        this.currentRoomCode = result.roomCode
        this.addTestResult(
          '快速开始聊天',
          `成功创建聊天室: ${result.roomCode}`,
          'success',
          result
        )
        this.refreshStatus()
      } catch (error) {
        this.addTestResult(
          '快速开始聊天',
          `失败: ${error.message}`,
          'danger',
          { error: error.message }
        )
      } finally {
        this.testing.quickStart = false
      }
    },
    
    // 测试发送消息
    async testSendMessage() {
      if (!this.currentRoomCode) {
        this.$message.warning('请先测试快速开始聊天')
        return
      }
      
      this.testing.sendMessage = true
      try {
        await ChatService.sendMessage(this.currentRoomCode, '这是一条测试消息')
        this.addTestResult(
          '发送消息',
          '消息发送成功',
          'success'
        )
      } catch (error) {
        this.addTestResult(
          '发送消息',
          `失败: ${error.message}`,
          'danger',
          { error: error.message }
        )
      } finally {
        this.testing.sendMessage = false
      }
    },
    
    // 测试获取待处理消息
    async testGetPendingRooms() {
      this.testing.pendingRooms = true
      try {
        const result = await ChatService.getPendingRooms()
        this.addTestResult(
          '获取待处理消息',
          `成功获取 ${result.length} 条待处理消息`,
          'success',
          result
        )
      } catch (error) {
        this.addTestResult(
          '获取待处理消息',
          `失败: ${error.message}`,
          'danger',
          { error: error.message }
        )
      } finally {
        this.testing.pendingRooms = false
      }
    },
    
    // 测试接管聊天室
    async testTakeOverRoom() {
      if (!this.currentRoomCode) {
        this.$message.warning('请先测试快速开始聊天')
        return
      }
      
      this.testing.takeOver = true
      try {
        const result = await ChatService.takeOverRoom(this.currentRoomCode)
        this.addTestResult(
          '接管聊天室',
          `成功接管聊天室: ${this.currentRoomCode}`,
          'success',
          result
        )
      } catch (error) {
        this.addTestResult(
          '接管聊天室',
          `失败: ${error.message}`,
          'danger',
          { error: error.message }
        )
      } finally {
        this.testing.takeOver = false
      }
    },
    
    // 打开用户聊天窗口
    openUserChat() {
      this.$refs.customerService?.toggleChat()
    },
    
    // 打开客服工作台
    openServiceWorkbench() {
      this.$router.push('/customer-service-workbench')
    }
  }
}
</script>

<style scoped>
.chat-integration-test {
  padding: 20px;
}

.test-card {
  max-width: 1200px;
  margin: 0 auto;
}

.status-section,
.test-section,
.results-section {
  margin-bottom: 30px;
}

.status-item {
  text-align: center;
  padding: 20px;
}

.status-item i {
  font-size: 24px;
  margin-bottom: 10px;
  display: block;
}

.status-value {
  font-size: 18px;
  font-weight: bold;
  margin-top: 5px;
}

.test-buttons {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.test-buttons .el-button {
  width: 100%;
}

.result-content {
  background: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
}

.result-data {
  background: #2d3748;
  color: #e2e8f0;
  padding: 10px;
  border-radius: 4px;
  font-size: 12px;
  margin-top: 10px;
  overflow-x: auto;
}

h3 {
  color: #409eff;
  margin-bottom: 20px;
}
</style>
