/* eslint-disable */
<template>
  <div class="index-container">
    <header class="header">
      <h1>系统首页</h1>
      <div class="user-info">
        <span>欢迎, {{ username }}</span>
        <button @click="logout" class="logout-btn">退出登录</button>
      </div>
    </header>

    <main class="main-content">
      <div class="welcome-card">
        <h2>欢迎使用本系统</h2>
        <p>您已成功登录系统</p>
        <p class="login-time">登录时间: {{ loginTime }}</p>

        <!-- 聊天功能入口 -->
        <div class="chat-actions" style="margin-top: 20px;">
          <!-- 简化的调试信息 -->
          <div style="margin-bottom: 10px; font-size: 12px; color: #666;">
            用户类型: {{ userType ? (userType.isCustomerService ? '客服' : '普通用户') : '加载中...' }}
            | 用户ID: {{ userType && userType.userInfo ? userType.userInfo.id : '未知' }}
          </div>

          <div v-if="userType" style="display: flex; gap: 10px; flex-wrap: wrap;">
            <el-button
              v-if="!userType.isCustomerService"
              type="primary"
              icon="el-icon-service"
              @click="openUserChat"
              size="medium">
              在线客服
            </el-button>

            <el-button
              v-if="userType.isCustomerService"
              type="success"
              icon="el-icon-monitor"
              @click="openServiceWorkbench"
              size="medium">
              客服工作台
            </el-button>

            <el-button
              type="info"
              icon="el-icon-setting"
              @click="openChatTest"
              size="medium">
              聊天测试
            </el-button>
          </div>

          <div v-else style="color: red;">
            用户类型信息加载中或加载失败...
          </div>
        </div>
      </div>

      <div class="employee-table-container" ref="employeeTableContainer">
        <div
          style="
            display: flex;
            justify-content: space-between;
            align-items: center;
          "
        >
          <h2>员工信息列表</h2>
          <div style="margin-bottom: 10px;">
            <el-button @click="startExport">全量导出</el-button>
            <!-- <el-button @click="testNetworkRequest" type="primary" style="margin-left: 10px;">测试网络请求</el-button> -->
            <!-- <el-button @click="toggleNetworkMonitor" type="warning" style="margin-left: 10px;">{{ monitorActive ? '停止' : '启动' }}网络监控</el-button> -->
          </div>

          <el-dialog
            :visible.sync="progressVisible"
            title="导出进度"
            width="30%"
          >
            <el-progress
              :percentage="progressPercent"
              :status="progressStatus || undefined"
            />
            <span v-if="progressPercent === 100"
              >文件生成完成，即将开始下载...</span
            >
            <span v-else>已导出 {{ progressPercent }}%</span>
          </el-dialog>
        </div>
        <div v-if="loading" class="loading-indicator">加载中...</div>

        <!-- 错误状态显示 -->
        <div v-if="!loading && hasError" class="error-state">
          <el-alert
            :title="errorTitle"
            :description="errorDescription"
            :type="errorType"
            :closable="false"
            show-icon
            class="error-alert"
          >
            <template slot="default">
              <div class="error-actions">
                <el-button
                  v-if="errorType === 'warning' && errorCode === 403"
                  type="primary"
                  size="small"
                  @click="contactAdmin"
                >
                  联系管理员
                </el-button>
                <el-button
                  v-if="errorType === 'error'"
                  type="primary"
                  size="small"
                  @click="retryRequest"
                >
                  重试
                </el-button>
              </div>
            </template>
          </el-alert>
        </div>

        <table
          v-if="!loading && !hasError && employeeList.length > 0"
          class="employee-table"
        >
          <thead>
            <tr>
              <th>ID</th>
              <th>姓名</th>
              <th>年龄</th>
              <th>性别</th>
              <th>创建时间</th>
              <!-- <th>更新时间</th> -->
            </tr>
          </thead>
          <tbody>
            <tr v-for="employee in employeeList" :key="employee.id">
              <td>{{ employee.id }}</td>
              <td>{{ employee.name }}</td>
              <td>{{ employee.age }}</td>
              <td>{{ employee.genderText }}</td>
              <td>{{ new Date(employee.createTime).toLocaleString() }}</td>
              <!-- <td>{{ new Date(employee.updateTime).toLocaleString() }}</td> -->
            </tr>
          </tbody>
        </table>
        <div v-if="!loading && !hasError && employeeList.length === 0" class="no-data">
          暂无员工数据
        </div>
        <div v-if="!loading && totalRecords > 0" class="pagination-container">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handlePageChange"
            :current-page="currentPage"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="totalRecords"
          >
          </el-pagination>
        </div>
      </div>
    </main>

    <!-- 用户聊天组件 -->
    <UserChat ref="userChat" />
  </div>
</template>

<script>
/* eslint-disable */
import axios from "axios"; // 保留原有的axios导入
import request from "@/utils/request"; // 导入新的请求工具
import userPermissionService from "@/utils/userPermission"; // 导入用户权限服务
import UserChat from "./UserChat.vue"; // 导入用户聊天组件

export default {
  components: {
    UserChat
  },
  data() {
    return {
      username: "用户",
      userInfo: null, // 完整的用户信息
      userType: null, // 用户类型信息
      loginTime: new Date().toLocaleString(),
      employeeList: [],
      currentPage: 1,
      pageSize: 10,
      totalRecords: 0,
      loading: false,
      progressVisible: false,
      progressPercent: 0,
      progressStatus: null,
      taskId: null,
      pollTimer: null,
      monitorActive: false, // 网络监控状态
      // 错误状态管理
      hasError: false,
      errorTitle: '',
      errorDescription: '',
      errorType: 'error', // error, warning, info
      errorCode: null,
    };
  },
  async created() {
    // 检查是否有token，如果没有则跳转到登录页
    const token = localStorage.getItem('token')
    if (!token) {
      this.$router.push('/login')
      return
    }

    // 优先从缓存获取用户信息，避免重复请求
    await this.initUserInfo()

    // 获取用户类型信息
    await this.initUserType()

    // 获取员工列表
    this.fetchEmployeeList()
  },
  methods: {
    // 初始化用户信息（优先使用缓存，避免重复请求）
    async initUserInfo() {
      try {
        // 先从localStorage获取缓存的用户信息
        const cachedUserInfo = localStorage.getItem('userInfo')
        if (cachedUserInfo) {
          this.userInfo = JSON.parse(cachedUserInfo)
          this.username = this.userInfo.userName || this.userInfo.nickName || '用户'
          console.log('✅ 使用缓存的用户信息:', this.userInfo)

          // 检查缓存是否是最近的（比如5分钟内）
          const cacheTime = localStorage.getItem('userInfoCacheTime')
          const now = Date.now()
          const fiveMinutes = 5 * 60 * 1000

          if (cacheTime && (now - parseInt(cacheTime)) < fiveMinutes) {
            console.log('✅ 用户信息缓存仍然有效，跳过API调用')
            return // 缓存有效，直接返回
          }
        }

        // 如果没有缓存或缓存过期，则调用API
        console.log('🔄 缓存过期或不存在，重新获取用户信息...')
        await this.getUserInfo()

      } catch (error) {
        console.error('❌ 初始化用户信息错误:', error)
        // 如果初始化失败，尝试调用API
        await this.getUserInfo()
      }
    },

    async getUserInfo() {
      try {
        console.log('👤 获取用户信息...')

        // 先尝试从localStorage获取缓存的用户信息
        const cachedUserInfo = localStorage.getItem('userInfo')
        if (cachedUserInfo) {
          this.userInfo = JSON.parse(cachedUserInfo)
          this.username = this.userInfo.userName || this.userInfo.nickName || '用户'
          console.log('✅ 从缓存获取用户信息:', this.userInfo)
        }

        // 调用接口获取最新的用户信息
        const response = await request.get('/api/user/getInfo')
        console.log('👤 用户信息响应:', response)

        if (response.data.code === '200') {
          this.userInfo = response.data.data
          this.username = this.userInfo.userName || this.userInfo.nickName || '用户'

          // 更新localStorage中的用户信息和缓存时间
          localStorage.setItem('userInfo', JSON.stringify(this.userInfo))
          localStorage.setItem('username', this.username)
          localStorage.setItem('userInfoCacheTime', Date.now().toString())

          console.log('✅ 用户信息已更新:', this.userInfo)
        } else {
          console.warn('⚠️ 获取用户信息失败:', response.data.message)
          this.handleUserInfoError(response.data.message, null, true)
        }
      } catch (error) {
        console.error('❌ 获取用户信息错误:', error)
        this.handleUserInfoError(null, error, false)
      }
    },

    // 处理用户信息获取错误
    handleUserInfoError(message, error, isBusinessError) {
      if (error && error.response) {
        const status = error.response.status;
        const errorData = error.response.data;

        switch (status) {
          case 401:
            // 权限不足，优雅提示
            this.$message.warning({
              message: '登录状态已过期，即将跳转到登录页',
              duration: 3000,
              onClose: () => {
                this.logout();
              }
            });
            // 延迟跳转，给用户看到提示的时间
            setTimeout(() => {
              this.logout();
            }, 3000);
            break;
          case 403:
            // 权限不足但不需要重新登录
            this.$message.warning({
              message: '权限不足，无法获取完整用户信息',
              duration: 5000
            });
            // 如果有缓存信息，继续使用
            if (!this.userInfo) {
              this.username = '访客用户';
            }
            break;
          case 404:
            this.$message.error('用户信息接口不存在，请联系管理员');
            break;
          case 500:
            this.$message.error('服务器内部错误，请稍后重试');
            break;
          default:
            this.$message.error({
              message: errorData?.message || '获取用户信息失败',
              duration: 5000
            });
        }
      } else if (error && error.code === 'ECONNABORTED') {
        this.$message.warning('获取用户信息超时，使用缓存信息');
      } else if (error && !error.response) {
        this.$message.warning('网络连接失败，使用缓存信息');
      } else if (isBusinessError) {
        // 业务逻辑错误，但有缓存可以继续使用
        if (!this.userInfo) {
          this.$message.warning({
            message: message || '获取用户信息失败，请重新登录',
            duration: 5000,
            onClose: () => {
              this.logout();
            }
          });
        } else {
          this.$message.warning('用户信息更新失败，使用缓存信息');
        }
      }

      // 如果完全没有用户信息，设置默认值
      if (!this.userInfo && !this.username) {
        this.username = '用户';
      }
    },

    // 初始化用户类型信息
    async initUserType() {
      try {
        this.userType = await userPermissionService.getUserType()
        console.log('✅ 用户类型信息:', this.userType)
      } catch (error) {
        console.error('❌ 获取用户类型失败:', error)
        this.userType = {
          userInfo: null,
          isCustomerService: false,
          userType: 'unknown',
          displayName: '用户'
        }
      }
    },

    logout() {
      // 清除登录信息
      localStorage.removeItem("username")
      localStorage.removeItem("token")
      localStorage.removeItem("userInfo")
      localStorage.removeItem("userInfoCacheTime")
      // 跳转到登录页
      this.$router.push("/login")
    },
    // exportData() {
    //   // 导出功能待实现
    //   alert("导出功能暂未实现");
    // },
    async startExport() {
      console.log("🚀 开始导出功能...");
      try {
        // 1. 创建导出任务
        console.log("📤 正在调用创建导出接口: /api/export/create");

        // 使用新的请求工具，确保在开发工具中可见
        const createRes = await request.post('/api/export/create');

        console.log("📤 创建导出接口响应:", createRes);

        // 检查响应格式，按照系统标准格式处理
        if (createRes.data && createRes.data.code === "200") {
          this.taskId = createRes.data.data.taskId || createRes.data.data;
          console.log("✅ 获取到任务ID:", this.taskId);

          // 2. 显示进度条
          this.progressVisible = true;
          this.progressPercent = 0;
          this.progressStatus = null;

          // 3. 开始轮询进度
          this.pollProgress();
        } else {
          // 处理业务逻辑错误
          this.handleExportError(createRes.data?.message, null, true);
        }
      } catch (error) {
        console.error("❌ 创建导出任务错误:", error);
        this.handleExportError(null, error, false);
      }
    },

    // 处理导出相关错误
    handleExportError(message, error, isBusinessError) {
      if (error && error.response) {
        const status = error.response.status;
        const errorData = error.response.data;

        switch (status) {
          case 401:
            this.$message.warning({
              message: '登录已过期，请重新登录后再试',
              duration: 3000,
              onClose: () => {
                this.logout();
              }
            });
            break;
          case 403:
            this.$message.warning({
              message: '权限不足，您没有导出数据的权限，请联系管理员申请权限',
              duration: 6000
            });
            break;
          case 404:
            this.$message.error('导出接口不存在，请联系技术支持');
            break;
          case 429:
            this.$message.warning('导出请求过于频繁，请稍后再试');
            break;
          case 500:
            this.$message.error('服务器内部错误，导出失败，请稍后重试');
            break;
          default:
            this.$message.error({
              message: errorData?.message || message || '导出失败，请稍后重试',
              duration: 5000
            });
        }
      } else if (error && error.code === 'ECONNABORTED') {
        this.$message.error('导出请求超时，请检查网络连接');
      } else if (error && !error.response) {
        this.$message.error('网络连接失败，无法创建导出任务');
      } else if (isBusinessError) {
        // 根据具体的业务错误消息进行处理
        if (message && message.includes('权限')) {
          this.$message.warning({
            message: '权限不足，您没有导出数据的权限，请联系管理员申请权限',
            duration: 6000
          });
        } else if (message && message.includes('频繁')) {
          this.$message.warning('导出请求过于频繁，请稍后再试');
        } else {
          this.$message.error({
            message: message || '创建导出任务失败，请稍后重试',
            duration: 5000
          });
        }
      }
    },

    async pollProgress() {
      // 停止已有轮询
      if (this.pollTimer) clearTimeout(this.pollTimer);

      try {
        // 查询进度
        console.log("🔄 正在查询导出进度: /api/export/progress/" + this.taskId);

        // 使用新的请求工具，确保在开发工具中可见
        const progressRes = await request.get(`/api/export/progress/${this.taskId}`);

        console.log("🔄 进度查询接口响应:", progressRes);

        // 检查响应格式，按照系统标准格式处理
        if (progressRes.data && progressRes.data.code === "200") {
          const progressData = progressRes.data.data;
          const progress = progressData.progress || 0;
          const status = progressData.status || "PROCESSING";

          // 更新进度
          this.progressPercent = progress;
          console.log(`📊 当前进度: ${progress}%, 状态: ${status}`);

          // 状态处理
          if (status === "FAILED") {
            this.progressStatus = "exception";
            const failReason = progressData.failReason || progressData.message || "导出任务执行失败";
            this.$message.error({
              message: `导出失败：${failReason}`,
              duration: 6000
            });
            // 3秒后关闭进度弹窗
            setTimeout(() => {
              this.progressVisible = false;
            }, 3000);
            return;
          }

          if (status === "COMPLETED" && progress === 100) {
            this.progressStatus = "success";
            // 触发文件下载
            this.downloadFile();
            // 2秒后关闭弹窗
            setTimeout(() => {
              this.progressVisible = false;
            }, 2000);
          } else {
            // 继续轮询
            this.pollTimer = setTimeout(this.pollProgress, 1000);
          }
        } else {
          // 处理业务逻辑错误
          this.progressStatus = "exception";
          this.handleProgressError(progressRes.data?.message, null, true);
        }
      } catch (error) {
        console.error("❌ 获取进度错误:", error);
        this.progressStatus = "exception";
        this.handleProgressError(null, error, false);
      }
    },

    // 处理进度查询错误
    handleProgressError(message, error, isBusinessError) {
      if (error && error.response) {
        const status = error.response.status;
        const errorData = error.response.data;

        switch (status) {
          case 401:
            this.$message.warning('登录已过期，进度查询失败');
            setTimeout(() => {
              this.progressVisible = false;
              this.logout();
            }, 2000);
            break;
          case 403:
            this.$message.warning('权限不足，无法查询导出进度');
            setTimeout(() => {
              this.progressVisible = false;
            }, 3000);
            break;
          case 404:
            this.$message.error('导出任务不存在或已过期');
            setTimeout(() => {
              this.progressVisible = false;
            }, 3000);
            break;
          default:
            this.$message.error({
              message: errorData?.message || '获取导出进度失败',
              duration: 5000
            });
        }
      } else if (isBusinessError) {
        this.$message.error({
          message: message || '获取导出进度失败',
          duration: 5000
        });
      } else {
        this.$message.error('网络错误，无法获取导出进度');
      }

      // 3秒后关闭进度弹窗
      setTimeout(() => {
        this.progressVisible = false;
      }, 3000);
    },

    async downloadFile() {
      try {
        console.log("📥 正在调用下载接口: /api/export/download/" + this.taskId);

        // 使用新的请求工具发起下载请求，这样会在开发工具中显示
        const response = await request.get(`/api/export/download/${this.taskId}`, {
          responseType: 'blob', // 重要：指定响应类型为blob
          headers: {
            'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
          }
        });

        console.log("📥 下载接口响应:", response);

        // 创建blob URL并触发下载
        const blob = new Blob([response.data], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        });

        const url = window.URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;

        // 从响应头中获取文件名，如果没有则使用默认名称
        const contentDisposition = response.headers['content-disposition'];
        let filename = '员工信息导出.xlsx';
        if (contentDisposition) {
          const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
          if (filenameMatch && filenameMatch[1]) {
            filename = filenameMatch[1].replace(/['"]/g, '');
          }
        }

        link.download = filename;
        link.style.display = "none";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // 清理blob URL
        window.URL.revokeObjectURL(url);

        console.log("✅ 文件下载完成:", filename);
        this.$message.success("文件下载完成");

      } catch (error) {
        console.error("❌ 下载文件错误:", error);
        this.$message.error("下载文件失败，请重试");
      }
    },


    beforeDestroy() {
      // 组件销毁时清除定时器
      if (this.pollTimer) clearTimeout(this.pollTimer);
    },
    async fetchEmployeeList() {
      this.loading = true;
      this.clearError(); // 清除之前的错误状态

      try {
        const response = await axios.post("/api/employee/pageList", {
          page: {
            currentPage: this.currentPage,
            pageSize: this.pageSize,
          },
        });

        if (response.data && response.data.code === "200") {
          // 成功获取数据
          this.employeeList = response.data.data.records.map((emp) => {
            return {
              ...emp,
              genderText:
                emp.gender === 0 ? "女" : emp.gender === 1 ? "男" : "未知",
            };
          });
          this.totalRecords = response.data.data.page.rowCount;
        } else {
          // 业务逻辑错误
          console.error("获取员工列表失败:", response.data.message);
          this.handleEmployeeListError(response.data.message || '获取员工列表失败');
        }
      } catch (error) {
        console.error("请求员工列表接口错误:", error);
        this.handleEmployeeListError(null, error);
      } finally {
        this.loading = false;
      }

      this.$nextTick(() => {
        if (this.$refs.employeeTableContainer) {
          this.$refs.employeeTableContainer.scrollIntoView({
            behavior: "auto",
          });
        }
      });
    },

    // 处理员工列表错误
    handleEmployeeListError(message, error) {
      this.employeeList = [];
      this.totalRecords = 0;
      this.hasError = true;

      if (error && error.response) {
        const status = error.response.status;
        const errorData = error.response.data;
        this.errorCode = status;

        switch (status) {
          case 401:
            this.errorType = 'warning';
            this.errorTitle = '登录已过期';
            this.errorDescription = '您的登录状态已过期，请重新登录以继续访问员工列表';
            // 延迟跳转，让用户看到错误信息
            setTimeout(() => {
              this.logout();
            }, 3000);
            break;
          case 403:
            this.errorType = 'warning';
            this.errorTitle = '权限不足';
            this.errorDescription = '您当前没有访问员工列表的权限，请联系管理员申请相应权限';
            break;
          case 404:
            this.errorType = 'error';
            this.errorTitle = '接口不存在';
            this.errorDescription = '员工列表接口不存在，请联系技术支持';
            break;
          case 500:
            this.errorType = 'error';
            this.errorTitle = '服务器错误';
            this.errorDescription = '服务器内部错误，请稍后重试或联系技术支持';
            break;
          default:
            this.errorType = 'error';
            this.errorTitle = '获取数据失败';
            this.errorDescription = errorData?.message || message || '获取员工列表失败，请稍后重试';
        }
      } else if (error && error.code === 'ECONNABORTED') {
        this.errorType = 'error';
        this.errorTitle = '请求超时';
        this.errorDescription = '网络请求超时，请检查网络连接后重试';
      } else if (error && !error.response) {
        this.errorType = 'error';
        this.errorTitle = '网络连接失败';
        this.errorDescription = '无法连接到服务器，请检查网络设置';
      } else {
        this.errorType = 'error';
        this.errorTitle = '获取数据失败';
        this.errorDescription = message || '获取员工列表失败，请稍后重试';
      }
    },

    // 清除错误状态
    clearError() {
      this.hasError = false;
      this.errorTitle = '';
      this.errorDescription = '';
      this.errorType = 'error';
      this.errorCode = null;
    },

    // 重试请求
    retryRequest() {
      this.clearError();
      this.fetchEmployeeList();
    },

    // 联系管理员
    contactAdmin() {
      this.$message.info('请联系系统管理员申请相应权限');
      // 这里可以添加更多联系管理员的逻辑，比如打开邮件客户端等
    },
    handlePageChange(newPage) {
      this.currentPage = newPage;
      this.employeeList = []; // 清空列表以避免幻影
      this.loading = true; // 在这里也设置loading，确保loading状态正确
      this.fetchEmployeeList();
    },
    handleSizeChange(newSize) {
      this.pageSize = newSize;
      this.currentPage = 1; // 页码大小改变时，通常回到第一页
      this.fetchEmployeeList();
    },

    // 聊天相关方法
    openUserChat() {
      console.log('🎯 打开用户聊天')
      console.log('🔍 UserChat组件引用:', this.$refs.userChat)
      console.log('🔍 用户类型信息:', this.userType)

      // 检查组件是否存在
      if (this.$refs.userChat) {
        console.log('✅ UserChat组件存在，调用startChat方法')
        this.$refs.userChat.startChat()
      } else {
        console.error('❌ UserChat组件不存在')
        this.$message.error('聊天组件加载失败，请刷新页面重试')
      }
    },

    openServiceWorkbench() {
      console.log('🎯 打开客服工作台')
      this.$router.push('/customer-service-workbench')
    },

    openChatTest() {
      console.log('🎯 打开聊天测试')
      this.$router.push('/chat-test')
    },



    // 切换网络监控
    toggleNetworkMonitor() {
      if (this.monitorActive) {
        window.networkMonitor.stopMonitoring();
        this.monitorActive = false;
        this.$message.success('网络监控已停止');
      } else {
        window.networkMonitor.startMonitoring();
        this.monitorActive = true;
        this.$message.success('网络监控已启动 - 现在可以在右上角看到网络请求面板');
      }
    },


  },
};
</script>

<style scoped>
.index-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.user-info {
  display: flex;
  align-items: center;
}

.logout-btn {
  margin-left: 15px;
  padding: 5px 15px;
  background-color: #f44336;
  color: white;
  border: none;
  border-radius: 3px;
  cursor: pointer;
}

.welcome-card {
  background-color: #f5f5f5;
  padding: 20px;
  border-radius: 5px;
  margin-bottom: 30px;
  text-align: center;
}

.login-time {
  color: #666;
  font-size: 14px;
  margin-top: 10px;
}

.feature-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.card {
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 5px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

h1 {
  margin: 0;
  color: #333;
}

.employee-table-container {
  margin-top: 30px;
}

.employee-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 20px;
}

.employee-table th,
.employee-table td {
  border: 1px solid #ddd;
  padding: 8px;
  text-align: left;
}

.employee-table th {
  background-color: #f2f2f2;
  color: #333;
}

.loading-indicator,
.no-data {
  text-align: center;
  padding: 20px;
  color: #666;
}

.pagination-container {
  text-align: center;
  margin-top: 20px;
}

.pagination-container button {
  margin: 0 5px;
  padding: 5px 10px;
  border: 1px solid #ddd;
  background-color: #f9f9f9;
  cursor: pointer;
}

.pagination-container button:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

h2,
h3 {
  color: #2196f3;
}
.export-btn {
  padding: 8px 15px;
  background-color: #4caf50;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.export-btn:hover {
  background-color: #45a049;
}

.logout-btn {
  margin-left: 15px;
  padding: 5px 15px;
  background-color: #f44336;
  color: white;
  border: none;
  border-radius: 3px;
  cursor: pointer;
}

/* 错误状态样式 */
.error-state {
  margin: 20px 0;
}

.error-alert {
  margin-bottom: 20px;
}

.error-actions {
  margin-top: 10px;
  text-align: right;
}

.error-actions .el-button {
  margin-left: 10px;
}
</style>
