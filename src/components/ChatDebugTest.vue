<template>
  <div class="chat-debug-test">
    <h2>聊天功能调试测试</h2>
    
    <div class="test-section">
      <h3>当前状态</h3>
      <div class="status-info">
        <p><strong>roomCode:</strong> {{ roomCode || '未设置' }}</p>
        <p><strong>isConnected:</strong> {{ isConnected }}</p>
        <p><strong>stompClient:</strong> {{ !!stompClient }}</p>
        <p><strong>serviceStatus:</strong> {{ serviceStatus }}</p>
      </div>
    </div>

    <div class="test-section">
      <h3>测试操作</h3>
      <div class="test-buttons">
        <button @click="testQuickStart" :disabled="loading">
          {{ loading ? '测试中...' : '测试快速开始聊天' }}
        </button>
        <button @click="testSendMessage" :disabled="!roomCode">
          测试发送消息
        </button>
        <button @click="resetState">重置状态</button>
      </div>
    </div>

    <div class="test-section">
      <h3>测试日志</h3>
      <div class="test-logs" ref="testLogs">
        <div v-for="(log, index) in testLogs" :key="index" :class="['log-item', log.type]">
          <span class="log-time">{{ formatTime(log.time) }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios'
import SockJS from 'sockjs-client'
import Stomp from 'stompjs'

export default {
  name: 'ChatDebugTest',
  data() {
    return {
      roomCode: null,
      isConnected: false,
      stompClient: null,
      serviceStatus: 'offline',
      loading: false,
      testLogs: []
    }
  },
  methods: {
    addLog(type, message) {
      this.testLogs.push({
        type,
        message,
        time: new Date()
      })
      this.$nextTick(() => {
        const logsContainer = this.$refs.testLogs
        if (logsContainer) {
          logsContainer.scrollTop = logsContainer.scrollHeight
        }
      })
    },

    formatTime(time) {
      return time.toLocaleTimeString()
    },

    async testQuickStart() {
      this.loading = true
      this.addLog('info', '开始测试快速开始聊天...')

      try {
        const token = localStorage.getItem('token')
        if (!token) {
          this.addLog('error', '未找到用户token')
          return
        }

        this.addLog('info', '发送快速开始聊天请求...')
        const response = await axios.post('/api/chat/quick-start', {}, {
          headers: { 'Authorization': token },
          timeout: 5000
        })

        if (response.data.code === 200) {
          const data = response.data.data
          this.roomCode = data.roomCode
          this.addLog('success', `快速开始聊天成功，roomCode: ${data.roomCode}`)
          
          // 测试WebSocket连接
          await this.testWebSocketConnection()
        } else {
          this.addLog('error', `快速开始聊天失败: ${response.data.message}`)
        }
      } catch (error) {
        this.addLog('error', `快速开始聊天异常: ${error.message}`)
        // 使用演示模式
        this.roomCode = 'DEMO_' + Date.now()
        this.isConnected = true
        this.serviceStatus = 'online'
        this.addLog('warning', `使用演示模式，roomCode: ${this.roomCode}`)
      } finally {
        this.loading = false
      }
    },

    async testWebSocketConnection() {
      try {
        this.addLog('info', '开始测试WebSocket连接...')
        const token = localStorage.getItem('token')
        const socket = new SockJS('/ws/chat')
        this.stompClient = Stomp.over(socket)

        this.stompClient.debug = (str) => {
          this.addLog('debug', `STOMP: ${str}`)
        }

        await new Promise((resolve, reject) => {
          this.stompClient.connect(
            { 'Authorization': token },
            (frame) => {
              this.addLog('success', 'WebSocket连接成功')
              this.isConnected = true
              this.serviceStatus = 'online'
              
              // 订阅聊天室
              this.stompClient.subscribe(`/topic/chat/${this.roomCode}`, (message) => {
                const data = JSON.parse(message.body)
                this.addLog('info', `收到WebSocket消息: ${JSON.stringify(data)}`)
              })
              
              // 加入聊天室
              this.stompClient.send(`/app/chat.joinRoom/${this.roomCode}`, {}, JSON.stringify({}))
              this.addLog('info', `已加入聊天室: ${this.roomCode}`)
              
              resolve(frame)
            },
            (error) => {
              this.addLog('error', `WebSocket连接失败: ${error}`)
              this.isConnected = false
              this.serviceStatus = 'offline'
              reject(error)
            }
          )
        })
      } catch (error) {
        this.addLog('error', `WebSocket连接异常: ${error.message}`)
      }
    },

    testSendMessage() {
      if (!this.roomCode) {
        this.addLog('error', '没有roomCode，无法发送消息')
        return
      }

      const testMessage = '这是一条测试消息 - ' + new Date().toLocaleTimeString()
      this.addLog('info', `准备发送测试消息: ${testMessage}`)

      if (this.stompClient && this.isConnected && this.stompClient.connected) {
        try {
          const messageData = { content: testMessage }
          const destination = `/app/chat.sendMessage/${this.roomCode}`
          
          this.addLog('info', `发送到: ${destination}`)
          this.stompClient.send(destination, {}, JSON.stringify(messageData))
          this.addLog('success', '消息发送成功')
        } catch (error) {
          this.addLog('error', `消息发送失败: ${error.message}`)
        }
      } else {
        this.addLog('warning', 'WebSocket未连接，无法发送消息')
      }
    },

    resetState() {
      this.roomCode = null
      this.isConnected = false
      this.serviceStatus = 'offline'
      if (this.stompClient) {
        this.stompClient.disconnect()
        this.stompClient = null
      }
      this.testLogs = []
      this.addLog('info', '状态已重置')
    }
  }
}
</script>

<style scoped>
.chat-debug-test {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 30px;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 15px;
}

.status-info p {
  margin: 5px 0;
}

.test-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.test-buttons button {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  background: #007bff;
  color: white;
  cursor: pointer;
}

.test-buttons button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.test-logs {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #eee;
  border-radius: 4px;
  padding: 10px;
  background: #f9f9f9;
}

.log-item {
  margin-bottom: 5px;
  font-family: monospace;
  font-size: 12px;
}

.log-item.success {
  color: #28a745;
}

.log-item.error {
  color: #dc3545;
}

.log-item.warning {
  color: #ffc107;
}

.log-item.info {
  color: #17a2b8;
}

.log-item.debug {
  color: #6c757d;
}

.log-time {
  color: #666;
  margin-right: 10px;
}
</style>
