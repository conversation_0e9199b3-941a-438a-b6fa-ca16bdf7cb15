<template>
  <div class="chat-test-container">
    <el-card class="test-card">
      <div slot="header" class="clearfix">
        <span>聊天功能测试</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="goBack">返回首页</el-button>
      </div>
      
      <div class="test-section">
        <h3>🔗 连接状态</h3>
        <div class="status-info">
          <el-tag :type="connectionStatus === 'connected' ? 'success' : 'danger'">
            {{ connectionStatusText }}
          </el-tag>
        </div>
      </div>

      <div class="test-section">
        <h3>🧪 功能测试</h3>
        <div class="test-buttons">
          <el-button type="primary" @click="testUserChat" :loading="testing">
            测试用户聊天
          </el-button>
          <el-button type="success" @click="testQuickStart" :loading="testing">
            测试快速开始
          </el-button>
          <el-button type="info" @click="testAPI" :loading="testing">
            测试API接口
          </el-button>
        </div>
      </div>
      
      <div class="test-section" v-if="testResults.length > 0">
        <h3>📊 测试结果</h3>
        <div class="test-results">
          <div v-for="(result, index) in testResults" :key="index" 
               :class="['test-result-item', result.type]">
            <div class="result-header">
              <i :class="result.type === 'success' ? 'el-icon-success' : 'el-icon-error'"></i>
              <span class="result-title">{{ result.title }}</span>
              <span class="result-time">{{ result.time }}</span>
            </div>
            <div class="result-content">{{ result.message }}</div>
            <div v-if="result.data" class="result-data">
              <pre>{{ JSON.stringify(result.data, null, 2) }}</pre>
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 用户聊天组件 -->
    <UserChat ref="userChat" />
  </div>
</template>

<script>
import UserChat from './UserChat.vue'
import axios from 'axios'

export default {
  name: 'ChatTest',
  components: {
    UserChat
  },
  data() {
    return {
      testing: false,
      connectionStatus: 'disconnected',
      testResults: []
    }
  },
  computed: {
    connectionStatusText() {
      return this.connectionStatus === 'connected' ? '已连接' : '未连接'
    }
  },
  methods: {
    goBack() {
      this.$router.push('/index')
    },

    addTestResult(title, type, message, data = null) {
      this.testResults.unshift({
        title,
        type,
        message,
        data,
        time: new Date().toLocaleTimeString()
      })
    },

    async testUserChat() {
      this.testing = true
      try {
        console.log('🧪 开始测试用户聊天...')
        
        // 触发用户聊天组件
        if (this.$refs.userChat) {
          await this.$refs.userChat.startChat()
          this.addTestResult('用户聊天测试', 'success', '聊天窗口已打开，请查看右下角')
        } else {
          this.addTestResult('用户聊天测试', 'error', 'UserChat组件未找到')
        }
      } catch (error) {
        console.error('❌ 用户聊天测试失败:', error)
        this.addTestResult('用户聊天测试', 'error', error.message)
      } finally {
        this.testing = false
      }
    },

    async testQuickStart() {
      this.testing = true
      try {
        console.log('🧪 开始测试快速开始聊天...')
        
        const token = localStorage.getItem('token')
        if (!token) {
          throw new Error('未找到用户token，请先登录')
        }

        const response = await axios.post('/api/chat/quick-start', {}, {
          headers: { 'Authorization': token }
        })

        if (response.data.code === 200) {
          this.addTestResult('快速开始测试', 'success', '快速开始聊天成功', response.data.data)
          this.connectionStatus = 'connected'
        } else {
          this.addTestResult('快速开始测试', 'error', response.data.message || '快速开始失败')
        }
      } catch (error) {
        console.error('❌ 快速开始测试失败:', error)
        this.addTestResult('快速开始测试', 'error', error.message)
      } finally {
        this.testing = false
      }
    },

    async testAPI() {
      this.testing = true
      try {
        console.log('🧪 开始测试API接口...')
        
        const token = localStorage.getItem('token')
        if (!token) {
          throw new Error('未找到用户token，请先登录')
        }

        // 测试获取用户信息
        const userInfoResponse = await axios.get('/api/user/getInfo', {
          headers: { 'Authorization': token }
        })

        if (userInfoResponse.data.code === '200') {
          this.addTestResult('用户信息API', 'success', '获取用户信息成功', userInfoResponse.data.data)
        } else {
          this.addTestResult('用户信息API', 'error', '获取用户信息失败')
        }

        // 测试获取待处理消息（如果是客服）
        try {
          const pendingResponse = await axios.get('/api/chat/service/pending-rooms', {
            headers: { 'Authorization': token }
          })

          if (pendingResponse.data.code === 200) {
            this.addTestResult('待处理消息API', 'success', '获取待处理消息成功', pendingResponse.data.data)
          } else {
            this.addTestResult('待处理消息API', 'error', pendingResponse.data.message)
          }
        } catch (error) {
          this.addTestResult('待处理消息API', 'error', '可能不是客服用户: ' + error.message)
        }

      } catch (error) {
        console.error('❌ API测试失败:', error)
        this.addTestResult('API测试', 'error', error.message)
      } finally {
        this.testing = false
      }
    }
  }
}
</script>

<style scoped>
.chat-test-container {
  max-width: 800px;
  margin: 20px auto;
  padding: 20px;
}

.test-card {
  margin-bottom: 20px;
}

.test-section {
  margin-bottom: 30px;
}

.test-section h3 {
  color: #409EFF;
  margin-bottom: 15px;
}

.status-info {
  margin-bottom: 15px;
}

.test-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.test-results {
  max-height: 400px;
  overflow-y: auto;
}

.test-result-item {
  border: 1px solid #EBEEF5;
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 10px;
}

.test-result-item.success {
  border-color: #67C23A;
  background-color: #F0F9FF;
}

.test-result-item.error {
  border-color: #F56C6C;
  background-color: #FEF0F0;
}

.result-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.result-header i {
  margin-right: 8px;
  font-size: 16px;
}

.result-header i.el-icon-success {
  color: #67C23A;
}

.result-header i.el-icon-error {
  color: #F56C6C;
}

.result-title {
  font-weight: bold;
  flex: 1;
}

.result-time {
  color: #909399;
  font-size: 12px;
}

.result-content {
  color: #606266;
  margin-bottom: 8px;
}

.result-data {
  background: #F5F7FA;
  border-radius: 4px;
  padding: 10px;
  font-size: 12px;
}

.result-data pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>
