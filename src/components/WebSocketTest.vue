<template>
  <div class="websocket-test">
    <h2>WebSocket连接测试</h2>
    
    <div class="test-section">
      <h3>连接状态</h3>
      <div class="status-grid">
        <div class="status-item">
          <label>后端HTTP状态:</label>
          <span :class="['status', httpStatus]">{{ httpStatusText }}</span>
        </div>
        <div class="status-item">
          <label>WebSocket状态:</label>
          <span :class="['status', wsStatus]">{{ wsStatusText }}</span>
        </div>
        <div class="status-item">
          <label>Token状态:</label>
          <span :class="['status', tokenStatus]">{{ tokenStatusText }}</span>
        </div>
      </div>
    </div>

    <div class="test-section">
      <h3>测试操作</h3>
      <div class="button-group">
        <button @click="testHttpConnection" :disabled="testing">测试HTTP连接</button>
        <button @click="testWebSocketConnection" :disabled="testing">测试WebSocket连接</button>
        <button @click="testQuickStart" :disabled="testing">测试快速开始</button>
        <button @click="clearLogs">清空日志</button>
      </div>
    </div>

    <div class="test-section">
      <h3>测试日志</h3>
      <div class="log-container" ref="logContainer">
        <div v-for="(log, index) in logs" :key="index" :class="['log-entry', log.level]">
          <span class="log-time">{{ formatTime(log.time) }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios'
import SockJS from 'sockjs-client'
import Stomp from 'stompjs'

export default {
  name: 'WebSocketTest',
  data() {
    return {
      testing: false,
      httpStatus: 'unknown',
      wsStatus: 'unknown',
      tokenStatus: 'unknown',
      logs: []
    }
  },
  computed: {
    httpStatusText() {
      const statusMap = {
        'unknown': '未知',
        'success': '正常',
        'error': '错误'
      }
      return statusMap[this.httpStatus] || '未知'
    },
    wsStatusText() {
      const statusMap = {
        'unknown': '未知',
        'success': '已连接',
        'error': '连接失败'
      }
      return statusMap[this.wsStatus] || '未知'
    },
    tokenStatusText() {
      const statusMap = {
        'unknown': '未知',
        'success': '有效',
        'error': '无效'
      }
      return statusMap[this.tokenStatus] || '未知'
    }
  },
  mounted() {
    this.checkToken()
  },
  methods: {
    addLog(level, message) {
      this.logs.push({
        level,
        message,
        time: new Date()
      })
      this.$nextTick(() => {
        const container = this.$refs.logContainer
        if (container) {
          container.scrollTop = container.scrollHeight
        }
      })
    },

    formatTime(time) {
      return time.toLocaleTimeString()
    },

    checkToken() {
      const token = localStorage.getItem('token')
      if (token) {
        this.tokenStatus = 'success'
        this.addLog('info', `Token存在: ${token.substring(0, 20)}...`)
      } else {
        this.tokenStatus = 'error'
        this.addLog('error', 'Token不存在，请先登录')
      }
    },

    async testHttpConnection() {
      this.testing = true
      this.addLog('info', '开始测试HTTP连接...')
      
      try {
        const token = localStorage.getItem('token')
        if (!token) {
          throw new Error('未找到token')
        }

        // 测试基本API连接
        const response = await axios.get('/api/user/info', {
          headers: { 'Authorization': token },
          timeout: 5000
        })

        this.httpStatus = 'success'
        this.addLog('success', `HTTP连接成功: ${response.status}`)
        this.addLog('info', `响应数据: ${JSON.stringify(response.data)}`)
      } catch (error) {
        this.httpStatus = 'error'
        this.addLog('error', `HTTP连接失败: ${error.message}`)
        if (error.response) {
          this.addLog('error', `响应状态: ${error.response.status}`)
          this.addLog('error', `响应数据: ${JSON.stringify(error.response.data)}`)
        }
      } finally {
        this.testing = false
      }
    },

    async testWebSocketConnection() {
      this.testing = true
      this.addLog('info', '开始测试WebSocket连接...')
      
      try {
        const token = localStorage.getItem('token')
        if (!token) {
          throw new Error('未找到token')
        }

        this.addLog('info', '创建SockJS连接: /ws/chat')
        const socket = new SockJS('/ws/chat')
        const stompClient = Stomp.over(socket)

        // 启用调试
        stompClient.debug = (str) => {
          this.addLog('debug', `STOMP: ${str}`)
        }

        await new Promise((resolve, reject) => {
          const timeout = setTimeout(() => {
            reject(new Error('连接超时'))
          }, 15000)

          stompClient.connect(
            { 'Authorization': token },
            (frame) => {
              clearTimeout(timeout)
              this.wsStatus = 'success'
              this.addLog('success', 'WebSocket连接成功')
              this.addLog('info', `会话信息: ${JSON.stringify(frame.headers)}`)

              // 断开连接
              stompClient.disconnect(() => {
                this.addLog('info', 'WebSocket连接已断开')
              })
              resolve()
            },
            (error) => {
              clearTimeout(timeout)
              this.wsStatus = 'error'
              this.addLog('error', `WebSocket连接失败: ${error}`)
              reject(error)
            }
          )
        })
      } catch (error) {
        this.wsStatus = 'error'
        this.addLog('error', `WebSocket测试失败: ${error.message}`)
      } finally {
        this.testing = false
      }
    },

    async testQuickStart() {
      this.testing = true
      this.addLog('info', '开始测试快速开始聊天...')
      
      try {
        const token = localStorage.getItem('token')
        if (!token) {
          throw new Error('未找到token')
        }

        const response = await axios.post('/api/chat/quick-start', {}, {
          headers: { 'Authorization': token },
          timeout: 5000
        })

        this.addLog('success', '快速开始聊天成功')
        this.addLog('info', `响应数据: ${JSON.stringify(response.data, null, 2)}`)
        
        if (response.data.data && response.data.data.roomCode) {
          this.addLog('success', `获得房间代码: ${response.data.data.roomCode}`)
        }
      } catch (error) {
        this.addLog('error', `快速开始聊天失败: ${error.message}`)
        if (error.response) {
          this.addLog('error', `响应状态: ${error.response.status}`)
          this.addLog('error', `响应数据: ${JSON.stringify(error.response.data)}`)
        }
      } finally {
        this.testing = false
      }
    },

    clearLogs() {
      this.logs = []
    }
  }
}
</script>

<style scoped>
.websocket-test {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 30px;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 20px;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 4px;
}

.status {
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: bold;
}

.status.success {
  background: #d4edda;
  color: #155724;
}

.status.error {
  background: #f8d7da;
  color: #721c24;
}

.status.unknown {
  background: #e2e3e5;
  color: #383d41;
}

.button-group {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.button-group button {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  background: #007bff;
  color: white;
  cursor: pointer;
}

.button-group button:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.log-container {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 10px;
  background: #f8f9fa;
  font-family: monospace;
}

.log-entry {
  margin-bottom: 5px;
  padding: 2px 0;
}

.log-entry.success {
  color: #28a745;
}

.log-entry.error {
  color: #dc3545;
}

.log-entry.info {
  color: #17a2b8;
}

.log-entry.debug {
  color: #6c757d;
  font-size: 0.9em;
}

.log-time {
  color: #666;
  margin-right: 10px;
}
</style>
