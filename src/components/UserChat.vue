<template>
  <div class="user-chat-container">


    <!-- 聊天图标按钮 -->
    <div v-if="!isOpen" class="chat-icon" @click="startChat" :style="iconPosition">
      <i class="el-icon-service"></i>
      <div class="chat-badge" v-if="unreadCount > 0">{{ unreadCount }}</div>
    </div>

    <!-- 聊天窗口 -->
    <div v-if="isOpen" class="chat-window" :style="windowPosition" ref="chatWindow">
      <!-- 聊天窗口头部 -->
      <div class="chat-header" @mousedown="startDrag">
        <div class="header-left">
          <div class="service-avatar">
            <i class="el-icon-service"></i>
          </div>
          <div class="service-info">
            <div class="service-name">在线客服</div>
            <div class="service-status" :class="connectionStatus">
              {{ connectionStatusText }}
            </div>
          </div>
        </div>
        <div class="header-actions">
          <el-button type="text" icon="el-icon-minus" @click="minimizeChat" size="mini"></el-button>
          <el-button type="text" icon="el-icon-close" @click="closeChat" size="mini"></el-button>
        </div>
      </div>

      <!-- 聊天消息区域 -->
      <div class="chat-messages" ref="messagesContainer">
        <div v-if="loading" class="loading-indicator">
          <i class="el-icon-loading"></i>
          <span>连接中...</span>
        </div>

        <div v-if="!loading && messages.length === 0" class="empty-messages">
          <i class="el-icon-chat-dot-round"></i>
          <p>开始与客服对话吧！</p>
        </div>

        <div v-for="message in messages" :key="message.id" class="message-item"
             :class="{ 'own-message': isOwnMessage(message) }">
          <div class="message-avatar">
            <img v-if="message.senderAvatar" :src="message.senderAvatar" :alt="message.senderName">
            <i v-else :class="isOwnMessage(message) ? 'el-icon-user' : 'el-icon-service'"></i>
          </div>
          <div class="message-content">
            <div class="message-info">
              <span class="sender-name">{{ message.senderName }}</span>
              <span class="message-time">{{ formatTime(message.createTime) }}</span>
            </div>
            <div class="message-text">{{ message.content }}</div>
          </div>
        </div>
      </div>

      <!-- 输入区域 -->
      <div class="chat-input">
        <div class="input-container">
          <el-input
            v-model="inputMessage"
            type="textarea"
            :rows="2"
            placeholder="请输入消息..."
            @keydown.enter.exact="handleEnterKey"
            :disabled="!isConnected || loading"
            resize="none">
          </el-input>
          <el-button
            type="primary"
            @click="sendMessage"
            :disabled="!inputMessage.trim() || !isConnected || loading"
            size="small">
            发送
          </el-button>
        </div>
        <div class="input-tips">
          <span v-if="!isConnected" class="status-tip error">连接已断开</span>
          <span v-else-if="loading" class="status-tip warning">连接中...</span>
          <span v-else class="status-tip success">按Enter发送，Shift+Enter换行</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'UserChat',
  data() {
    return {
      isOpen: false,
      loading: false,
      isConnected: false,
      inputMessage: '',
      messages: [],
      unreadCount: 0,
      roomCode: null,
      roomId: null,
      chatStatus: null,
      currentUserId: null,

      // WebSocket相关
      stompClient: null,
      subscription: null,

      // 拖拽相关
      isDragging: false,
      dragOffset: { x: 0, y: 0 },
      windowPosition: {
        position: 'fixed',
        right: '20px',
        bottom: '20px',
        zIndex: 1000
      },
      iconPosition: {
        right: '20px',
        bottom: '20px'
      }
    }
  },
  computed: {
    connectionStatus() {
      if (this.loading) return 'connecting'
      return this.isConnected ? 'online' : 'offline'
    },
    connectionStatusText() {
      const statusMap = {
        connecting: '连接中',
        online: '在线',
        offline: '离线'
      }
      return statusMap[this.connectionStatus] || '未知'
    }
  },
  async created() {
    // 获取当前用户ID - 暂时使用简单方式
    try {
      const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')
      this.currentUserId = userInfo.id
    } catch (error) {
      console.error('获取用户ID失败:', error)
    }
  },
  beforeDestroy() {
    this.cleanup()
  },
  methods: {
    /**
     * 开始聊天
     */
    async startChat() {
      console.log('🚀 [UserChat] 开始聊天')

      if (this.loading) {
        console.log('⚠️ 正在加载中，跳过重复调用')
        return
      }

      this.loading = true

      try {
        // 先测试简单的API调用
        console.log('📡 [UserChat] 调用快速开始接口...')
        console.log('📡 [UserChat] 当前token:', localStorage.getItem('token'))

        const response = await this.$http.post('/api/chat/quick-start')
        console.log('📡 [UserChat] 快速开始响应:', response)
        console.log('📡 [UserChat] 响应数据:', response.data)

        if (response.data && (response.data.code === 200 || response.data.code === "200")) {
          const data = response.data.data
          this.roomCode = data.roomCode
          this.roomId = data.roomId
          this.chatStatus = data.status

          console.log('✅ [UserChat] 获取到roomCode:', this.roomCode)

          // 2. 建立WebSocket连接
          await this.connectWebSocket()

          // 3. 显示聊天窗口
          this.isOpen = true
          this.loading = false

          // 4. 加载聊天历史
          await this.loadChatHistory()

          // 5. 加入聊天室
          this.joinRoom()

          // 6. 滚动到底部
          this.$nextTick(() => {
            this.scrollToBottom()
          })

        } else {
          throw new Error(response.data?.message || '启动聊天失败')
        }

      } catch (error) {
        console.error('❌ [UserChat] 启动聊天失败:', error)
        console.error('❌ [UserChat] 错误详情:', error.response)
        this.loading = false

        let errorMessage = '启动聊天失败，请稍后重试'
        if (error.response) {
          errorMessage += `（状态码: ${error.response.status}）`
        }
        this.$message.error(errorMessage)
      }
    },

    /**
     * 处理接收到的消息
     */
    handleMessage(message) {
      console.log('📨 [UserChat] 收到新消息:', message)

      // 格式化消息
      const formattedMessage = this.formatMessage(message)
      this.messages.push(formattedMessage)

      this.$nextTick(() => {
        this.scrollToBottom()
      })
    },

    /**
     * 建立WebSocket连接
     */
    async connectWebSocket() {
      console.log('🔌 [UserChat] 建立WebSocket连接...')

      try {
        // 检查全局对象是否存在
        if (typeof SockJS === 'undefined' || typeof Stomp === 'undefined') {
          throw new Error('SockJS或Stomp未加载')
        }

        // 获取认证token
        const token = localStorage.getItem('token')
        if (!token) {
          throw new Error('未找到认证token')
        }

        // 创建WebSocket连接 - 使用正确的端点
        const socket = new SockJS('/ws/chat')
        this.stompClient = Stomp.over(socket)

        // 设置调试信息
        this.stompClient.debug = (str) => {
          console.log('🔌 [WebSocket]', str)
        }

        // 连接WebSocket
        await new Promise((resolve, reject) => {
          this.stompClient.connect(
            { Authorization: localStorage.getItem('token') },
            (frame) => {
              console.log('✅ [UserChat] WebSocket连接成功:', frame)
              this.isConnected = true
              resolve()
            },
            (error) => {
              console.error('❌ [UserChat] WebSocket连接失败:', error)
              this.isConnected = false
              reject(error)
            }
          )
        })

        // 订阅聊天室
        this.subscribeToRoom()

      } catch (error) {
        console.error('❌ [UserChat] WebSocket连接异常:', error)
        this.isConnected = false
        throw error
      }
    },

    /**
     * 订阅聊天室
     */
    subscribeToRoom() {
      if (!this.stompClient || !this.roomCode) {
        console.warn('⚠️ [UserChat] 无法订阅聊天室: stompClient或roomCode为空')
        return
      }

      console.log('📡 [UserChat] 订阅聊天室:', this.roomCode)

      this.subscription = this.stompClient.subscribe(
        `/topic/chat/${this.roomCode}`,
        (message) => {
          const data = JSON.parse(message.body)
          console.log('📨 [UserChat] 收到WebSocket消息:', data)
          this.handleMessage(data)
        }
      )
    },

    /**
     * 加入聊天室
     */
    joinRoom() {
      if (!this.stompClient || !this.roomCode) {
        console.warn('⚠️ [UserChat] 无法加入聊天室: stompClient或roomCode为空')
        return
      }

      console.log('🚪 [UserChat] 加入聊天室:', this.roomCode)

      this.stompClient.send(`/app/chat.joinRoom/${this.roomCode}`, {}, JSON.stringify({}))
    },

    /**
     * 格式化消息
     */
    formatMessage(message) {
      // 获取用户信息作为默认值
      const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')

      // 判断是否为自己的消息
      const isOwnMessage = message.senderId === this.currentUserId

      // 根据消息类型设置默认信息
      let defaultName, defaultAvatar
      if (isOwnMessage) {
        // 自己的消息：使用用户信息
        defaultName = userInfo.nickName || userInfo.userName || '用户'
        defaultAvatar = userInfo.avatar
      } else {
        // 其他人的消息：默认为客服
        defaultName = '客服'
        defaultAvatar = null // 使用默认客服头像
      }

      // 处理时间格式
      let createTime = message.createTime
      console.log('🕐 [UserChat] formatMessage time debug:', {
        originalCreateTime: message.createTime,
        createTimeType: typeof message.createTime,
        messageId: message.id
      })

      if (createTime) {
        // 如果是字符串，转换为Date对象
        if (typeof createTime === 'string') {
          // 处理UTC时间字符串，确保正确解析
          if (createTime.includes('T') && createTime.includes('+00:00')) {
            // 这是UTC时间格式，直接解析
            createTime = new Date(createTime)
          } else if (createTime.includes('T')) {
            // 其他ISO格式
            createTime = new Date(createTime)
          } else {
            // 其他格式
            createTime = new Date(createTime)
          }
        }
        // 如果转换失败，使用当前时间
        if (isNaN(createTime.getTime())) {
          console.warn('⚠️ [UserChat] Invalid createTime, using current time:', message.createTime)
          createTime = new Date()
        }
      } else {
        // 如果没有时间，使用当前时间
        console.warn('⚠️ [UserChat] No createTime, using current time')
        createTime = new Date()
      }

      return {
        id: message.id,
        senderId: message.senderId,
        senderName: message.senderName || defaultName,
        senderAvatar: message.senderAvatar || defaultAvatar,
        senderType: message.senderType || (isOwnMessage ? 'user' : 'service'),
        content: message.content,
        createTime: createTime,
        isOwn: isOwnMessage
      }
    },

    /**
     * 加载聊天历史
     */
    async loadChatHistory() {
      try {
        if (!this.roomCode) {
          console.warn('⚠️ [UserChat] 无法加载历史消息: roomCode为空')
          return
        }

        console.log('📜 [UserChat] 开始加载聊天历史:', this.roomCode)

        const response = await this.$http.get(`/api/chat/history/${this.roomCode}`, {
          params: {
            page: 1,
            size: 20
          }
        })

        if (response.data && (response.data.code === 200 || response.data.code === "200")) {
          const data = response.data.data

          if (data.messages && data.messages.length > 0) {
            this.messages = data.messages.map(msg => this.formatMessage(msg))
            console.log('📜 [UserChat] 加载历史消息成功:', this.messages.length, '条')
          } else {
            // 添加欢迎消息
            this.messages = [
              {
                id: Date.now(),
                senderId: 'system',
                senderName: '系统',
                senderAvatar: null,
                senderType: 'system',
                content: '欢迎使用在线客服！客服将尽快为您服务。',
                createTime: new Date(),
                isOwn: false
              }
            ]
            console.log('📜 [UserChat] 无历史消息，显示欢迎消息')
          }
        } else {
          console.warn('⚠️ [UserChat] 加载历史消息失败:', response.data?.message)
          // 即使加载失败也显示欢迎消息
          this.messages = [
            {
              id: Date.now(),
              senderId: 'system',
              senderName: '系统',
              senderAvatar: null,
              senderType: 'system',
              content: '欢迎使用在线客服！客服将尽快为您服务。',
              createTime: new Date(),
              isOwn: false
            }
          ]
        }

      } catch (error) {
        console.error('❌ [UserChat] 加载聊天历史失败:', error)
        // 加载失败时显示欢迎消息
        this.messages = [
          {
            id: Date.now(),
            senderId: 'system',
            senderName: '系统',
            senderAvatar: null,
            senderType: 'system',
            content: '欢迎使用在线客服！客服将尽快为您服务。',
            createTime: new Date(),
            isOwn: false
          }
        ]
      }
    },

    /**
     * 发送消息
     */
    async sendMessage() {
      if (!this.inputMessage.trim() || !this.isConnected || this.loading) {
        console.log('⚠️ [UserChat] 无法发送消息:', {
          hasInput: !!this.inputMessage.trim(),
          isConnected: this.isConnected,
          loading: this.loading
        })
        return
      }

      if (!this.stompClient || !this.roomCode) {
        console.warn('⚠️ [UserChat] 无法发送消息: stompClient或roomCode为空')
        this.$message.error('聊天连接异常，请重新开始聊天')
        return
      }

      const content = this.inputMessage.trim()
      this.inputMessage = ''

      try {
        console.log('📤 [UserChat] 发送消息:', content)

        // 获取用户信息
        const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')

        // 通过WebSocket发送消息
        const messageRequest = {
          content: content,
          senderName: userInfo.nickName || userInfo.userName || '用户',
          senderAvatar: userInfo.avatar
        }

        this.stompClient.send(
          `/app/chat.sendMessage/${this.roomCode}`,
          {},
          JSON.stringify(messageRequest)
        )

        console.log('✅ [UserChat] 消息发送成功')

      } catch (error) {
        console.error('❌ [UserChat] 发送消息失败:', error)
        this.$message.error('发送消息失败：' + error.message)
        // 恢复输入内容
        this.inputMessage = content
      }
    },

    /**
     * 处理WebSocket消息
     */
    handleMessage(data) {
      console.log('📨 [UserChat] 处理WebSocket消息:', data)

      try {
        // 处理不同类型的消息
        if (data.type === 'USER_JOINED') {
          // 用户加入通知
          console.log('👋 [UserChat] 用户加入:', data.userName)
          return
        }

        // 处理聊天消息
        if (data.content) {
          const message = this.formatMessage(data)
          this.messages.push(message)

          // 滚动到底部
          this.$nextTick(() => {
            this.scrollToBottom()
          })

          console.log('✅ [UserChat] 消息已添加到聊天记录')
        }

      } catch (error) {
        console.error('❌ [UserChat] 处理消息失败:', error)
      }
    },

    /**
     * 处理Enter键
     */
    handleEnterKey(event) {
      if (!event.shiftKey) {
        event.preventDefault()
        this.sendMessage()
      }
    },

    /**
     * 判断是否为自己发送的消息
     */
    isOwnMessage(message) {
      return message.senderId === this.currentUserId
    },

    /**
     * 格式化时间 - 直接显示实际日期时间，精确到分钟
     */
    formatTime(time) {
      if (!time) return ''

      let date
      // 处理时间格式
      if (typeof time === 'string') {
        // 如果是UTC时间格式，直接解析（JavaScript会自动转换为本地时间）
        date = new Date(time)
      } else if (time instanceof Date) {
        date = time
      } else {
        date = new Date(time)
      }

      // 检查时间是否有效
      if (isNaN(date.getTime())) {
        console.warn('⚠️ [UserChat] 无效的时间格式:', time)
        return '时间错误'
      }

      const now = new Date()
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
      const messageDate = new Date(date.getFullYear(), date.getMonth(), date.getDate())

      // 判断是否是今天
      if (messageDate.getTime() === today.getTime()) {
        // 今天的消息，只显示时间 HH:mm
        return date.toLocaleTimeString('zh-CN', {
          hour: '2-digit',
          minute: '2-digit',
          hour12: false
        })
      } else {
        // 不是今天的消息，显示完整日期时间 MM/dd HH:mm
        return date.toLocaleString('zh-CN', {
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          hour12: false
        })
      }
    },

    /**
     * 滚动到底部
     */
    scrollToBottom() {
      if (this.$refs.messagesContainer) {
        this.$refs.messagesContainer.scrollTop = this.$refs.messagesContainer.scrollHeight
      }
    },

    /**
     * 最小化聊天窗口
     */
    minimizeChat() {
      this.isOpen = false
    },

    /**
     * 关闭聊天窗口
     */
    closeChat() {
      this.isOpen = false
      this.cleanup()
    },

    /**
     * 清理资源
     */
    cleanup() {
      console.log('🧹 [UserChat] 清理资源...')

      // 断开WebSocket连接
      if (this.subscription) {
        this.subscription.unsubscribe()
        this.subscription = null
        console.log('🔌 [UserChat] 取消订阅')
      }

      if (this.stompClient && this.stompClient.connected) {
        this.stompClient.disconnect(() => {
          console.log('🔌 [UserChat] WebSocket连接已断开')
        })
        this.stompClient = null
      }

      // 清理数据
      this.messages = []
      this.roomCode = null
      this.roomId = null
      this.chatStatus = null
      this.isConnected = false
      this.unreadCount = 0
    },

    /**
     * 开始拖拽
     */
    startDrag(event) {
      this.isDragging = true
      const rect = this.$refs.chatWindow.getBoundingClientRect()
      this.dragOffset.x = event.clientX - rect.left
      this.dragOffset.y = event.clientY - rect.top

      document.addEventListener('mousemove', this.onDrag)
      document.addEventListener('mouseup', this.stopDrag)
    },

    /**
     * 拖拽中
     */
    onDrag(event) {
      if (!this.isDragging) return

      const x = event.clientX - this.dragOffset.x
      const y = event.clientY - this.dragOffset.y

      this.windowPosition = {
        position: 'fixed',
        left: Math.max(0, Math.min(x, window.innerWidth - 350)) + 'px',
        top: Math.max(0, Math.min(y, window.innerHeight - 500)) + 'px',
        right: 'auto',
        bottom: 'auto',
        zIndex: 1000
      }
    },

    /**
     * 停止拖拽
     */
    stopDrag() {
      this.isDragging = false
      document.removeEventListener('mousemove', this.onDrag)
      document.removeEventListener('mouseup', this.stopDrag)
    }
  }
}
</script>

<style scoped>
.user-chat-container {
  position: fixed;
  z-index: 9999;
}

.chat-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #409EFF, #67C23A);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
  transition: all 0.3s ease;
  position: relative;
}

.chat-icon:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 16px rgba(64, 158, 255, 0.4);
}

.chat-icon i {
  font-size: 24px;
  color: white;
}

.chat-badge {
  position: absolute;
  top: -5px;
  right: -5px;
  background: #F56C6C;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
}

.chat-window {
  width: 350px;
  height: 500px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.chat-header {
  background: linear-gradient(135deg, #409EFF, #67C23A);
  color: white;
  padding: 12px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: move;
}

.header-left {
  display: flex;
  align-items: center;
}

.service-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.service-avatar i {
  font-size: 18px;
}

.service-name {
  font-weight: bold;
  font-size: 14px;
}

.service-status {
  font-size: 12px;
  opacity: 0.9;
}

.service-status.connecting {
  color: #E6A23C;
}

.service-status.online {
  color: #67C23A;
}

.service-status.offline {
  color: #F56C6C;
}

.header-actions .el-button {
  color: white;
  padding: 4px;
}

.chat-messages {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  background: #f8f9fa;
}

.loading-indicator {
  text-align: center;
  padding: 40px 20px;
  color: #909399;
}

.loading-indicator i {
  font-size: 24px;
  margin-right: 8px;
}

.empty-messages {
  text-align: center;
  padding: 40px 20px;
  color: #909399;
}

.empty-messages i {
  font-size: 48px;
  margin-bottom: 12px;
  display: block;
}

.message-item {
  display: flex;
  margin-bottom: 16px;
}

.message-item.own-message {
  flex-direction: row-reverse;
}

.message-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #E4E7ED;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 8px;
  flex-shrink: 0;
}

.message-avatar img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.message-avatar i {
  font-size: 16px;
  color: #909399;
}

.message-content {
  max-width: 70%;
}

.own-message .message-content {
  text-align: right;
}

.message-info {
  margin-bottom: 4px;
  font-size: 12px;
  color: #909399;
}

.message-text {
  background: white;
  padding: 8px 12px;
  border-radius: 12px;
  word-wrap: break-word;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.own-message .message-text {
  background: #409EFF;
  color: white;
}

.chat-input {
  border-top: 1px solid #E4E7ED;
  padding: 12px 16px;
  background: white;
}

.input-container {
  display: flex;
  gap: 8px;
  align-items: flex-end;
}

.input-container .el-textarea {
  flex: 1;
}

.input-tips {
  margin-top: 8px;
  font-size: 12px;
  text-align: center;
}

.status-tip.success {
  color: #67C23A;
}

.status-tip.warning {
  color: #E6A23C;
}

.status-tip.error {
  color: #F56C6C;
}
</style>
