<template>
  <div class="register-container" style="background-image: url('./assets/background.jpg'); background-size: cover; background-position: center;">
    <h2>注册</h2>
    <form @submit.prevent="handleRegister">
      <div class="form-group">
        <label for="username">用户名:</label>
        <input type="text" id="username" v-model="username" required>
        <small class="hint">请输入您的用户名</small>
      </div>
      <div class="form-group">
        <label for="password">密码:</label>
        <input type="password" id="password" v-model="password" required>
        <small class="hint">密码长度至少为6位</small>
      </div>
      <div class="form-group">
        <label for="confirmPassword">确认密码:</label>
        <input type="password" id="confirmPassword" v-model="confirmPassword" required>
        <small class="hint">请再次输入密码</small>
      </div>
      <button type="submit" class="register-button">注册</button>
    </form>
    <p v-if="errorMessage" class="error-message">{{ errorMessage }}</p>
    <p>已有账号? <router-link to="/login">登录</router-link></p>
  </div>
</template>

<script>
export default {
  data () {
    return {
      username: '',
      password: '',
      confirmPassword: '',
      errorMessage: ''
    }
  },
  methods: {
    handleRegister () {
      if (!this.username || !this.password || !this.confirmPassword) {
        this.errorMessage = '请填写所有字段'
        return
      }
      if (this.password.length < 6) {
        this.errorMessage = '密码长度至少为6位'
        return
      }
      if (this.password !== this.confirmPassword) {
        this.errorMessage = '两次输入的密码不一致'
        return
      }
      // 这里可以添加实际的注册逻辑
      this.errorMessage = ''
      this.$router.push('/login')
    }
  }
}
</script>

<style scoped>
.register-container {
  max-width: 300px;
  margin: 50px auto;
  padding: 20px;
  border: 1px solid #ccc;
  border-radius: 5px;
  text-align: center;
  backdrop-filter: blur(5px);
}
.form-group {
  margin-bottom: 15px;
  text-align: left;
}
label {
  display: block;
  margin-bottom: 5px;
}
input {
  width: 100%;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 3px;
}
.hint {
  color: #666;
  font-size: 12px;
}
.register-button {
  width: 100%;
  padding: 10px;
  background-color: #2196F3;
  color: white;
  border: none;
  border-radius: 3px;
  cursor: pointer;
  transition: background-color 0.3s;
}
.register-button:hover {
  background-color: #1976D2;
}
.error-message {
  color: red;
  margin-top: 10px;
}
</style>
