<template>
  <div class="workbench-container">
    <div class="workbench-header">
      <h2>客服工作台</h2>
      <div class="header-actions">
        <el-button @click="refreshPendingRooms" :loading="loading" size="small">
          <i class="el-icon-refresh"></i>
          刷新
        </el-button>
        <div class="connection-status" :class="connectionStatus">
          <i :class="connectionStatusIcon"></i>
          {{ connectionStatusText }}
        </div>
      </div>
    </div>

    <div class="workbench-content">
      <!-- 左侧：待处理消息列表 -->
      <div class="pending-panel">
        <div class="panel-header">
          <h3>待处理消息 ({{ pendingRooms.length }})</h3>
        </div>

        <div class="pending-list" v-loading="loading">
          <div v-if="pendingRooms.length === 0" class="empty-state">
            <i class="el-icon-chat-dot-round"></i>
            <p>暂无待处理消息</p>
          </div>

          <div
            v-for="room in pendingRooms"
            :key="room.roomId"
            class="pending-item"
            :class="{ active: currentRoom && currentRoom.roomId === room.roomId }"
            @click="takeOverRoom(room)">

            <div class="user-avatar">
              <img v-if="room.userAvatar" :src="room.userAvatar" :alt="room.userName">
              <i v-else class="el-icon-user"></i>
            </div>

            <div class="room-info">
              <div class="user-name">{{ room.userName }}</div>
              <div class="latest-message" :title="room.latestMessage">{{ room.latestMessage }}</div>
              <div class="message-time">{{ formatTime(room.latestMessageTime) }}</div>
            </div>

            <div class="unread-badge" v-if="room.unreadCount > 0">
              {{ room.unreadCount }}
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：聊天区域 -->
      <div class="chat-panel">
        <div v-if="!currentRoom" class="no-chat-selected">
          <i class="el-icon-chat-dot-round"></i>
          <p>请选择一个聊天室开始对话</p>
        </div>

        <div v-else class="chat-area">
          <!-- 聊天头部 -->
          <div class="chat-header">
            <div class="user-info">
              <div class="user-avatar">
                <img v-if="currentRoom.userAvatar" :src="currentRoom.userAvatar" :alt="currentRoom.userName">
                <i v-else class="el-icon-user"></i>
              </div>
              <div class="user-details">
                <div class="user-name">{{ currentRoom.userName }}</div>
                <div class="room-code">房间号: {{ currentRoom.roomCode }}</div>
              </div>
            </div>
            <div class="chat-actions">
              <el-button @click="endChat" size="small" type="danger">结束对话</el-button>
            </div>
          </div>

          <!-- 消息列表 -->
          <div class="messages-container" ref="messagesContainer">
            <div v-for="message in currentMessages" :key="message.id" class="message-item"
                 :class="{ 'service-message': message.senderType === 2 }">

              <div class="message-avatar">
                <img v-if="message.senderAvatar" :src="message.senderAvatar" :alt="message.senderName">
                <i v-else :class="message.senderType === 2 ? 'el-icon-service' : 'el-icon-user'"></i>
              </div>

              <div class="message-content">
                <div class="message-info">
                  <span class="sender-name">{{ message.senderName }}</span>
                  <span class="message-time">{{ formatTime(message.createTime) }}</span>
                </div>
                <div class="message-text">{{ message.content }}</div>
              </div>
            </div>
          </div>

          <!-- 输入区域 -->
          <div class="input-area">
            <div class="input-container">
              <el-input
                v-model="inputMessage"
                type="textarea"
                :rows="3"
                placeholder="请输入回复内容..."
                @keydown.enter.exact="handleEnterKey"
                :disabled="!isConnected"
                resize="none">
              </el-input>
              <div class="input-actions">
                <el-button
                  type="primary"
                  @click="sendMessage"
                  :disabled="!inputMessage.trim() || !isConnected"
                  size="small">
                  发送
                </el-button>
              </div>
            </div>
            <div class="input-tips">
              <span v-if="!isConnected" class="status-tip error">连接已断开</span>
              <span v-else class="status-tip success">按Enter发送，Shift+Enter换行</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import chatService from '@/utils/chatService'
import userPermissionService from '@/utils/userPermission'

export default {
  name: 'CustomerServiceWorkbench',
  data() {
    return {
      loading: false,
      isConnected: false,
      pendingRooms: [],
      currentRoom: null,
      currentMessages: [],
      inputMessage: '',
      refreshTimer: null,
      currentUserId: null
    }
  },
  computed: {
    connectionStatus() {
      return this.isConnected ? 'online' : 'offline'
    },
    connectionStatusText() {
      return this.isConnected ? '已连接' : '未连接'
    },
    connectionStatusIcon() {
      return this.isConnected ? 'el-icon-success' : 'el-icon-error'
    }
  },
  async created() {
    try {
      // 检查客服权限 - 直接调用后端API检查
      const token = localStorage.getItem('token')
      if (!token) {
        this.$message.error('请先登录')
        this.$router.push('/login')
        return
      }

      // 先尝试获取待处理消息，如果成功说明有客服权限
      console.log('🔍 检查客服权限...')
      await chatService.getPendingRooms()
      console.log('✅ 客服权限验证通过')

      // 获取用户信息
      const userType = await userPermissionService.getUserType()
      this.currentUserId = userType.userInfo.id

      await this.init()
    } catch (error) {
      console.error('❌ 客服工作台初始化失败:', error)
      if (error.message && (error.message.includes('403') || error.message.includes('权限'))) {
        this.$message.error('您没有客服权限')
        this.$router.push('/index')
      } else {
        this.$message.error('初始化失败：' + (error.message || '未知错误'))
      }
    }
  },
  beforeDestroy() {
    this.cleanup()
  },
  methods: {
    /**
     * 初始化工作台
     */
    async init() {
      try {
        // 1. 连接WebSocket
        const token = localStorage.getItem('token')
        await chatService.connect(token)
        this.isConnected = true

        // 2. 加载待处理消息
        await this.loadPendingRooms()

        // 3. 设置定时刷新
        this.refreshTimer = setInterval(() => {
          this.loadPendingRooms()
        }, 10000) // 每10秒刷新一次

        console.log('✅ 客服工作台初始化成功')
      } catch (error) {
        console.error('❌ 客服工作台初始化失败:', error)
        this.$message.error('工作台初始化失败：' + error.message)
      }
    },

    /**
     * 加载待处理消息列表
     */
    async loadPendingRooms() {
      try {
        this.loading = true
        console.log('🔄 开始加载待处理消息...')

        const rooms = await chatService.getPendingRooms()
        console.log('📋 API返回的原始数据:', rooms)

        // 确保返回的是数组
        if (!Array.isArray(rooms)) {
          console.warn('⚠️ API返回的数据不是数组:', typeof rooms, rooms)
          this.pendingRooms = []
          return
        }

        this.pendingRooms = rooms
        console.log('✅ 待处理消息加载成功:', this.pendingRooms.length, '条消息')
        console.log('📝 待处理消息详情:', this.pendingRooms)

        // 检查数据结构
        if (this.pendingRooms.length > 0) {
          const firstRoom = this.pendingRooms[0]
          console.log('🔍 第一条消息的数据结构:', {
            roomId: firstRoom.roomId,
            roomCode: firstRoom.roomCode,
            userName: firstRoom.userName,
            latestMessage: firstRoom.latestMessage,
            latestMessageLength: firstRoom.latestMessage ? firstRoom.latestMessage.length : 0,
            hasRequiredFields: !!(firstRoom.roomId && firstRoom.roomCode && firstRoom.userName)
          })

          // 检查所有房间的消息内容
          this.pendingRooms.forEach((room, index) => {
            console.log(`📝 房间${index + 1} [${room.roomCode}] 消息详情:`, {
              userName: room.userName,
              message: room.latestMessage,
              messageLength: room.latestMessage ? room.latestMessage.length : 0,
              messagePreview: room.latestMessage ? room.latestMessage.substring(0, 50) + '...' : 'null'
            })
          })
        }

      } catch (error) {
        console.error('❌ 加载待处理消息失败:', error)
        console.error('❌ 错误详情:', {
          message: error.message,
          response: error.response?.data,
          status: error.response?.status
        })

        if (error.response?.status === 403) {
          this.$message.error('您没有客服权限')
        } else if (error.response?.status === 401) {
          this.$message.error('登录已过期，请重新登录')
          this.$router.push('/login')
        } else {
          this.$message.error('加载待处理消息失败：' + (error.message || '网络错误'))
        }
      } finally {
        this.loading = false
      }
    },

    /**
     * 刷新待处理消息
     */
    async refreshPendingRooms() {
      await this.loadPendingRooms()
      this.$message.success('刷新成功')
    },

    /**
     * 接管聊天室
     */
    async takeOverRoom(room) {
      if (this.currentRoom && this.currentRoom.roomId === room.roomId) {
        return // 已经选中的房间
      }

      try {
        console.log('🎯 接管聊天室:', room.roomCode)

        // 1. 取消之前的订阅
        if (this.currentRoom) {
          chatService.unsubscribeFromRoom(this.currentRoom.roomCode)
        }

        // 2. 调用接管接口
        const result = await chatService.takeOverRoom(room.roomCode)

        // 3. 设置当前聊天室
        this.currentRoom = {
          ...room,
          roomCode: result.roomCode,
          roomId: result.roomId,
          userId: result.userId
        }
        this.currentMessages = result.messages || []

        // 4. 订阅新聊天室
        await chatService.subscribeToRoom(this.currentRoom.roomCode, this.handleMessage)

        // 5. 加入聊天室
        await chatService.joinRoom(this.currentRoom.roomCode)

        // 6. 从待处理列表中移除
        this.pendingRooms = this.pendingRooms.filter(r => r.roomId !== room.roomId)

        console.log('✅ 聊天室接管成功')
        this.$nextTick(() => {
          this.scrollToBottom()
        })

      } catch (error) {
        console.error('❌ 接管聊天室失败:', error)
        this.$message.error('接管聊天室失败：' + error.message)
      }
    },

    /**
     * 处理接收到的消息
     */
    handleMessage(message) {
      console.log('📨 收到新消息:', message)

      // 过滤掉加入通知
      if (message.type === 'USER_JOINED') {
        return
      }

      this.currentMessages.push(message)

      this.$nextTick(() => {
        this.scrollToBottom()
      })
    },

    /**
     * 发送消息
     */
    async sendMessage() {
      if (!this.inputMessage.trim() || !this.isConnected || !this.currentRoom) {
        return
      }

      const content = this.inputMessage.trim()
      this.inputMessage = ''

      try {
        await chatService.sendMessage(this.currentRoom.roomCode, content)
        console.log('✅ 消息发送成功')
      } catch (error) {
        console.error('❌ 发送消息失败:', error)
        this.$message.error('发送消息失败：' + error.message)
        // 恢复输入内容
        this.inputMessage = content
      }
    },

    /**
     * 处理Enter键
     */
    handleEnterKey(event) {
      if (!event.shiftKey) {
        event.preventDefault()
        this.sendMessage()
      }
    },

    /**
     * 结束对话
     */
    async endChat() {
      if (!this.currentRoom) return

      try {
        await this.$confirm('确定要结束此对话吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        // 取消订阅
        chatService.unsubscribeFromRoom(this.currentRoom.roomCode)

        // 清空当前聊天
        this.currentRoom = null
        this.currentMessages = []

        this.$message.success('对话已结束')
      } catch (error) {
        // 用户取消
      }
    },

    /**
     * 格式化时间
     */
    formatTime(time) {
      if (!time) return ''
      const date = new Date(time)
      const now = new Date()
      const diff = now - date

      if (diff < 60000) { // 1分钟内
        return '刚刚'
      } else if (diff < 3600000) { // 1小时内
        return Math.floor(diff / 60000) + '分钟前'
      } else if (date.toDateString() === now.toDateString()) { // 今天
        return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
      } else {
        return date.toLocaleString('zh-CN', {
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit'
        })
      }
    },

    /**
     * 滚动到底部
     */
    scrollToBottom() {
      if (this.$refs.messagesContainer) {
        this.$refs.messagesContainer.scrollTop = this.$refs.messagesContainer.scrollHeight
      }
    },

    /**
     * 清理资源
     */
    cleanup() {
      if (this.refreshTimer) {
        clearInterval(this.refreshTimer)
        this.refreshTimer = null
      }

      if (this.currentRoom) {
        chatService.unsubscribeFromRoom(this.currentRoom.roomCode)
      }
    }
  }
}
</script>

<style scoped>
.workbench-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
}

.workbench-header {
  background: white;
  padding: 16px 24px;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.workbench-header h2 {
  margin: 0;
  color: #303133;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
}

.connection-status.online {
  color: #67c23a;
}

.connection-status.offline {
  color: #f56c6c;
}

.workbench-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.pending-panel {
  width: 300px;
  background: white;
  border-right: 1px solid #e4e7ed;
  display: flex;
  flex-direction: column;
}

.panel-header {
  padding: 16px;
  border-bottom: 1px solid #e4e7ed;
}

.panel-header h3 {
  margin: 0;
  color: #303133;
  font-size: 16px;
}

.pending-list {
  flex: 1;
  overflow-y: auto;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #909399;
}

.empty-state i {
  font-size: 48px;
  margin-bottom: 12px;
  display: block;
}

.pending-item {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: background-color 0.2s;
}

.pending-item:hover {
  background: #f5f7fa;
}

.pending-item.active {
  background: #ecf5ff;
  border-left: 3px solid #409eff;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  flex-shrink: 0;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.user-avatar i {
  font-size: 20px;
  color: #909399;
}

.room-info {
  flex: 1;
  min-width: 0;
}

.user-name {
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.latest-message {
  color: #606266;
  font-size: 13px;
  margin-bottom: 4px;
  word-break: break-word;
  line-height: 1.4;
}

.message-time {
  color: #909399;
  font-size: 12px;
}

.unread-badge {
  background: #f56c6c;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  flex-shrink: 0;
}

.chat-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: white;
}

.no-chat-selected {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #909399;
}

.no-chat-selected i {
  font-size: 64px;
  margin-bottom: 16px;
}

.chat-area {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.chat-header {
  padding: 16px 24px;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
}

.user-info .user-avatar {
  margin-right: 12px;
}

.user-details .user-name {
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.user-details .room-code {
  color: #909399;
  font-size: 12px;
}

.messages-container {
  flex: 1;
  padding: 16px 24px;
  overflow-y: auto;
  background: #f8f9fa;
}

.message-item {
  display: flex;
  margin-bottom: 16px;
}

.message-item.service-message {
  flex-direction: row-reverse;
}

.message-item .message-avatar {
  width: 32px;
  height: 32px;
  margin: 0 8px;
}

.message-content {
  max-width: 70%;
}

.service-message .message-content {
  text-align: right;
}

.message-info {
  margin-bottom: 4px;
  font-size: 12px;
  color: #909399;
}

.message-text {
  background: white;
  padding: 8px 12px;
  border-radius: 12px;
  word-wrap: break-word;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.service-message .message-text {
  background: #409eff;
  color: white;
}

.input-area {
  border-top: 1px solid #e4e7ed;
  padding: 16px 24px;
  background: white;
}

.input-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.input-actions {
  display: flex;
  justify-content: flex-end;
}

.input-tips {
  margin-top: 8px;
  font-size: 12px;
  text-align: center;
}

.status-tip.success {
  color: #67c23a;
}

.status-tip.error {
  color: #f56c6c;
}
</style>
