<template>
  <div class="test-container">
    <h2>客服工作台API测试</h2>

    <div class="test-section">
      <h3>1. 测试获取待处理消息</h3>
      <el-button @click="testGetPendingRooms" :loading="loading1">测试API</el-button>
      <div class="result-box">
        <h4>API响应:</h4>
        <pre>{{ apiResponse }}</pre>
      </div>
      <div class="result-box">
        <h4>处理后的数据:</h4>
        <pre>{{ processedData }}</pre>
      </div>
    </div>

    <div class="test-section">
      <h3>2. 测试权限检查</h3>
      <el-button @click="testPermission" :loading="loading2">测试权限</el-button>
      <div class="result-box">
        <h4>权限检查结果:</h4>
        <pre>{{ permissionResult }}</pre>
      </div>
    </div>

    <div class="test-section">
      <h3>3. 测试用户信息</h3>
      <el-button @click="testUserInfo" :loading="loading3">测试用户信息</el-button>
      <div class="result-box">
        <h4>用户信息:</h4>
        <pre>{{ userInfoResult }}</pre>
      </div>
    </div>
  </div>
</template>

<script>
import chatService from '@/utils/chatService'
import userPermissionService from '@/utils/userPermission'

export default {
  name: 'CustomerServiceTest',
  data() {
    return {
      loading1: false,
      loading2: false,
      loading3: false,
      apiResponse: null,
      processedData: null,
      permissionResult: null,
      userInfoResult: null
    }
  },
  methods: {
    async testGetPendingRooms() {
      this.loading1 = true
      try {
        console.log('🧪 开始测试获取待处理消息API...')

        // 直接使用axios调用API，获取原始响应
        const token = localStorage.getItem('token')
        console.log('🔑 使用的token:', token)

        const axios = require('axios')
        const rawResponse = await axios.get('/api/chat/service/pending-rooms', {
          headers: { 'Authorization': token }
        })

        console.log('📡 原始HTTP响应:', rawResponse)
        console.log('📋 响应状态:', rawResponse.status)
        console.log('📋 响应数据:', rawResponse.data)

        this.apiResponse = {
          status: rawResponse.status,
          headers: rawResponse.headers,
          data: rawResponse.data
        }

        // 通过chatService调用
        const serviceResponse = await chatService.getPendingRooms()
        console.log('🔧 chatService响应:', serviceResponse)

        // 处理数据
        if (Array.isArray(serviceResponse)) {
          this.processedData = {
            isArray: true,
            length: serviceResponse.length,
            data: serviceResponse
          }
        } else {
          this.processedData = {
            isArray: false,
            type: typeof serviceResponse,
            data: serviceResponse
          }
        }

        console.log('✅ API测试成功')
        this.$message.success('API测试成功')

      } catch (error) {
        console.error('❌ API测试失败:', error)
        this.apiResponse = {
          error: error.message,
          response: error.response?.data,
          status: error.response?.status,
          stack: error.stack
        }
        this.$message.error('API测试失败：' + error.message)
      } finally {
        this.loading1 = false
      }
    },

    async testPermission() {
      this.loading2 = true
      try {
        console.log('🧪 开始测试权限检查...')

        const userType = await userPermissionService.getUserType()
        this.permissionResult = userType

        console.log('✅ 权限检查成功:', userType)
        this.$message.success('权限检查成功')

      } catch (error) {
        console.error('❌ 权限检查失败:', error)
        this.permissionResult = {
          error: error.message
        }
        this.$message.error('权限检查失败：' + error.message)
      } finally {
        this.loading2 = false
      }
    },

    async testUserInfo() {
      this.loading3 = true
      try {
        console.log('🧪 开始测试用户信息...')

        const userInfo = await userPermissionService.getUserInfo()
        this.userInfoResult = userInfo

        console.log('✅ 用户信息获取成功:', userInfo)
        this.$message.success('用户信息获取成功')

      } catch (error) {
        console.error('❌ 用户信息获取失败:', error)
        this.userInfoResult = {
          error: error.message
        }
        this.$message.error('用户信息获取失败：' + error.message)
      } finally {
        this.loading3 = false
      }
    }
  }
}
</script>

<style scoped>
.test-container {
  max-width: 800px;
  margin: 20px auto;
  padding: 20px;
}

.test-section {
  margin-bottom: 30px;
  border: 1px solid #ddd;
  padding: 20px;
  border-radius: 5px;
}

.result-box {
  margin-top: 15px;
  padding: 10px;
  background-color: #f5f5f5;
  border-radius: 3px;
}

.result-box pre {
  white-space: pre-wrap;
  word-wrap: break-word;
  max-height: 300px;
  overflow-y: auto;
}

h2 {
  color: #333;
  text-align: center;
}

h3 {
  color: #666;
  margin-bottom: 15px;
}

h4 {
  color: #888;
  margin-bottom: 10px;
}
</style>
