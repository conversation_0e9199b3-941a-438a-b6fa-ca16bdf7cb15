<template>
  <div class="login-container" style="background-image: url('./assets/background.jpg'); background-size: cover; background-position: center;">
    <h2>登录</h2>
    <form @submit.prevent="handleLogin">
      <div class="form-group">
        <label for="username">用户名:</label>
        <input type="text" id="username" v-model="username" required>
        <small class="hint">请输入您的用户名</small>
      </div>
      <div class="form-group">
        <label for="password">密码:</label>
        <input type="password" id="password" v-model="password" required>
        <small class="hint">密码长度至少为6位</small>
      </div>
      <button type="submit" class="login-button">登录</button>
    </form>
    <p v-if="errorMessage" class="error-message">{{ errorMessage }}</p>
    <p>还没有账号? <router-link to="/register">注册</router-link></p>
  </div>
</template>

<script>
import request from '@/utils/request'

export default {
  data () {
    return {
      username: '',
      password: '',
      errorMessage: ''
    }
  },
  methods: {
    async handleLogin () {
      if (this.username && this.password) {
        try {
          console.log('🔐 开始用户登录...')

          // 调用登录接口
          const response = await request.post('/api/user/login', {
            userName: this.username,
            password: this.password
          })

          console.log('🔐 登录接口响应:', response)

          if (response.data.code === '200') {
            // 保存token到本地存储
            const token = response.data.data.token
            localStorage.setItem('token', token)
            console.log('✅ Token已保存:', token.substring(0, 20) + '...')

            // 获取用户信息
            await this.getUserInfo()

            // 跳转到原来要访问的页面或首页
            const redirect = this.$route.query.redirect || '/index'
            this.$router.push(redirect)
          } else {
            this.errorMessage = '登录失败：' + response.data.message
          }
        } catch (error) {
          console.error('❌ 登录失败:', error)
          this.errorMessage = '登录失败，请检查网络连接'
        }
      } else {
        this.errorMessage = '请输入用户名和密码'
      }
    },

    async getUserInfo() {
      try {
        console.log('👤 获取用户信息...')

        const response = await request.get('/api/user/getInfo')
        console.log('👤 用户信息响应:', response)

        if (response.data.code === '200') {
          const userInfo = response.data.data
          // 保存用户信息到本地存储，包括缓存时间戳
          localStorage.setItem('userInfo', JSON.stringify(userInfo))
          localStorage.setItem('username', userInfo.userName || userInfo.nickName || '用户')
          localStorage.setItem('userInfoCacheTime', Date.now().toString())
          console.log('✅ 用户信息已保存:', userInfo)
        } else {
          console.warn('⚠️ 获取用户信息失败:', response.data.message)
        }
      } catch (error) {
        console.error('❌ 获取用户信息错误:', error)
        // 即使获取用户信息失败，也不阻止登录流程
      }
    }
  }
}
</script>

<style scoped>
.login-container {
  max-width: 300px;
  margin: 50px auto;
  padding: 20px;
  border: 1px solid #ccc;
  border-radius: 5px;
  text-align: center;
  backdrop-filter: blur(5px);
}
.form-group {
  margin-bottom: 15px;
  text-align: left;
}
label {
  display: block;
  margin-bottom: 5px;
}
input {
  width: 100%;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 3px;
}
.hint {
  color: #666;
  font-size: 12px;
}
.login-button {
  width: 100%;
  padding: 10px;
  background-color: #2196F3;
  color: white;
  border: none;
  border-radius: 3px;
  cursor: pointer;
  transition: background-color 0.3s;
}
.login-button:hover {
  background-color: #1976D2;
}
.error-message {
  color: red;
  margin-top: 10px;
}
</style>
