# 聊天系统前端集成说明

## 📋 概述

本文档说明了如何在 minio-vue 前端项目中使用已集成的聊天系统功能。聊天系统已从演示模式改为真实的 API 调用，支持用户与客服之间的实时通信。

## 🎯 主要功能

### 用户端功能
- **快速开始聊天**: 用户点击"在线客服"按钮即可快速启动聊天
- **实时消息**: 基于 WebSocket 的实时消息收发
- **聊天历史**: 自动加载历史聊天记录
- **状态显示**: 显示客服在线状态和输入状态

### 客服端功能
- **待处理消息列表**: 查看所有等待处理的用户消息
- **聊天室接管**: 客服可以接管用户的聊天室
- **实时通信**: 与用户进行实时对话
- **多聊天室管理**: 支持客服同时处理多个聊天室

## 🚀 快速开始

### 1. 启动后端服务
确保后端 Spring Boot 应用已启动并运行在 `http://localhost:8080`

### 2. 启动前端项目
```bash
cd minio-vue
npm run dev
```

### 3. 访问系统
- 前端地址: `http://localhost:3000`
- 登录系统后即可使用聊天功能

## 🔧 使用方法

### 普通用户使用聊天功能

1. **登录系统**
   - 访问 `http://localhost:3000`
   - 使用有效的用户账号登录

2. **启动聊天**
   - 在首页点击"在线客服"按钮
   - 系统会自动创建聊天室并连接 WebSocket
   - 开始与客服对话

3. **发送消息**
   - 在聊天窗口底部输入框输入消息
   - 按回车键或点击发送按钮发送消息

### 客服用户使用工作台

1. **客服登录**
   - 使用具有客服权限的账号登录
   - 在首页会显示"客服工作台"按钮

2. **打开工作台**
   - 点击"客服工作台"按钮
   - 或直接访问 `/customer-service-workbench`

3. **处理用户消息**
   - 查看待处理消息列表
   - 点击用户消息接管聊天室
   - 与用户进行实时对话

## 🧪 测试功能

### 集成测试页面
访问 `/chat-integration-test` 可以进行完整的功能测试：

- **连接状态检查**: 查看 WebSocket 连接状态
- **用户端测试**: 测试快速开始聊天、发送消息等功能
- **客服端测试**: 测试获取待处理消息、接管聊天室等功能
- **实时结果**: 查看每个测试的详细结果和数据

### 测试步骤
1. 登录系统
2. 访问聊天集成测试页面
3. 按顺序执行各项测试
4. 查看测试结果和连接状态

## 📁 文件结构

```
minio-vue/src/
├── components/
│   ├── CustomerService.vue      # 用户端聊天组件
│   ├── AgentWorkspace.vue       # 客服工作台组件
│   ├── ChatIntegrationTest.vue  # 集成测试组件
│   └── Index.vue               # 首页（包含聊天入口）
├── utils/
│   └── chatService.js          # 聊天服务管理器
└── router/
    └── index.js                # 路由配置
```

## 🔌 API 接口

### 用户端接口
- `POST /api/chat/quick-start` - 快速开始聊天
- `GET /api/chat/history/{roomCode}` - 获取聊天历史

### 客服端接口
- `GET /api/chat/service/pending-rooms` - 获取待处理消息列表
- `POST /api/chat/service/take-over/{roomCode}` - 接管聊天室
- `GET /api/chat/service/rooms` - 获取客服已分配聊天室

### WebSocket 通信
- **连接端点**: `ws://localhost:8080/ws/chat`
- **协议**: STOMP over SockJS
- **认证**: Headers 中携带 JWT Token
- **订阅**: `/topic/chat/{roomCode}`
- **发送**: `/app/chat.sendMessage/{roomCode}`
- **加入**: `/app/chat.joinRoom/{roomCode}`

## ⚠️ 注意事项

### 权限控制
- 普通用户只能使用聊天功能
- 客服用户可以访问工作台
- 系统会自动检查用户权限

### Token 认证
- 所有 API 请求都需要携带 JWT Token
- Token 格式：`Authorization: {JWT_TOKEN}`（无需 Bearer 前缀）
- Token 过期时会自动跳转到登录页

### 错误处理
- 网络连接失败时会显示相应错误信息
- WebSocket 连接断开时会自动尝试重连
- API 调用失败时会显示具体错误原因

## 🔍 故障排除

### 常见问题

1. **WebSocket 连接失败**
   - 检查后端服务是否启动
   - 确认 WebSocket 端点配置正确
   - 检查 Token 是否有效

2. **聊天室创建失败**
   - 确认用户已正确登录
   - 检查后端 API 是否正常
   - 查看浏览器控制台错误信息

3. **客服工作台无法访问**
   - 确认当前用户具有客服权限
   - 检查路由配置是否正确
   - 验证权限检查逻辑

### 调试方法
- 打开浏览器开发者工具查看控制台日志
- 使用集成测试页面检查各项功能
- 查看 Network 面板确认 API 调用状态
- 检查 WebSocket 连接状态

## 📞 技术支持

如果遇到问题，请：
1. 查看浏览器控制台错误信息
2. 检查后端服务日志
3. 使用集成测试页面进行功能验证
4. 参考快速聊天集成指南文档

---

**注意**: 本聊天系统已完全移除演示模式，所有功能都基于真实的后端 API 调用。确保后端服务正常运行是使用聊天功能的前提。
