# 聊天系统故障排除指南

## 🚨 常见问题及解决方案

### 1. 点击"在线客服"按钮没有反应

#### 可能原因：
- 后端服务未启动
- 网络连接问题
- 用户未登录或Token过期
- API接口配置错误

#### 解决步骤：

**第一步：检查后端服务**
```bash
# 确认后端Spring Boot应用是否运行
curl http://localhost:8080/api/test
```

**第二步：检查用户登录状态**
1. 打开浏览器开发者工具 (F12)
2. 查看 Application -> Local Storage
3. 确认是否存在 `token` 和 `userInfo`

**第三步：使用连接诊断工具**
1. 访问 `/chat-integration-test` 页面
2. 点击 "🔍 连接诊断" 按钮
3. 查看详细的测试结果

**第四步：查看控制台错误**
1. 打开浏览器开发者工具 (F12)
2. 查看 Console 面板的错误信息
3. 查看 Network 面板的请求状态

### 2. 聊天界面弹出但无法发送消息

#### 可能原因：
- WebSocket连接失败
- 聊天室创建失败
- 消息格式错误

#### 解决步骤：

**检查WebSocket连接**
```javascript
// 在浏览器控制台执行
ChatService.getConnectionStatus()
```

**重新连接WebSocket**
```javascript
// 在浏览器控制台执行
ChatService.disconnect()
// 然后重新打开聊天窗口
```

### 3. 客服工作台无法访问

#### 可能原因：
- 用户没有客服权限
- 路由配置错误
- 权限验证失败

#### 解决步骤：

**检查用户权限**
```javascript
// 在浏览器控制台执行
JSON.parse(localStorage.getItem('userInfo'))
// 查看 isCustomerService 字段
```

**手动设置客服权限（测试用）**
```javascript
// 在浏览器控制台执行
const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')
userInfo.isCustomerService = true
localStorage.setItem('userInfo', JSON.stringify(userInfo))
// 然后刷新页面
```

### 4. API调用失败

#### 常见错误码：

**401 Unauthorized**
- 原因：Token无效或过期
- 解决：重新登录获取新Token

**403 Forbidden**
- 原因：权限不足
- 解决：检查用户权限设置

**404 Not Found**
- 原因：API接口不存在
- 解决：检查后端服务和接口配置

**500 Internal Server Error**
- 原因：后端服务内部错误
- 解决：查看后端服务日志

**ECONNREFUSED**
- 原因：无法连接到后端服务
- 解决：确认后端服务已启动

## 🔧 调试工具

### 1. 连接诊断工具
访问 `/chat-integration-test` 页面，使用内置的诊断工具：
- 连接状态检查
- API接口测试
- WebSocket连接测试
- 完整功能验证

### 2. 浏览器开发者工具
- **Console**: 查看错误日志和调试信息
- **Network**: 查看API请求和响应
- **Application**: 查看本地存储数据
- **WebSocket**: 查看WebSocket连接状态

### 3. 手动测试命令

**测试基础连接**
```bash
curl -X GET http://localhost:8080/api/test
```

**测试用户认证**
```bash
curl -X GET http://localhost:8080/api/user/info \
  -H "Authorization: YOUR_TOKEN_HERE"
```

**测试聊天API**
```bash
curl -X POST http://localhost:8080/api/chat/quick-start \
  -H "Authorization: YOUR_TOKEN_HERE" \
  -H "Content-Type: application/json"
```

## 🚀 快速修复

### 重置聊天状态
```javascript
// 在浏览器控制台执行
ChatService.disconnect()
localStorage.removeItem('currentRoomCode')
// 然后重新打开聊天窗口
```

### 清除缓存数据
```javascript
// 在浏览器控制台执行
localStorage.clear()
sessionStorage.clear()
// 然后重新登录
```

### 强制重新连接
```javascript
// 在浏览器控制台执行
location.reload()
```

## 📞 获取帮助

### 1. 查看日志
- 前端：浏览器开发者工具 Console
- 后端：Spring Boot 应用日志

### 2. 使用测试工具
- 访问 `/chat-integration-test` 进行完整测试
- 使用连接诊断功能定位问题

### 3. 检查配置
- 确认后端服务地址：`http://localhost:8080`
- 确认WebSocket地址：`ws://localhost:8080/ws/chat`
- 确认代理配置正确

### 4. 常用检查清单
- [ ] 后端服务已启动
- [ ] 用户已正确登录
- [ ] Token有效且未过期
- [ ] 网络连接正常
- [ ] 浏览器支持WebSocket
- [ ] 没有防火墙阻止连接

## 🔍 详细错误分析

### 网络错误
```
ERR_CONNECTION_REFUSED
ERR_NETWORK_CHANGED
ERR_INTERNET_DISCONNECTED
```
**解决方案**: 检查网络连接和后端服务状态

### 认证错误
```
401 Unauthorized
403 Forbidden
Token expired
```
**解决方案**: 重新登录或检查用户权限

### WebSocket错误
```
WebSocket connection failed
Connection timeout
Handshake failed
```
**解决方案**: 检查WebSocket服务配置和网络连接

---

**提示**: 如果问题仍然存在，请使用连接诊断工具获取详细的错误信息，这将帮助快速定位和解决问题。
